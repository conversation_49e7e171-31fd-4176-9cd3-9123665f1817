import '../services/aws_auth_service.dart' as aws;

class UserModel {
  final String id;
  final String email;
  final String? phone;
  final DateTime? emailConfirmedAt;
  final DateTime? phoneConfirmedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? userMetadata;
  final Map<String, dynamic>? appMetadata;

  UserModel({
    required this.id,
    required this.email,
    this.phone,
    this.emailConfirmedAt,
    this.phoneConfirmedAt,
    required this.createdAt,
    required this.updatedAt,
    this.userMetadata,
    this.appMetadata,
  });

  /// Create UserModel from AWS User
  factory UserModel.fromAwsUser(aws.AwsUser user) {
    return UserModel(
      id: user.id,
      email: user.email,
      phone: null, // AWS Cognito phone not implemented yet
      emailConfirmedAt: user.isVerified ? DateTime.now() : null,
      phoneConfirmedAt: null,
      createdAt: DateTime.now(), // AWS user doesn't have creation date
      updatedAt: DateTime.now(),
      userMetadata: {
        'username': user.username,
        'displayName': user.displayName,
        'avatarUrl': user.avatarUrl,
      },
      appMetadata: {},
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'phone': phone,
      'emailConfirmedAt': emailConfirmedAt?.toIso8601String(),
      'phoneConfirmedAt': phoneConfirmedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'userMetadata': userMetadata,
      'appMetadata': appMetadata,
    };
  }

  /// Create from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      email: json['email'],
      phone: json['phone'],
      emailConfirmedAt:
          json['emailConfirmedAt'] != null
              ? DateTime.parse(json['emailConfirmedAt'])
              : null,
      phoneConfirmedAt:
          json['phoneConfirmedAt'] != null
              ? DateTime.parse(json['phoneConfirmedAt'])
              : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      userMetadata: json['userMetadata'],
      appMetadata: json['appMetadata'],
    );
  }

  /// Check if email is confirmed
  bool get isEmailConfirmed => emailConfirmedAt != null;

  /// Check if phone is confirmed
  bool get isPhoneConfirmed => phoneConfirmedAt != null;

  /// Get display name from metadata or email
  String get displayName {
    if (userMetadata != null) {
      final name = userMetadata!['fullName'] ?? userMetadata!['name'];
      if (name != null && name.toString().isNotEmpty) {
        return name.toString();
      }
    }
    return email.split('@').first;
  }

  /// Copy with new values
  UserModel copyWith({
    String? id,
    String? email,
    String? phone,
    DateTime? emailConfirmedAt,
    DateTime? phoneConfirmedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? userMetadata,
    Map<String, dynamic>? appMetadata,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      emailConfirmedAt: emailConfirmedAt ?? this.emailConfirmedAt,
      phoneConfirmedAt: phoneConfirmedAt ?? this.phoneConfirmedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userMetadata: userMetadata ?? this.userMetadata,
      appMetadata: appMetadata ?? this.appMetadata,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, displayName: $displayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
