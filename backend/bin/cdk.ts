#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import { GameFlexBackendStack } from '../lib/gameflex-backend-stack';

const app = new cdk.App();

// Get environment from context or environment variables
const environment = app.node.tryGetContext('environment') || process.env.ENVIRONMENT || 'development';
const projectName = app.node.tryGetContext('projectName') || process.env.PROJECT_NAME || 'gameflex';

// Environment-specific configuration
const envConfig = {
  development: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION || 'us-west-2',
    domainName: '',
    certificateArn: '',
    r2BucketName: 'gameflex-development',
    r2PublicUrl: 'https://pub-34709f09e8384ef1a67928492571c01d.r2.dev'
  },
  staging: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION || 'us-west-2',
    domainName: 'staging.api.gameflex.io',
    certificateArn: 'arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662',
    r2BucketName: 'gameflex-staging',
    r2PublicUrl: 'https://staging.media.gameflex.io'
  },
  production: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION || 'us-west-2',
    domainName: 'api.gameflex.io',
    certificateArn: 'arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662',
    r2BucketName: 'gameflex-production',
    r2PublicUrl: 'https://media.gameflex.io'
  }
};

const config = envConfig[environment as keyof typeof envConfig] || envConfig.development;

new GameFlexBackendStack(app, `${projectName}-${environment}`, {
  env: {
    account: config.account,
    region: config.region,
  },
  environment,
  projectName,
  domainName: config.domainName,
  certificateArn: config.certificateArn,
  r2BucketName: config.r2BucketName,
  r2PublicUrl: config.r2PublicUrl,
});