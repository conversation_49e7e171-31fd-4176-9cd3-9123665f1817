#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const cdk = __importStar(require("aws-cdk-lib"));
const gameflex_backend_stack_1 = require("../lib/gameflex-backend-stack");
const app = new cdk.App();
// Get environment from context or environment variables
const environment = app.node.tryGetContext('environment') || process.env.ENVIRONMENT || 'development';
const projectName = app.node.tryGetContext('projectName') || process.env.PROJECT_NAME || 'gameflex';
// Environment-specific configuration
const envConfig = {
    development: {
        account: process.env.CDK_DEFAULT_ACCOUNT,
        region: process.env.CDK_DEFAULT_REGION || 'us-west-2',
        domainName: '',
        certificateArn: '',
        r2BucketName: 'gameflex-development',
        r2PublicUrl: 'https://pub-34709f09e8384ef1a67928492571c01d.r2.dev'
    },
    staging: {
        account: process.env.CDK_DEFAULT_ACCOUNT,
        region: process.env.CDK_DEFAULT_REGION || 'us-west-2',
        domainName: 'staging.api.gameflex.io',
        certificateArn: 'arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662',
        r2BucketName: 'gameflex-staging',
        r2PublicUrl: 'https://staging.media.gameflex.io'
    },
    production: {
        account: process.env.CDK_DEFAULT_ACCOUNT,
        region: process.env.CDK_DEFAULT_REGION || 'us-west-2',
        domainName: 'api.gameflex.io',
        certificateArn: 'arn:aws:acm:us-east-1:************:certificate/9c6c8946-2c19-4efa-ab52-c44a42236662',
        r2BucketName: 'gameflex-production',
        r2PublicUrl: 'https://media.gameflex.io'
    }
};
const config = envConfig[environment] || envConfig.development;
new gameflex_backend_stack_1.GameFlexBackendStack(app, `${projectName}-${environment}`, {
    env: {
        account: config.account,
        region: config.region,
    },
    environment,
    projectName,
    domainName: config.domainName,
    certificateArn: config.certificateArn,
    r2BucketName: config.r2BucketName,
    r2PublicUrl: config.r2PublicUrl,
});
//# sourceMappingURL=data:application/json;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************