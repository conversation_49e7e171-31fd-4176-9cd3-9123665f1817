{"name": "gameflex-cdk-backend", "version": "1.0.0", "description": "GameFlex Backend using AWS CDK for infrastructure and deployment", "main": "bin/cdk.js", "bin": {"cdk": "bin/cdk.js"}, "scripts": {"start": "./start.sh", "stop": "./stop.sh", "install-deps": "./scripts/install-dependencies.sh", "build": "tsc", "watch": "tsc -w", "deploy": "./deploy.sh", "deploy:dev": "./deploy.sh development", "deploy:staging": "./deploy-staging.sh", "deploy:production": "./deploy-production.sh", "synth": "cdk synth", "diff": "cdk diff", "destroy": "cdk destroy", "cdk": "cdk", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e", "test:run": "./scripts/run-tests.sh"}, "keywords": ["aws", "cdk", "serverless", "lambda", "api-gateway", "dynamodb", "cognito", "cloudflare-r2", "gameflex"], "author": "GameFlex Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@types/aws-lambda": "^8.10.150", "@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk": "2.1022.0", "axios": "^1.6.0", "babel-jest": "^29.7.0", "dotenv": "^17.2.0", "esbuild": "^0.25.8", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.848.0", "@aws-sdk/client-dynamodb": "^3.848.0", "@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/client-secrets-manager": "^3.848.0", "@aws-sdk/lib-dynamodb": "^3.850.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "aws-cdk-lib": "2.206.0", "aws-sdk": "^2.1499.0", "constructs": "^10.0.0", "uuid": "^9.0.1"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/gameflex/gameflex-backend"}, "bugs": {"url": "https://github.com/gameflex/gameflex-backend/issues"}, "homepage": "https://github.com/gameflex/gameflex-backend#readme"}