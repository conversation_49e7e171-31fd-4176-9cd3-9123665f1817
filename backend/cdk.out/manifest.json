{"version": "45.0.0", "artifacts": {"gameflex-development.assets": {"type": "cdk:asset-manifest", "properties": {"file": "gameflex-development.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "gameflex-development": {"type": "aws:cloudformation:stack", "environment": "aws://405316604661/us-west-2", "properties": {"templateFile": "gameflex-development.template.json", "terminationProtection": false, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::405316604661:role/cdk-hnb659fds-deploy-role-405316604661-us-west-2", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::405316604661:role/cdk-hnb659fds-cfn-exec-role-405316604661-us-west-2", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-405316604661-us-west-2/a300213c4631e084a4b9d5d0bbc0a378af42055b6cd551665856954eb8ee7629.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["gameflex-development.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::405316604661:role/cdk-hnb659fds-lookup-role-405316604661-us-west-2", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["gameflex-development.assets"], "metadata": {"/gameflex-development/UserPool": [{"type": "aws:cdk:analytics:construct", "data": {"userPoolName": "*", "autoVerify": {"email": true}, "signInAliases": {"email": true}, "passwordPolicy": {"minLength": "*", "requireUppercase": true, "requireLowercase": true, "requireDigits": true, "requireSymbols": false}, "deletionProtection": false, "removalPolicy": "destroy"}}], "/gameflex-development/UserPool/Resource": [{"type": "aws:cdk:logicalId", "data": "UserPool6BA7E5F2"}], "/gameflex-development/UserPoolClient": [{"type": "aws:cdk:analytics:construct", "data": {"userPool": "*", "userPoolClientName": "*", "generateSecret": false, "authFlows": {"adminUserPassword": true, "userPassword": true, "userSrp": true}, "refreshTokenValidity": "*", "accessTokenValidity": "*", "idTokenValidity": "*"}}], "/gameflex-development/UserPoolClient/Resource": [{"type": "aws:cdk:logicalId", "data": "UserPoolClient2F5918F7"}], "/gameflex-development/PostsTable": [{"type": "aws:cdk:analytics:construct", "data": {"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}}, {"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "PostsTableC82B36F0"}}], "/gameflex-development/PostsTable/Resource": [{"type": "aws:cdk:logicalId", "data": "PostsTableC82B36F0"}], "/gameflex-development/PostsTable/ScalingRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/MediaTable": [{"type": "aws:cdk:analytics:construct", "data": {"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}}, {"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "MediaTableCFC93525"}}], "/gameflex-development/MediaTable/Resource": [{"type": "aws:cdk:logicalId", "data": "MediaTableCFC93525"}], "/gameflex-development/MediaTable/ScalingRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/UserProfilesTable": [{"type": "aws:cdk:analytics:construct", "data": {"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}}, {"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "UserProfilesTableF49D814C"}}], "/gameflex-development/UserProfilesTable/Resource": [{"type": "aws:cdk:logicalId", "data": "UserProfilesTableF49D814C"}], "/gameflex-development/UserProfilesTable/ScalingRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/CommentsTable": [{"type": "aws:cdk:analytics:construct", "data": {"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}}, {"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "CommentsTableBBDBF0A8"}}, {"type": "aws:cdk:analytics:method", "data": {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}}], "/gameflex-development/CommentsTable/Resource": [{"type": "aws:cdk:logicalId", "data": "CommentsTableBBDBF0A8"}], "/gameflex-development/CommentsTable/ScalingRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/LikesTable": [{"type": "aws:cdk:analytics:construct", "data": {"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}, "sortKey": {"name": "*", "type": "S"}}}, {"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "LikesTable9511B0A4"}}, {"type": "aws:cdk:analytics:method", "data": {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}}], "/gameflex-development/LikesTable/Resource": [{"type": "aws:cdk:logicalId", "data": "LikesTable9511B0A4"}], "/gameflex-development/LikesTable/ScalingRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/FollowsTable": [{"type": "aws:cdk:analytics:construct", "data": {"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}, "sortKey": {"name": "*", "type": "S"}}}, {"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "FollowsTable7B81FE10"}}], "/gameflex-development/FollowsTable/Resource": [{"type": "aws:cdk:logicalId", "data": "FollowsTable7B81FE10"}], "/gameflex-development/FollowsTable/ScalingRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/ChannelsTable": [{"type": "aws:cdk:analytics:construct", "data": {"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}}, {"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "ChannelsTable6C883730"}}, {"type": "aws:cdk:analytics:method", "data": {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}}], "/gameflex-development/ChannelsTable/Resource": [{"type": "aws:cdk:logicalId", "data": "ChannelsTable6C883730"}], "/gameflex-development/ChannelsTable/ScalingRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/ChannelMembersTable": [{"type": "aws:cdk:analytics:construct", "data": {"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}, "sortKey": {"name": "*", "type": "S"}}}, {"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "ChannelMembersTable93E36D7C"}}, {"type": "aws:cdk:analytics:method", "data": {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}}], "/gameflex-development/ChannelMembersTable/Resource": [{"type": "aws:cdk:logicalId", "data": "ChannelMembersTable93E36D7C"}], "/gameflex-development/ChannelMembersTable/ScalingRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/ReflexesTable": [{"type": "aws:cdk:analytics:construct", "data": {"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}}, {"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "ReflexesTableD80A0AD3"}}, {"type": "aws:cdk:analytics:method", "data": {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}}, {"type": "aws:cdk:analytics:method", "data": {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}}], "/gameflex-development/ReflexesTable/Resource": [{"type": "aws:cdk:logicalId", "data": "ReflexesTableD80A0AD3"}], "/gameflex-development/ReflexesTable/ScalingRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/UsersTable": [{"type": "aws:cdk:analytics:construct", "data": {"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}}, {"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "UsersTable9725E9C8"}}, {"type": "aws:cdk:analytics:method", "data": {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}}, {"type": "aws:cdk:analytics:method", "data": {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}}, {"type": "aws:cdk:analytics:method", "data": {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}}], "/gameflex-development/UsersTable/Resource": [{"type": "aws:cdk:logicalId", "data": "UsersTable9725E9C8"}], "/gameflex-development/UsersTable/ScalingRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/AuthorizerFunction": [{"type": "aws:cdk:analytics:construct", "data": {"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}], "/gameflex-development/AuthorizerFunction/ServiceRole": [{"type": "aws:cdk:analytics:construct", "data": {"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}], "/gameflex-development/AuthorizerFunction/ServiceRole/ImportServiceRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/AuthorizerFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "AuthorizerFunctionServiceRole5B2A061B"}], "/gameflex-development/AuthorizerFunction/ServiceRole/DefaultPolicy": [{"type": "aws:cdk:analytics:construct", "data": "*"}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}], "/gameflex-development/AuthorizerFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "AuthorizerFunctionServiceRoleDefaultPolicy2C75A4EA"}], "/gameflex-development/AuthorizerFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "AuthorizerFunctionB4DBAA43"}], "/gameflex-development/AuthorizerFunction/LogGroup": [{"type": "aws:cdk:analytics:construct", "data": {"logGroupName": "*"}}], "/gameflex-development/AuthorizerFunction/LogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "AuthorizerFunctionLogGroupB47801AB"}], "/gameflex-development/AuthorizerFunction/gameflexdevelopmentDefaultAuthorizerCA84F39E:Permissions": [{"type": "aws:cdk:logicalId", "data": "AuthorizerFunctiongameflexdevelopmentDefaultAuthorizerCA84F39EPermissionsCB2412EE"}], "/gameflex-development/AuthFunction": [{"type": "aws:cdk:analytics:construct", "data": {"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}], "/gameflex-development/AuthFunction/ServiceRole": [{"type": "aws:cdk:analytics:construct", "data": {"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}], "/gameflex-development/AuthFunction/ServiceRole/ImportServiceRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/AuthFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "AuthFunctionServiceRole87A7A68C"}], "/gameflex-development/AuthFunction/ServiceRole/DefaultPolicy": [{"type": "aws:cdk:analytics:construct", "data": "*"}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}], "/gameflex-development/AuthFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "AuthFunctionServiceRoleDefaultPolicy4198AD5B"}], "/gameflex-development/AuthFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "AuthFunctionA1CD5E0F"}], "/gameflex-development/AuthFunction/LogGroup": [{"type": "aws:cdk:analytics:construct", "data": {"logGroupName": "*"}}], "/gameflex-development/AuthFunction/LogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "AuthFunctionLogGroupF2D1C9DD"}], "/gameflex-development/PostsFunction": [{"type": "aws:cdk:analytics:construct", "data": {"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}], "/gameflex-development/PostsFunction/ServiceRole": [{"type": "aws:cdk:analytics:construct", "data": {"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}], "/gameflex-development/PostsFunction/ServiceRole/ImportServiceRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/PostsFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "PostsFunctionServiceRoleF2DB8406"}], "/gameflex-development/PostsFunction/ServiceRole/DefaultPolicy": [{"type": "aws:cdk:analytics:construct", "data": "*"}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}], "/gameflex-development/PostsFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "PostsFunctionServiceRoleDefaultPolicyACA3B729"}], "/gameflex-development/PostsFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "PostsFunction3DC4AEDB"}], "/gameflex-development/PostsFunction/LogGroup": [{"type": "aws:cdk:analytics:construct", "data": {"logGroupName": "*"}}], "/gameflex-development/PostsFunction/LogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "PostsFunctionLogGroupC6FCA2A2"}], "/gameflex-development/MediaFunction": [{"type": "aws:cdk:analytics:construct", "data": {"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}], "/gameflex-development/MediaFunction/ServiceRole": [{"type": "aws:cdk:analytics:construct", "data": {"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}], "/gameflex-development/MediaFunction/ServiceRole/ImportServiceRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/MediaFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "MediaFunctionServiceRole8E5A8C78"}], "/gameflex-development/MediaFunction/ServiceRole/DefaultPolicy": [{"type": "aws:cdk:analytics:construct", "data": "*"}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}], "/gameflex-development/MediaFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "MediaFunctionServiceRoleDefaultPolicy8C41214C"}], "/gameflex-development/MediaFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "MediaFunctionD33228E9"}], "/gameflex-development/MediaFunction/LogGroup": [{"type": "aws:cdk:analytics:construct", "data": {"logGroupName": "*"}}], "/gameflex-development/MediaFunction/LogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "MediaFunctionLogGroup944AF0E8"}], "/gameflex-development/UsersFunction": [{"type": "aws:cdk:analytics:construct", "data": {"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}], "/gameflex-development/UsersFunction/ServiceRole": [{"type": "aws:cdk:analytics:construct", "data": {"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}], "/gameflex-development/UsersFunction/ServiceRole/ImportServiceRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/UsersFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "UsersFunctionServiceRoleAA0FFEE7"}], "/gameflex-development/UsersFunction/ServiceRole/DefaultPolicy": [{"type": "aws:cdk:analytics:construct", "data": "*"}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}], "/gameflex-development/UsersFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "UsersFunctionServiceRoleDefaultPolicyDAA4211F"}], "/gameflex-development/UsersFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "UsersFunction1976AE51"}], "/gameflex-development/UsersFunction/LogGroup": [{"type": "aws:cdk:analytics:construct", "data": {"logGroupName": "*"}}], "/gameflex-development/UsersFunction/LogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "UsersFunctionLogGroup08AD9B83"}], "/gameflex-development/HealthFunction": [{"type": "aws:cdk:analytics:construct", "data": {"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}], "/gameflex-development/HealthFunction/ServiceRole": [{"type": "aws:cdk:analytics:construct", "data": {"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}], "/gameflex-development/HealthFunction/ServiceRole/ImportServiceRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/HealthFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "HealthFunctionServiceRole04552894"}], "/gameflex-development/HealthFunction/ServiceRole/DefaultPolicy": [{"type": "aws:cdk:analytics:construct", "data": "*"}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}], "/gameflex-development/HealthFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "HealthFunctionServiceRoleDefaultPolicy57228674"}], "/gameflex-development/HealthFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "HealthFunction19D7724A"}], "/gameflex-development/HealthFunction/LogGroup": [{"type": "aws:cdk:analytics:construct", "data": {"logGroupName": "*"}}], "/gameflex-development/HealthFunction/LogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "HealthFunctionLogGroup8350B222"}], "/gameflex-development/ReflexesFunction": [{"type": "aws:cdk:analytics:construct", "data": {"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}], "/gameflex-development/ReflexesFunction/ServiceRole": [{"type": "aws:cdk:analytics:construct", "data": {"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}], "/gameflex-development/ReflexesFunction/ServiceRole/ImportServiceRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/ReflexesFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "ReflexesFunctionServiceRole768786C8"}], "/gameflex-development/ReflexesFunction/ServiceRole/DefaultPolicy": [{"type": "aws:cdk:analytics:construct", "data": "*"}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}], "/gameflex-development/ReflexesFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "ReflexesFunctionServiceRoleDefaultPolicyEBCECFAA"}], "/gameflex-development/ReflexesFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "ReflexesFunction5F91D3B2"}], "/gameflex-development/ReflexesFunction/LogGroup": [{"type": "aws:cdk:analytics:construct", "data": {"logGroupName": "*"}}], "/gameflex-development/ReflexesFunction/LogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ReflexesFunctionLogGroupF5ABF65B"}], "/gameflex-development/ChannelsFunction": [{"type": "aws:cdk:analytics:construct", "data": {"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addEnvironment": ["*", "*"]}}], "/gameflex-development/ChannelsFunction/ServiceRole": [{"type": "aws:cdk:analytics:construct", "data": {"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}], "/gameflex-development/ChannelsFunction/ServiceRole/ImportServiceRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/ChannelsFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "ChannelsFunctionServiceRole0A782FB9"}], "/gameflex-development/ChannelsFunction/ServiceRole/DefaultPolicy": [{"type": "aws:cdk:analytics:construct", "data": "*"}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}], "/gameflex-development/ChannelsFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "ChannelsFunctionServiceRoleDefaultPolicy6E3FD81C"}], "/gameflex-development/ChannelsFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "ChannelsFunction9B818B1B"}], "/gameflex-development/ChannelsFunction/LogGroup": [{"type": "aws:cdk:analytics:construct", "data": {"logGroupName": "*"}}], "/gameflex-development/ChannelsFunction/LogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ChannelsFunctionLogGroupE884AE8C"}], "/gameflex-development/GameFlexApi": [{"type": "aws:cdk:analytics:construct", "data": {"restApiName": "*", "description": "*", "defaultCorsPreflightOptions": {"allowOrigins": "*", "allowMethods": "*", "allowHeaders": "*"}, "deployOptions": {"stageName": "*", "throttlingRateLimit": "*", "throttlingBurstLimit": "*"}}}], "/gameflex-development/GameFlexApi/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiC965E6C8"}], "/gameflex-development/GameFlexApi/Deployment": [{"type": "aws:cdk:analytics:construct", "data": {"description": "*", "api": "*", "retainDeployments": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToLogicalId": [{}]}}], "/gameflex-development/GameFlexApi/Deployment/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiDeployment8960269Dd23fb76cc0ff6720e32e2ac231ca298a"}], "/gameflex-development/GameFlexApi/DeploymentStage.v1": [{"type": "aws:cdk:analytics:construct", "data": {"deployment": "*", "stageName": "*", "throttlingRateLimit": "*", "throttlingBurstLimit": "*"}}], "/gameflex-development/GameFlexApi/DeploymentStage.v1/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiDeploymentStagev1A494DD80"}], "/gameflex-development/GameFlexApi/Endpoint": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiEndpoint1F3A0AB8"}], "/gameflex-development/GameFlexApi/Default": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/GameFlexApi/Default/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiOPTIONS68CD382C"}], "/gameflex-development/GameFlexApi/Default/health": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/health/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApihealth412F2DFA"}], "/gameflex-development/GameFlexApi/Default/health/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/health/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApihealthOPTIONS019C8FE9"}], "/gameflex-development/GameFlexApi/Default/health/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": "*"}}], "/gameflex-development/GameFlexApi/Default/health/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..health": [{"type": "aws:cdk:logicalId", "data": "GameFlexApihealthGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GEThealth272E2BBE"}], "/gameflex-development/GameFlexApi/Default/health/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..health": [{"type": "aws:cdk:logicalId", "data": "GameFlexApihealthGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GEThealth0C4B10BB"}], "/gameflex-development/GameFlexApi/Default/health/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApihealthGET79EDAB02"}], "/gameflex-development/GameFlexApi/Default/auth": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/auth/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiauth34DB0BF1"}], "/gameflex-development/GameFlexApi/Default/auth/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/auth/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiauthOPTIONSA390FDA7"}], "/gameflex-development/GameFlexApi/Default/auth/POST": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": "*"}}], "/gameflex-development/GameFlexApi/Default/auth/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..auth": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiauthPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTauth1F61597A"}], "/gameflex-development/GameFlexApi/Default/auth/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..auth": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiauthPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTauth4AFBD867"}], "/gameflex-development/GameFlexApi/Default/auth/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiauthPOSTB8EDAEDC"}], "/gameflex-development/GameFlexApi/Default/auth/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": "*"}}], "/gameflex-development/GameFlexApi/Default/auth/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..auth": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiauthGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETauth8265FB7D"}], "/gameflex-development/GameFlexApi/Default/auth/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..auth": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiauthGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETauthB8C104F6"}], "/gameflex-development/GameFlexApi/Default/auth/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiauthGET38F7822D"}], "/gameflex-development/GameFlexApi/Default/posts": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/posts/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiposts137B57F5"}], "/gameflex-development/GameFlexApi/Default/posts/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/posts/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsOPTIONSF0778EA0"}], "/gameflex-development/GameFlexApi/Default/posts/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETposts3D9E48D5"}], "/gameflex-development/GameFlexApi/Default/posts/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETpostsB09E6BBA"}], "/gameflex-development/GameFlexApi/Default/posts/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsGET0F7C7972"}], "/gameflex-development/GameFlexApi/Default/posts/POST": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTposts616FF206"}], "/gameflex-development/GameFlexApi/Default/posts/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTposts25767A0A"}], "/gameflex-development/GameFlexApi/Default/posts/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsPOST6CAE87D1"}], "/gameflex-development/GameFlexApi/Default/posts/draft": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/posts/draft/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsdraftAAEA2551"}], "/gameflex-development/GameFlexApi/Default/posts/draft/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/posts/draft/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsdraftOPTIONSCC49E433"}], "/gameflex-development/GameFlexApi/Default/posts/draft/POST": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/draft/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.draft": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsdraftPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTpostsdraft141EE64F"}], "/gameflex-development/GameFlexApi/Default/posts/draft/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.draft": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsdraftPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTpostsdraftF65F1D73"}], "/gameflex-development/GameFlexApi/Default/posts/draft/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsdraftPOST3EC59156"}], "/gameflex-development/GameFlexApi/Default/posts/{id}": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidEA3F073A"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidOPTIONSD440FEBE"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETpostsid1228102E"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETpostsidF96746EB"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidGETDBDEB4C6"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/PUT": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTpostsidA215B381"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTpostsid810758A2"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/PUT/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidPUT90659B0E"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/DELETE": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidDELETEApiPermissiongameflexdevelopmentGameFlexApi1C9066F4DELETEpostsidACA73A1C"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidDELETEApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4DELETEpostsid512347D2"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/DELETE/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidDELETE3A55CAA5"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/media": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/media/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidmediaA8DE1E9D"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/media/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/media/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidmediaOPTIONS73D78B81"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.media": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidmediaPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTpostsidmediaD261D149"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.media": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidmediaPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTpostsidmediaED4A6CBB"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidmediaPUT1A3D52BF"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/publish": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/publish/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidpublish67AF9D0C"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/publish/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/publish/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidpublishOPTIONSD6B22911"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.publish": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidpublishPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTpostsidpublishF74F2BCF"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.publish": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidpublishPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTpostsidpublish13867156"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidpublishPUTD94AEA0F"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidlikeCDEA7366"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidlikeOPTIONS4C5C766C"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/POST": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.like": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidlikePOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTpostsidlike5BFFC18C"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.like": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidlikePOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTpostsidlike17A6FFA2"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidlikePOSTF39DD595"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}.like": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidlikeDELETEApiPermissiongameflexdevelopmentGameFlexApi1C9066F4DELETEpostsidlike87453760"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}.like": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidlikeDELETEApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4DELETEpostsidlikeD1BA6E14"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidlikeDELETE1046505C"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidcomments3BE1F05E"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidcommentsOPTIONS863B9C13"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}.comments": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidcommentsGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETpostsidcommentsB568A75F"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}.comments": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidcommentsGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETpostsidcomments8820FCE4"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidcommentsGETF1DF9BDB"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.comments": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidcommentsPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTpostsidcomments5D5E5838"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.comments": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidcommentsPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTpostsidcommentsC2738A2B"}], "/gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApipostsidcommentsPOST45548CB9"}], "/gameflex-development/GameFlexApi/Default/users": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/users/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusers539BBD8B"}], "/gameflex-development/GameFlexApi/Default/users/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/users/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersOPTIONS91C48C12"}], "/gameflex-development/GameFlexApi/Default/users/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/users/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETusersCA21EEFD"}], "/gameflex-development/GameFlexApi/Default/users/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETusers4D263189"}], "/gameflex-development/GameFlexApi/Default/users/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersGETD39706DD"}], "/gameflex-development/GameFlexApi/Default/users/POST": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/users/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..users": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTusersC1E14F4E"}], "/gameflex-development/GameFlexApi/Default/users/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..users": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTusers52B047F5"}], "/gameflex-development/GameFlexApi/Default/users/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersPOST5E8DF6A8"}], "/gameflex-development/GameFlexApi/Default/users/{id}": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/users/{id}/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersidDF782D5C"}], "/gameflex-development/GameFlexApi/Default/users/{id}/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/users/{id}/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersidOPTIONSFAA6CD24"}], "/gameflex-development/GameFlexApi/Default/users/{id}/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/users/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersidGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETusersidEC8EE65D"}], "/gameflex-development/GameFlexApi/Default/users/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersidGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETusersidD49C646A"}], "/gameflex-development/GameFlexApi/Default/users/{id}/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersidGET5EDFC202"}], "/gameflex-development/GameFlexApi/Default/users/{id}/PUT": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/users/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersidPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTusersidD9AD0EA0"}], "/gameflex-development/GameFlexApi/Default/users/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersidPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTusersid4CD0E122"}], "/gameflex-development/GameFlexApi/Default/users/{id}/PUT/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersidPUT6BC0DEBC"}], "/gameflex-development/GameFlexApi/Default/users/profile": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/users/profile/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofileE72A3ECA"}], "/gameflex-development/GameFlexApi/Default/users/profile/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/users/profile/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofileOPTIONS3FBFBB96"}], "/gameflex-development/GameFlexApi/Default/users/profile/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/users/profile/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofileGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETusersprofileBD9A6FF8"}], "/gameflex-development/GameFlexApi/Default/users/profile/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofileGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETusersprofile18F88C76"}], "/gameflex-development/GameFlexApi/Default/users/profile/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofileGETC63B83E9"}], "/gameflex-development/GameFlexApi/Default/users/profile/PUT": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/users/profile/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.profile": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofilePUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTusersprofileC7CD10E2"}], "/gameflex-development/GameFlexApi/Default/users/profile/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.profile": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofilePUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTusersprofileF01F543C"}], "/gameflex-development/GameFlexApi/Default/users/profile/PUT/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofilePUTB1C58802"}], "/gameflex-development/GameFlexApi/Default/users/profile/liked-posts": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/users/profile/liked-posts/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofilelikedposts922C1EB7"}], "/gameflex-development/GameFlexApi/Default/users/profile/liked-posts/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/users/profile/liked-posts/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofilelikedpostsOPTIONS91344DD8"}], "/gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile.liked-posts": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofilelikedpostsGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETusersprofilelikedpostsC6301664"}], "/gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile.liked-posts": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofilelikedpostsGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETusersprofilelikedposts985C2BCA"}], "/gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApiusersprofilelikedpostsGETC07CC6B9"}], "/gameflex-development/GameFlexApi/Default/media": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/media/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimedia4CFF2055"}], "/gameflex-development/GameFlexApi/Default/media/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/media/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaOPTIONS888063DA"}], "/gameflex-development/GameFlexApi/Default/media/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/media/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..media": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETmedia851AB196"}], "/gameflex-development/GameFlexApi/Default/media/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..media": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETmedia9C36643A"}], "/gameflex-development/GameFlexApi/Default/media/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaGETF88B1162"}], "/gameflex-development/GameFlexApi/Default/media/POST": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/media/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..media": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTmediaFA50121E"}], "/gameflex-development/GameFlexApi/Default/media/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..media": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTmediaE6B58FC3"}], "/gameflex-development/GameFlexApi/Default/media/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaPOST23821932"}], "/gameflex-development/GameFlexApi/Default/media/upload": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/media/upload/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaupload246052B2"}], "/gameflex-development/GameFlexApi/Default/media/upload/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/media/upload/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediauploadOPTIONS3D8C100F"}], "/gameflex-development/GameFlexApi/Default/media/upload/POST": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/media/upload/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..media.upload": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediauploadPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTmediauploadFE07429B"}], "/gameflex-development/GameFlexApi/Default/media/upload/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..media.upload": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediauploadPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTmediaupload2A33C7CB"}], "/gameflex-development/GameFlexApi/Default/media/upload/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediauploadPOST48EB5DC0"}], "/gameflex-development/GameFlexApi/Default/media/{id}": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/media/{id}/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidB68B38E9"}], "/gameflex-development/GameFlexApi/Default/media/{id}/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/media/{id}/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidOPTIONS15C420E0"}], "/gameflex-development/GameFlexApi/Default/media/{id}/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/media/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..media.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETmediaid338CA734"}], "/gameflex-development/GameFlexApi/Default/media/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..media.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETmediaid0AFE803B"}], "/gameflex-development/GameFlexApi/Default/media/{id}/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidGET53E65C4D"}], "/gameflex-development/GameFlexApi/Default/media/{id}/PUT": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/media/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..media.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTmediaid6309CDA4"}], "/gameflex-development/GameFlexApi/Default/media/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..media.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTmediaid276B28A2"}], "/gameflex-development/GameFlexApi/Default/media/{id}/PUT/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidPUTD672B82A"}], "/gameflex-development/GameFlexApi/Default/media/{id}/DELETE": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/media/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..media.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidDELETEApiPermissiongameflexdevelopmentGameFlexApi1C9066F4DELETEmediaidADA8C4BB"}], "/gameflex-development/GameFlexApi/Default/media/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..media.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidDELETEApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4DELETEmediaid54761C46"}], "/gameflex-development/GameFlexApi/Default/media/{id}/DELETE/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApimediaidDELETEF9AAD476"}], "/gameflex-development/GameFlexApi/Default/reflexes": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/reflexes/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesF9B892EC"}], "/gameflex-development/GameFlexApi/Default/reflexes/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/reflexes/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesOPTIONS651473FA"}], "/gameflex-development/GameFlexApi/Default/reflexes/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/reflexes/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETreflexesD3B84664"}], "/gameflex-development/GameFlexApi/Default/reflexes/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETreflexes75399A82"}], "/gameflex-development/GameFlexApi/Default/reflexes/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesGET98D253A6"}], "/gameflex-development/GameFlexApi/Default/reflexes/POST": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/reflexes/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..reflexes": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTreflexes6C7E14B2"}], "/gameflex-development/GameFlexApi/Default/reflexes/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..reflexes": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTreflexesE9A2DF18"}], "/gameflex-development/GameFlexApi/Default/reflexes/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesPOST49B0BCC9"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidFB646CA8"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidOPTIONS9DD475C6"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETreflexesidC72268A4"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETreflexesid19262906"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidGETE144C995"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..reflexes.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTreflexesid4DE788CC"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..reflexes.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTreflexesid67A173B7"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidPUT3264D76B"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..reflexes.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidDELETEApiPermissiongameflexdevelopmentGameFlexApi1C9066F4DELETEreflexesidF8D33183"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..reflexes.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidDELETEApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4DELETEreflexesid7351C9EC"}], "/gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApireflexesidDELETEFDB13D0A"}], "/gameflex-development/GameFlexApi/Default/channels": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/channels/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannels7A12BEA5"}], "/gameflex-development/GameFlexApi/Default/channels/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/channels/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsOPTIONS6D8DAFD4"}], "/gameflex-development/GameFlexApi/Default/channels/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/channels/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETchannelsC9BB72A4"}], "/gameflex-development/GameFlexApi/Default/channels/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETchannels48E71BB4"}], "/gameflex-development/GameFlexApi/Default/channels/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsGET659DD05A"}], "/gameflex-development/GameFlexApi/Default/channels/POST": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/channels/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..channels": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTchannelsDC7248FA"}], "/gameflex-development/GameFlexApi/Default/channels/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..channels": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTchannelsB7C82A82"}], "/gameflex-development/GameFlexApi/Default/channels/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsPOSTCF16E736"}], "/gameflex-development/GameFlexApi/Default/channels/{id}": [{"type": "aws:cdk:analytics:construct", "data": {"parent": "*", "pathPart": "*"}}], "/gameflex-development/GameFlexApi/Default/channels/{id}/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidA7085A68"}], "/gameflex-development/GameFlexApi/Default/channels/{id}/OPTIONS": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}}, {"type": "aws:cdk:analytics:method", "data": {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}}], "/gameflex-development/GameFlexApi/Default/channels/{id}/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidOPTIONS29E6B8F8"}], "/gameflex-development/GameFlexApi/Default/channels/{id}/GET": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/channels/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETchannelsid41711DF8"}], "/gameflex-development/GameFlexApi/Default/channels/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETchannelsidA9D411FD"}], "/gameflex-development/GameFlexApi/Default/channels/{id}/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidGETCBD3255E"}], "/gameflex-development/GameFlexApi/Default/channels/{id}/PUT": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/channels/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..channels.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTchannelsidC0518577"}], "/gameflex-development/GameFlexApi/Default/channels/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..channels.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTchannelsid0EFA3962"}], "/gameflex-development/GameFlexApi/Default/channels/{id}/PUT/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidPUT5A9E3CDD"}], "/gameflex-development/GameFlexApi/Default/channels/{id}/DELETE": [{"type": "aws:cdk:analytics:construct", "data": {"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}}], "/gameflex-development/GameFlexApi/Default/channels/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..channels.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidDELETEApiPermissiongameflexdevelopmentGameFlexApi1C9066F4DELETEchannelsidB3E67663"}], "/gameflex-development/GameFlexApi/Default/channels/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..channels.{id}": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidDELETEApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4DELETEchannelsidBD961F2E"}], "/gameflex-development/GameFlexApi/Default/channels/{id}/DELETE/Resource": [{"type": "aws:cdk:logicalId", "data": "GameFlexApichannelsidDELETEB3C13444"}], "/gameflex-development/DefaultAuthorizer": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/gameflex-development/DefaultAuthorizer/Resource": [{"type": "aws:cdk:logicalId", "data": "DefaultAuthorizerCA0170E0"}], "/gameflex-development/ApiGatewayUrl": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayUrl"}], "/gameflex-development/UserPoolId": [{"type": "aws:cdk:logicalId", "data": "UserPoolId"}], "/gameflex-development/UserPoolClientId": [{"type": "aws:cdk:logicalId", "data": "UserPoolClientId"}], "/gameflex-development/R2SecretArn": [{"type": "aws:cdk:logicalId", "data": "R2SecretArn"}], "/gameflex-development/AppConfigSecretArn": [{"type": "aws:cdk:logicalId", "data": "AppConfigSecretArn"}], "/gameflex-development/PostsTableName": [{"type": "aws:cdk:logicalId", "data": "PostsTableName"}], "/gameflex-development/MediaTableName": [{"type": "aws:cdk:logicalId", "data": "MediaTableName"}], "/gameflex-development/UserProfilesTableName": [{"type": "aws:cdk:logicalId", "data": "UserProfilesTableName"}], "/gameflex-development/CommentsTableName": [{"type": "aws:cdk:logicalId", "data": "CommentsTableName"}], "/gameflex-development/LikesTableName": [{"type": "aws:cdk:logicalId", "data": "LikesTableName"}], "/gameflex-development/FollowsTableName": [{"type": "aws:cdk:logicalId", "data": "FollowsTable<PERSON>ame"}], "/gameflex-development/ChannelsTableName": [{"type": "aws:cdk:logicalId", "data": "ChannelsTableName"}], "/gameflex-development/ChannelMembersTableName": [{"type": "aws:cdk:logicalId", "data": "ChannelMembersTableName"}], "/gameflex-development/ReflexesTableName": [{"type": "aws:cdk:logicalId", "data": "ReflexesTableName"}], "/gameflex-development/UsersTableName": [{"type": "aws:cdk:logicalId", "data": "UsersTableName"}], "/gameflex-development/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/gameflex-development/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/gameflex-development/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "gameflex-development"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}, "aws-cdk-lib/feature-flag-report": {"type": "cdk:feature-flag-report", "properties": {"module": "aws-cdk-lib", "flags": {"@aws-cdk/core:enableStackNameDuplicates": {"recommendedValue": true, "explanation": "Allow multiple stacks with the same name"}, "aws-cdk:enableDiffNoFail": {"recommendedValue": true, "explanation": "Make `cdk diff` not fail when there are differences"}, "@aws-cdk/core:newStyleStackSynthesis": {"recommendedValue": true, "explanation": "Switch to new stack synthesis method which enables CI/CD"}, "@aws-cdk/core:stackRelativeExports": {"recommendedValue": true, "explanation": "Name exports based on the construct paths relative to the stack, rather than the global construct path"}, "@aws-cdk/aws-ecr-assets:dockerIgnoreSupport": {"recommendedValue": true, "explanation": "DockerImageAsset properly supports `.dockerignore` files by default"}, "@aws-cdk/aws-secretsmanager:parseOwnedSecretName": {"recommendedValue": true, "explanation": "Fix the referencing of SecretsManager names from ARNs"}, "@aws-cdk/aws-kms:defaultKeyPolicies": {"recommendedValue": true, "explanation": "Tighten default KMS key policies"}, "@aws-cdk/aws-s3:grantWriteWithoutAcl": {"recommendedValue": true, "explanation": "Remove `PutObjectAcl` from Bucket.grantWrite"}, "@aws-cdk/aws-ecs-patterns:removeDefaultDesiredCount": {"recommendedValue": true, "explanation": "Do not specify a default DesiredCount for ECS services"}, "@aws-cdk/aws-rds:lowercaseDbIdentifier": {"recommendedValue": true, "explanation": "Force lowercasing of RDS Cluster names in CDK"}, "@aws-cdk/aws-apigateway:usagePlanKeyOrderInsensitiveId": {"recommendedValue": true, "explanation": "Allow adding/removing multiple UsagePlanKeys independently"}, "@aws-cdk/aws-efs:defaultEncryptionAtRest": {"recommendedValue": true, "explanation": "Enable this feature flag to have elastic file systems encrypted at rest by default."}, "@aws-cdk/aws-lambda:recognizeVersionProps": {"recommendedValue": true, "explanation": "Enable this feature flag to opt in to the updated logical id calculation for Lambda Version created using the  `fn.currentVersion`."}, "@aws-cdk/aws-lambda:recognizeLayerVersion": {"userValue": true, "recommendedValue": true, "explanation": "Enable this feature flag to opt in to the updated logical id calculation for Lambda Version created using the `fn.currentVersion`."}, "@aws-cdk/aws-cloudfront:defaultSecurityPolicyTLSv1.2_2021": {"recommendedValue": true, "explanation": "Enable this feature flag to have cloudfront distributions use the security policy TLSv1.2_2021 by default."}, "@aws-cdk/core:checkSecretUsage": {"userValue": true, "recommendedValue": true, "explanation": "Enable this flag to make it impossible to accidentally use SecretValues in unsafe locations"}, "@aws-cdk/core:target-partitions": {"userValue": ["aws", "aws-cn"], "recommendedValue": ["aws", "aws-cn"], "explanation": "What regions to include in lookup tables of environment agnostic stacks"}, "@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver": {"userValue": true, "recommendedValue": true, "explanation": "ECS extensions will automatically add an `awslogs` driver if no logging is specified"}, "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName": {"userValue": true, "recommendedValue": true, "explanation": "Enable this feature flag to have Launch Templates generated by the `InstanceRequireImdsv2Aspect` use unique names."}, "@aws-cdk/aws-ecs:arnFormatIncludesClusterName": {"userValue": true, "recommendedValue": true, "explanation": "ARN format used by ECS. In the new ARN format, the cluster name is part of the resource ID."}, "@aws-cdk/aws-iam:minimizePolicies": {"userValue": true, "recommendedValue": true, "explanation": "Minimize IAM policies by combining Statements"}, "@aws-cdk/core:validateSnapshotRemovalPolicy": {"userValue": true, "recommendedValue": true, "explanation": "Error on snapshot removal policies on resources that do not support it."}, "@aws-cdk/aws-codepipeline:crossAccountKeyAliasStackSafeResourceName": {"userValue": true, "recommendedValue": true, "explanation": "Generate key aliases that include the stack name"}, "@aws-cdk/aws-s3:createDefaultLoggingPolicy": {"userValue": true, "recommendedValue": true, "explanation": "Enable this feature flag to create an S3 bucket policy by default in cases where an AWS service would automatically create the Policy if one does not exist."}, "@aws-cdk/aws-sns-subscriptions:restrictSqsDescryption": {"userValue": true, "recommendedValue": true, "explanation": "Restrict KMS key policy for encrypted Queues a bit more"}, "@aws-cdk/aws-apigateway:disableCloudWatchRole": {"userValue": true, "recommendedValue": true, "explanation": "Make default CloudWatch Role behavior safe for multiple API Gateways in one environment"}, "@aws-cdk/core:enablePartitionLiterals": {"userValue": true, "recommendedValue": true, "explanation": "Make ARNs concrete if AWS partition is known"}, "@aws-cdk/aws-events:eventsTargetQueueSameAccount": {"userValue": true, "recommendedValue": true, "explanation": "Event Rules may only push to encrypted SQS queues in the same account"}, "@aws-cdk/aws-ecs:disableExplicitDeploymentControllerForCircuitBreaker": {"userValue": true, "recommendedValue": true, "explanation": "Avoid setting the \"ECS\" deployment controller when adding a circuit breaker"}, "@aws-cdk/aws-iam:importedRoleStackSafeDefaultPolicyName": {"userValue": true, "recommendedValue": true, "explanation": "Enable this feature to by default create default policy names for imported roles that depend on the stack the role is in."}, "@aws-cdk/aws-s3:serverAccessLogsUseBucketPolicy": {"userValue": true, "recommendedValue": true, "explanation": "Use S3 Bucket Policy instead of ACLs for Server Access Logging"}, "@aws-cdk/aws-route53-patters:useCertificate": {"userValue": true, "recommendedValue": true, "explanation": "Use the official `Certificate` resource instead of `DnsValidatedCertificate`"}, "@aws-cdk/customresources:installLatestAwsSdkDefault": {"userValue": false, "recommendedValue": false, "explanation": "Whether to install the latest SDK by default in AwsCustomResource"}, "@aws-cdk/aws-rds:databaseProxyUniqueResourceName": {"userValue": true, "recommendedValue": true, "explanation": "Use unique resource name for Database Proxy"}, "@aws-cdk/aws-codedeploy:removeAlarmsFromDeploymentGroup": {"userValue": true, "recommendedValue": true, "explanation": "Remove CloudWatch alarms from deployment group"}, "@aws-cdk/aws-apigateway:authorizerChangeDeploymentLogicalId": {"userValue": true, "recommendedValue": true, "explanation": "Include authorizer configuration in the calculation of the API deployment logical ID."}, "@aws-cdk/aws-ec2:launchTemplateDefaultUserData": {"userValue": true, "recommendedValue": true, "explanation": "Define user data for a launch template by default when a machine image is provided."}, "@aws-cdk/aws-secretsmanager:useAttachedSecretResourcePolicyForSecretTargetAttachments": {"userValue": true, "recommendedValue": true, "explanation": "SecretTargetAttachments uses the ResourcePolicy of the attached Secret."}, "@aws-cdk/aws-redshift:columnId": {"userValue": true, "recommendedValue": true, "explanation": "Whether to use an ID to track Redshift column changes"}, "@aws-cdk/aws-stepfunctions-tasks:enableEmrServicePolicyV2": {"userValue": true, "recommendedValue": true, "explanation": "Enable AmazonEMRServicePolicy_v2 managed policies"}, "@aws-cdk/aws-ec2:restrictDefaultSecurityGroup": {"userValue": true, "recommendedValue": true, "explanation": "Restrict access to the VPC default security group"}, "@aws-cdk/aws-apigateway:requestValidatorUniqueId": {"userValue": true, "recommendedValue": true, "explanation": "Generate a unique id for each RequestValidator added to a method"}, "@aws-cdk/aws-kms:aliasNameRef": {"userValue": true, "recommendedValue": true, "explanation": "KMS Alias name and keyArn will have implicit reference to KMS Key"}, "@aws-cdk/aws-kms:applyImportedAliasPermissionsToPrincipal": {"userValue": true, "recommendedValue": true, "explanation": "Enable grant methods on Aliases imported by name to use kms:ResourceAliases condition"}, "@aws-cdk/aws-autoscaling:generateLaunchTemplateInsteadOfLaunchConfig": {"userValue": true, "recommendedValue": true, "explanation": "Generate a launch template when creating an AutoScalingGroup"}, "@aws-cdk/core:includePrefixInUniqueNameGeneration": {"userValue": true, "recommendedValue": true, "explanation": "Include the stack prefix in the stack name generation process"}, "@aws-cdk/aws-efs:denyAnonymousAccess": {"userValue": true, "recommendedValue": true, "explanation": "EFS denies anonymous clients accesses"}, "@aws-cdk/aws-opensearchservice:enableOpensearchMultiAzWithStandby": {"userValue": true, "recommendedValue": true, "explanation": "Enables support for Multi-AZ with Standby deployment for opensearch domains"}, "@aws-cdk/aws-lambda-nodejs:useLatestRuntimeVersion": {"userValue": true, "recommendedValue": true, "explanation": "Enables aws-lambda-nodejs.Function to use the latest available NodeJs runtime as the default"}, "@aws-cdk/aws-efs:mountTargetOrderInsensitiveLogicalId": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, mount targets will have a stable logicalId that is linked to the associated subnet."}, "@aws-cdk/aws-rds:auroraClusterChangeScopeOfInstanceParameterGroupWithEachParameters": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, a scope of InstanceParameterGroup for AuroraClusterInstance with each parameters will change."}, "@aws-cdk/aws-appsync:useArnForSourceApiAssociationIdentifier": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, will always use the arn for identifiers for CfnSourceApiAssociation in the GraphqlApi construct rather than id."}, "@aws-cdk/aws-rds:preventRenderingDeprecatedCredentials": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, creating an RDS database cluster from a snapshot will only render credentials for snapshot credentials."}, "@aws-cdk/aws-codepipeline-actions:useNewDefaultBranchForCodeCommitSource": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the CodeCommit source action is using the default branch name 'main'."}, "@aws-cdk/aws-cloudwatch-actions:changeLambdaPermissionLogicalIdForLambdaAction": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the logical ID of a Lambda permission for a Lambda action includes an alarm ID."}, "@aws-cdk/aws-codepipeline:crossAccountKeysDefaultValueToFalse": {"userValue": true, "recommendedValue": true, "explanation": "Enables Pipeline to set the default value for crossAccountKeys to false."}, "@aws-cdk/aws-codepipeline:defaultPipelineTypeToV2": {"userValue": true, "recommendedValue": true, "explanation": "Enables Pipeline to set the default pipeline type to V2."}, "@aws-cdk/aws-kms:reduceCrossAccountRegionPolicyScope": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, IAM Policy created from KMS key grant will reduce the resource scope to this key only."}, "@aws-cdk/pipelines:reduceAssetRoleTrustScope": {"recommendedValue": true, "explanation": "Remove the root account principal from PipelineAssetsFileRole trust policy"}, "@aws-cdk/aws-eks:nodegroupNameAttribute": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, nodegroupName attribute of the provisioned EKS NodeGroup will not have the cluster name prefix."}, "@aws-cdk/aws-ec2:ebsDefaultGp3Volume": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the default volume type of the EBS volume will be GP3"}, "@aws-cdk/aws-ecs:removeDefaultDeploymentAlarm": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, remove default deployment alarm settings"}, "@aws-cdk/custom-resources:logApiResponseDataPropertyTrueDefault": {"userValue": false, "recommendedValue": false, "explanation": "When enabled, the custom resource used for `AwsCustomResource` will configure the `logApiResponseData` property as true by default"}, "@aws-cdk/aws-s3:keepNotificationInImportedBucket": {"userValue": false, "recommendedValue": false, "explanation": "When enabled, Adding notifications to a bucket in the current stack will not remove notification from imported stack."}, "@aws-cdk/aws-stepfunctions-tasks:useNewS3UriParametersForBedrockInvokeModelTask": {"recommendedValue": true, "explanation": "When enabled, use new props for S3 URI field in task definition of state machine for bedrock invoke model."}, "@aws-cdk/core:explicitStackTags": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, stack tags need to be assigned explicitly on a Stack."}, "@aws-cdk/aws-ecs:enableImdsBlockingDeprecatedFeature": {"userValue": false, "recommendedValue": false, "explanation": "When set to true along with canContainersAccessInstanceRole=false in ECS cluster, new updated commands will be added to UserData to block container accessing IMDS. **Applicable to Linux only. IMPORTANT: See [details.](#aws-cdkaws-ecsenableImdsBlockingDeprecatedFeature)**"}, "@aws-cdk/aws-ecs:disableEcsImdsBlocking": {"userValue": true, "recommendedValue": true, "explanation": "When set to true, CDK synth will throw exception if canContainersAccessInstanceRole is false. **IMPORTANT: See [details.](#aws-cdkaws-ecsdisableEcsImdsBlocking)**"}, "@aws-cdk/aws-ecs:reduceEc2FargateCloudWatchPermissions": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, we will only grant the necessary permissions when users specify cloudwatch log group through logConfiguration"}, "@aws-cdk/aws-dynamodb:resourcePolicyPerReplica": {"userValue": true, "recommendedValue": true, "explanation": "When enabled will allow you to specify a resource policy per replica, and not copy the source table policy to all replicas"}, "@aws-cdk/aws-ec2:ec2SumTImeoutEnabled": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, initOptions.timeout and resourceSignalTimeout values will be summed together."}, "@aws-cdk/aws-appsync:appSyncGraphQLAPIScopeLambdaPermission": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, a Lambda authorizer Permission created when using GraphqlApi will be properly scoped with a SourceArn."}, "@aws-cdk/aws-rds:setCorrectValueForDatabaseInstanceReadReplicaInstanceResourceId": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the value of property `instanceResourceId` in construct `DatabaseInstanceReadReplica` will be set to the correct value which is `DbiResourceId` instead of currently `DbInstanceArn`"}, "@aws-cdk/core:cfnIncludeRejectComplexResourceUpdateCreatePolicyIntrinsics": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, CFN templates added with `cfn-include` will error if the template contains Resource Update or Create policies with CFN Intrinsics that include non-primitive values."}, "@aws-cdk/aws-lambda-nodejs:sdkV3ExcludeSmithyPackages": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, both `@aws-sdk` and `@smithy` packages will be excluded from the Lambda Node.js 18.x runtime to prevent version mismatches in bundled applications."}, "@aws-cdk/aws-stepfunctions-tasks:fixRunEcsTaskPolicy": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the resource of IAM Run Ecs policy generated by SFN EcsRunTask will reference the definition, instead of constructing ARN."}, "@aws-cdk/aws-ec2:bastionHostUseAmazonLinux2023ByDefault": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the BastionHost construct will use the latest Amazon Linux 2023 AMI, instead of Amazon Linux 2."}, "@aws-cdk/core:aspectStabilization": {"recommendedValue": true, "explanation": "When enabled, a stabilization loop will be run when invoking Aspects during synthesis."}, "@aws-cdk/aws-route53-targets:userPoolDomainNameMethodWithoutCustomResource": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, use a new method for DNS Name of user pool domain target without creating a custom resource."}, "@aws-cdk/aws-elasticloadbalancingV2:albDualstackWithoutPublicIpv4SecurityGroupRulesDefault": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the default security group ingress rules will allow IPv6 ingress from anywhere"}, "@aws-cdk/aws-iam:oidcRejectUnauthorizedConnections": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the default behaviour of OIDC provider will reject unauthorized connections"}, "@aws-cdk/core:enableAdditionalMetadataCollection": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, CDK will expand the scope of usage data collected to better inform CDK development and improve communication for security concerns and emerging issues."}, "@aws-cdk/aws-lambda:createNewPoliciesWithAddToRolePolicy": {"userValue": false, "recommendedValue": false, "explanation": "[Deprecated] When enabled, Lambda will create new inline policies with AddToRolePolicy instead of adding to the Default Policy Statement"}, "@aws-cdk/aws-s3:setUniqueReplicationRoleName": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, CDK will automatically generate a unique role name that is used for s3 object replication."}, "@aws-cdk/pipelines:reduceStageRoleTrustScope": {"recommendedValue": true, "explanation": "Remove the root account principal from Stage addActions trust policy"}, "@aws-cdk/aws-events:requireEventBusPolicySid": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, grantPutEventsTo() will use resource policies with Statement IDs for service principals."}, "@aws-cdk/core:aspectPrioritiesMutating": {"userValue": true, "recommendedValue": true, "explanation": "When set to true, Aspects added by the construct library on your behalf will be given a priority of MUTATING."}, "@aws-cdk/aws-dynamodb:retainTableReplica": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, table replica will be default to the removal policy of source table unless specified otherwise."}, "@aws-cdk/cognito:logUserPoolClientSecretValue": {"recommendedValue": false, "explanation": "When disabled, the value of the user pool client secret will not be logged in the custom resource lambda function logs."}, "@aws-cdk/pipelines:reduceCrossAccountActionRoleTrustScope": {"recommendedValue": true, "explanation": "When enabled, scopes down the trust policy for the cross-account action role"}, "@aws-cdk/aws-stepfunctions:useDistributedMapResultWriterV2": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the resultWriterV2 property of DistributedMap will be used insted of resultWriter"}, "@aws-cdk/s3-notifications:addS3TrustKeyPolicyForSnsSubscriptions": {"userValue": true, "recommendedValue": true, "explanation": "Add an S3 trust policy to a KMS key resource policy for SNS subscriptions."}, "@aws-cdk/aws-ec2:requirePrivateSubnetsForEgressOnlyInternetGateway": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the EgressOnlyGateway resource is only created if private subnets are defined in the dual-stack VPC."}, "@aws-cdk/aws-ec2-alpha:useResourceIdForVpcV2Migration": {"recommendedValue": false, "explanation": "When enabled, use resource IDs for VPC V2 migration"}, "@aws-cdk/aws-s3:publicAccessBlockedByDefault": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, setting any combination of options for BlockPublicAccess will automatically set true for any options not defined."}, "@aws-cdk/aws-lambda:useCdkManagedLogGroup": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, CDK creates and manages loggroup for the lambda function"}}}}}, "minimumCliVersion": "2.1020.2"}