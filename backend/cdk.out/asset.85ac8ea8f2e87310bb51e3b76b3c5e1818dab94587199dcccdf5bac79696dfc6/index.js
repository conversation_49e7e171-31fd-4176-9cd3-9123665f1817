"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const client_s3_1 = require("@aws-sdk/client-s3");
const s3_request_presigner_1 = require("@aws-sdk/s3-request-presigner");
const uuid_1 = require("uuid");
const r2_config_1 = require("./r2-config");
// Configure AWS SDK for DynamoDB
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};
const dynamodbClient = new client_dynamodb_1.DynamoDBClient(awsConfig);
const dynamodb = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamodbClient);
const MEDIA_TABLE = process.env.MEDIA_TABLE;
// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});
// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event) => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return event.requestContext.authorizer.userId;
    }
    return null;
};
// Upload media
const uploadMedia = async (event) => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { fileName, fileType, fileSize, mediaType } = JSON.parse(event.body);
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!fileName || !fileType) {
            return createResponse(400, { error: 'fileName and fileType are required' });
        }
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        const mediaId = (0, uuid_1.v4)();
        const fileExtension = fileName.split('.').pop();
        // Create R2 key based on media type
        let r2Key;
        if (mediaType === 'avatar') {
            r2Key = `avatars/${userId}/${mediaId}.${fileExtension}`;
        }
        else {
            r2Key = `media/${userId}/${mediaId}.${fileExtension}`;
        }
        // Get R2 configuration and create client
        const r2Client = await (0, r2_config_1.createR2Client)();
        const bucketName = await (0, r2_config_1.getR2BucketName)();
        // Generate presigned URL for R2 upload
        let presignedUrl;
        if ('getSignedUrl' in r2Client && typeof r2Client.getSignedUrl === 'function') {
            // Mock client
            presignedUrl = r2Client.getSignedUrl('putObject', {
                Bucket: bucketName,
                Key: r2Key,
                ContentType: fileType
            });
        }
        else {
            // Real S3Client
            const command = new client_s3_1.PutObjectCommand({
                Bucket: bucketName,
                Key: r2Key,
                ContentType: fileType
            });
            presignedUrl = await (0, s3_request_presigner_1.getSignedUrl)(r2Client, command, { expiresIn: 300 });
        }
        // Get public URL
        const publicUrl = await (0, r2_config_1.getR2PublicUrl)();
        // Create media record
        const mediaRecord = {
            id: mediaId,
            fileName,
            fileType,
            fileSize: fileSize || 0,
            mediaType: mediaType || 'image',
            userId,
            r2Key,
            bucketName,
            url: `${publicUrl}/${r2Key}`,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        const putCommand = new lib_dynamodb_1.PutCommand({
            TableName: MEDIA_TABLE,
            Item: mediaRecord
        });
        await dynamodb.send(putCommand);
        return createResponse(200, {
            message: 'Upload URL generated successfully',
            mediaId,
            uploadUrl: presignedUrl,
            media: mediaRecord
        });
    }
    catch (error) {
        console.error('UploadMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const errorStack = error instanceof Error ? error.stack : 'No stack trace';
        console.error('UploadMedia error stack:', errorStack);
        return createResponse(500, { error: 'Failed to generate upload URL', details: errorMessage });
    }
};
// Get media
const getMedia = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }
        const getCommand = new lib_dynamodb_1.GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });
        const result = await dynamodb.send(getCommand);
        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }
        const media = result.Item;
        // For R2, we use the public URL directly since it's publicly accessible
        // No need for presigned URLs for downloads with R2 public bucket
        if (media.status === 'uploaded' && media.url) {
            media.downloadUrl = media.url;
        }
        return createResponse(200, { media });
    }
    catch (error) {
        console.error('GetMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to get media', details: errorMessage });
    }
};
// Delete media
const deleteMedia = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }
        // Get media record
        const getCommand = new lib_dynamodb_1.GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });
        const result = await dynamodb.send(getCommand);
        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }
        const media = result.Item;
        // Delete from R2
        if (media.r2Key && media.bucketName) {
            const r2Client = await (0, r2_config_1.createR2Client)();
            if ('deleteObject' in r2Client && typeof r2Client.deleteObject === 'function') {
                // Mock client
                await r2Client.deleteObject().promise();
            }
            else {
                // Real S3Client
                const deleteCommand = new client_s3_1.DeleteObjectCommand({
                    Bucket: media.bucketName,
                    Key: media.r2Key
                });
                await r2Client.send(deleteCommand);
            }
        }
        // Delete from DynamoDB
        const deleteCommand = new lib_dynamodb_1.DeleteCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });
        await dynamodb.send(deleteCommand);
        return createResponse(200, { message: 'Media deleted successfully' });
    }
    catch (error) {
        console.error('DeleteMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to delete media', details: errorMessage });
    }
};
// Update media status (called after successful upload)
const updateMediaStatus = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { status } = JSON.parse(event.body);
        if (!status) {
            return createResponse(400, { error: 'Status is required' });
        }
        const updateCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: MEDIA_TABLE,
            Key: { id },
            UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': status,
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);
        return createResponse(200, {
            message: 'Media status updated successfully',
            media: result.Attributes
        });
    }
    catch (error) {
        console.error('UpdateMediaStatus error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to update media status', details: errorMessage });
    }
};
// Main handler
const handler = async (event, context) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    const { httpMethod, path, pathParameters } = event;
    try {
        if (httpMethod === 'POST' && path === '/media/upload') {
            return await uploadMedia(event);
        }
        else if (httpMethod === 'GET' && pathParameters && pathParameters.id) {
            return await getMedia(event);
        }
        else if (httpMethod === 'DELETE' && pathParameters && pathParameters.id) {
            return await deleteMedia(event);
        }
        else if (httpMethod === 'PUT' && pathParameters && pathParameters.id) {
            return await updateMediaStatus(event);
        }
        else {
            return createResponse(404, { error: 'Not found' });
        }
    }
    catch (error) {
        console.error('Handler error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Internal server error', details: errorMessage });
    }
};
exports.handler = handler;
//# sourceMappingURL=data:application/json;base64,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