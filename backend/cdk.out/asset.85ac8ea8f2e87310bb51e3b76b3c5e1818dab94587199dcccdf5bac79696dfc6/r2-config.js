"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getR2Config = getR2Config;
exports.createR2Client = createR2Client;
exports.getR2BucketName = getR2BucketName;
exports.getR2PublicUrl = getR2PublicUrl;
exports.getAppConfig = getAppConfig;
const client_secrets_manager_1 = require("@aws-sdk/client-secrets-manager");
const client_s3_1 = require("@aws-sdk/client-s3");
// Cache for R2 configuration to avoid repeated Secrets Manager calls
let r2ConfigCache = null;
let cacheTimestamp = null;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
/**
 * Get R2 configuration from AWS Secrets Manager (all environments)
 */
async function getR2Config() {
    const environment = process.env.ENVIRONMENT || 'development';
    const secretName = process.env.R2_SECRET_NAME;
    if (!secretName) {
        // Fallback to environment variables for backward compatibility
        console.warn('R2_SECRET_NAME not found, falling back to environment variables');
        return {
            accountId: process.env.R2_ACCOUNT_ID,
            accessKeyId: process.env.R2_ACCESS_KEY_ID,
            secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
            endpoint: process.env.R2_ENDPOINT,
            bucketName: process.env.R2_BUCKET_NAME,
            publicUrl: process.env.R2_PUBLIC_URL
        };
    }
    // Use AWS Secrets Manager for all environments
    // Check cache first
    const now = Date.now();
    if (r2ConfigCache && cacheTimestamp && (now - cacheTimestamp) < CACHE_TTL) {
        return r2ConfigCache;
    }
    try {
        const secretsManager = new client_secrets_manager_1.SecretsManagerClient({
            region: process.env.AWS_REGION || 'us-west-2'
        });
        const command = new client_secrets_manager_1.GetSecretValueCommand({
            SecretId: secretName
        });
        const result = await secretsManager.send(command);
        if (!result.SecretString) {
            throw new Error('Secret value is empty');
        }
        const config = JSON.parse(result.SecretString);
        // Cache the configuration
        r2ConfigCache = config;
        cacheTimestamp = now;
        return config;
    }
    catch (error) {
        console.error('Failed to retrieve R2 configuration from Secrets Manager:', error);
        throw new Error('Failed to retrieve R2 configuration');
    }
}
/**
 * Create R2 client with proper configuration
 */
async function createR2Client() {
    const config = await getR2Config();
    // Return mock client if configuration is incomplete (for development)
    if (!config.endpoint || !config.accessKeyId || !config.secretAccessKey) {
        console.warn('R2 configuration incomplete, returning mock client');
        return {
            getSignedUrl: (operation, params) => {
                return `https://mock-r2-url.com/${params.Bucket}/${params.Key}?operation=${operation}`;
            },
            deleteObject: () => ({ promise: () => Promise.resolve() }),
            putObject: () => ({ promise: () => Promise.resolve() }),
            getObject: () => ({ promise: () => Promise.resolve({ Body: Buffer.from('mock') }) })
        };
    }
    const r2Config = {
        endpoint: config.endpoint,
        credentials: {
            accessKeyId: config.accessKeyId,
            secretAccessKey: config.secretAccessKey
        },
        region: 'auto', // R2 uses 'auto' as region
        forcePathStyle: true
    };
    return new client_s3_1.S3Client(r2Config);
}
/**
 * Get R2 bucket name from configuration
 */
async function getR2BucketName() {
    const config = await getR2Config();
    return config.bucketName || process.env.R2_BUCKET_NAME || 'gameflex-development';
}
/**
 * Get R2 public URL from configuration
 */
async function getR2PublicUrl() {
    const config = await getR2Config();
    return config.publicUrl || process.env.R2_PUBLIC_URL || 'https://pub-34709f09e8384ef1a67928492571c01d.r2.dev';
}
/**
 * Get application configuration from AWS Secrets Manager
 */
async function getAppConfig() {
    const secretName = process.env.APP_CONFIG_SECRET_NAME;
    if (!secretName) {
        // Fallback to environment variables
        console.warn('APP_CONFIG_SECRET_NAME not found, falling back to environment variables');
        return {
            cloudflareApiToken: process.env.CLOUDFLARE_API_TOKEN,
            testUserEmail: process.env.TEST_USER_EMAIL,
            testUserPassword: process.env.TEST_USER_PASSWORD,
            debugMode: process.env.DEBUG_MODE || process.env.ENVIRONMENT,
            apiBaseUrl: process.env.API_BASE_URL,
            userPoolId: process.env.USER_POOL_ID,
            userPoolClientId: process.env.USER_POOL_CLIENT_ID
        };
    }
    try {
        const secretsManager = new client_secrets_manager_1.SecretsManagerClient({
            region: process.env.AWS_REGION || 'us-west-2'
        });
        const command = new client_secrets_manager_1.GetSecretValueCommand({
            SecretId: secretName
        });
        const result = await secretsManager.send(command);
        if (!result.SecretString) {
            throw new Error('Secret value is empty');
        }
        return JSON.parse(result.SecretString);
    }
    catch (error) {
        console.error('Failed to retrieve app configuration from Secrets Manager:', error);
        throw new Error('Failed to retrieve app configuration');
    }
}
//# sourceMappingURL=data:application/json;base64,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