import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, DeleteCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import { createR2Client, getR2BucketName, getR2PublicUrl } from './r2-config';

// Configure AWS SDK for DynamoDB
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);
const MEDIA_TABLE = process.env.MEDIA_TABLE!;

// TypeScript interfaces
interface MediaRecord {
    id: string;
    fileName: string;
    fileType: string;
    fileSize: number;
    mediaType: string;
    userId: string;
    r2Key: string;
    bucketName: string;
    url: string;
    status: 'pending' | 'uploaded' | 'failed';
    createdAt: string;
    updatedAt: string;
}

interface UploadRequest {
    fileName: string;
    fileType: string;
    fileSize?: number;
    mediaType?: string;
}

interface UpdateStatusRequest {
    status: 'pending' | 'uploaded' | 'failed';
}

interface UserContext {
    userId: string;
}

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return (event.requestContext.authorizer as any).userId;
    }
    return null;
};

// Upload media
const uploadMedia = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { fileName, fileType, fileSize, mediaType }: UploadRequest = JSON.parse(event.body);

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!fileName || !fileType) {
            return createResponse(400, { error: 'fileName and fileType are required' });
        }

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        const mediaId = uuidv4();
        const fileExtension = fileName.split('.').pop();

        // Create R2 key based on media type
        let r2Key: string;
        if (mediaType === 'avatar') {
            r2Key = `avatars/${userId}/${mediaId}.${fileExtension}`;
        } else {
            r2Key = `media/${userId}/${mediaId}.${fileExtension}`;
        }

        // Get R2 configuration and create client
        const r2Client = await createR2Client();
        const bucketName = await getR2BucketName();

        // Generate presigned URL for R2 upload
        let presignedUrl: string;

        if ('getSignedUrl' in r2Client && typeof r2Client.getSignedUrl === 'function') {
            // Mock client
            presignedUrl = r2Client.getSignedUrl('putObject', {
                Bucket: bucketName,
                Key: r2Key,
                ContentType: fileType
            });
        } else {
            // Real S3Client
            const command = new PutObjectCommand({
                Bucket: bucketName,
                Key: r2Key,
                ContentType: fileType
            });
            presignedUrl = await getSignedUrl(r2Client as S3Client, command, { expiresIn: 300 });
        }

        // Get public URL
        const publicUrl = await getR2PublicUrl();

        // Create media record
        const mediaRecord: MediaRecord = {
            id: mediaId,
            fileName,
            fileType,
            fileSize: fileSize || 0,
            mediaType: mediaType || 'image',
            userId,
            r2Key,
            bucketName,
            url: `${publicUrl}/${r2Key}`,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        const putCommand = new PutCommand({
            TableName: MEDIA_TABLE,
            Item: mediaRecord
        });

        await dynamodb.send(putCommand);

        return createResponse(200, {
            message: 'Upload URL generated successfully',
            mediaId,
            uploadUrl: presignedUrl,
            media: mediaRecord
        });

    } catch (error) {
        console.error('UploadMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const errorStack = error instanceof Error ? error.stack : 'No stack trace';
        console.error('UploadMedia error stack:', errorStack);
        return createResponse(500, { error: 'Failed to generate upload URL', details: errorMessage });
    }
};

// Get media
const getMedia = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        const getCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });

        const result = await dynamodb.send(getCommand);

        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item as MediaRecord;

        // For R2, we use the public URL directly since it's publicly accessible
        // No need for presigned URLs for downloads with R2 public bucket
        if (media.status === 'uploaded' && media.url) {
            (media as any).downloadUrl = media.url;
        }

        return createResponse(200, { media });

    } catch (error) {
        console.error('GetMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to get media', details: errorMessage });
    }
};

// Delete media
const deleteMedia = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        // Get media record
        const getCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });

        const result = await dynamodb.send(getCommand);

        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item as MediaRecord;

        // Delete from R2
        if (media.r2Key && media.bucketName) {
            const r2Client = await createR2Client();

            if ('deleteObject' in r2Client && typeof r2Client.deleteObject === 'function') {
                // Mock client
                await r2Client.deleteObject().promise();
            } else {
                // Real S3Client
                const deleteCommand = new DeleteObjectCommand({
                    Bucket: media.bucketName,
                    Key: media.r2Key
                });
                await (r2Client as S3Client).send(deleteCommand);
            }
        }

        // Delete from DynamoDB
        const deleteCommand = new DeleteCommand({
            TableName: MEDIA_TABLE,
            Key: { id }
        });

        await dynamodb.send(deleteCommand);

        return createResponse(200, { message: 'Media deleted successfully' });

    } catch (error) {
        console.error('DeleteMedia error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to delete media', details: errorMessage });
    }
};

// Update media status (called after successful upload)
const updateMediaStatus = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Media ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { status }: UpdateStatusRequest = JSON.parse(event.body);

        if (!status) {
            return createResponse(400, { error: 'Status is required' });
        }

        const updateCommand = new UpdateCommand({
            TableName: MEDIA_TABLE,
            Key: { id },
            UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': status,
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });

        const result = await dynamodb.send(updateCommand);

        return createResponse(200, {
            message: 'Media status updated successfully',
            media: result.Attributes
        });

    } catch (error) {
        console.error('UpdateMediaStatus error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to update media status', details: errorMessage });
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        if (httpMethod === 'POST' && path === '/media/upload') {
            return await uploadMedia(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id) {
            return await getMedia(event);
        } else if (httpMethod === 'DELETE' && pathParameters && pathParameters.id) {
            return await deleteMedia(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id) {
            return await updateMediaStatus(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Internal server error', details: errorMessage });
    }
};
