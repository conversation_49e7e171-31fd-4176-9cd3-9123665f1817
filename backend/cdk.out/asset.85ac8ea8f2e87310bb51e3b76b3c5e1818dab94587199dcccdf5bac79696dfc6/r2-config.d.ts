import { S3Client } from '@aws-sdk/client-s3';
interface R2Config {
    accountId?: string;
    accessKeyId?: string;
    secretAccessKey?: string;
    endpoint?: string;
    bucketName?: string;
    publicUrl?: string;
}
interface AppConfig {
    cloudflareApiToken?: string;
    testUserEmail?: string;
    testUserPassword?: string;
    debugMode?: string;
    apiBaseUrl?: string;
    userPoolId?: string;
    userPoolClientId?: string;
}
interface MockR2Client {
    getSignedUrl: (operation: string, params: any) => string;
    deleteObject: () => {
        promise: () => Promise<void>;
    };
    putObject: () => {
        promise: () => Promise<void>;
    };
    getObject: () => {
        promise: () => Promise<{
            Body: Buffer;
        }>;
    };
}
/**
 * Get R2 configuration from AWS Secrets Manager (all environments)
 */
declare function getR2Config(): Promise<R2Config>;
/**
 * Create R2 client with proper configuration
 */
declare function createR2Client(): Promise<S3Client | MockR2Client>;
/**
 * Get R2 bucket name from configuration
 */
declare function getR2BucketName(): Promise<string>;
/**
 * Get R2 public URL from configuration
 */
declare function getR2PublicUrl(): Promise<string>;
/**
 * Get application configuration from AWS Secrets Manager
 */
declare function getAppConfig(): Promise<AppConfig>;
export { getR2Config, createR2Client, getR2BucketName, getR2PublicUrl, getAppConfig, R2Config, AppConfig };
