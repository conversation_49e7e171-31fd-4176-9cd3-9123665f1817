"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const client_s3_1 = require("@aws-sdk/client-s3");
const util_dynamodb_1 = require("@aws-sdk/util-dynamodb");
// Configure AWS SDK v3 clients
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};
const dynamodbClient = new client_dynamodb_1.DynamoDBClient(awsConfig);
const dynamodb = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamodbClient);
const s3 = new client_s3_1.S3Client(awsConfig);
const USERS_TABLE = process.env.USERS_TABLE;
const USER_PROFILES_TABLE = process.env.USER_PROFILES_TABLE;
const FOLLOWS_TABLE = process.env.FOLLOWS_TABLE;
const POSTS_TABLE = process.env.POSTS_TABLE;
const LIKES_TABLE = process.env.LIKES_TABLE;
const AVATARS_BUCKET = process.env.AVATARS_BUCKET;
// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});
// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event) => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return event.requestContext.authorizer.userId;
    }
    return null;
};
// Helper function to calculate user statistics
const calculateUserStats = async (userId) => {
    try {
        console.log('calculateUserStats: Calculating stats for user ID:', userId);
        // Count posts by user
        const scanPostsCommand = new lib_dynamodb_1.ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: 'user_id = :userId AND is_active = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            },
            Select: 'COUNT'
        });
        const postsResult = await dynamodb.send(scanPostsCommand);
        console.log('calculateUserStats: Found', postsResult.Count, 'posts for user', userId);
        // Count likes by user (using GSI)
        const queryLikesCommand = new lib_dynamodb_1.QueryCommand({
            TableName: LIKES_TABLE,
            IndexName: 'userId-index',
            KeyConditionExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        });
        const likesResult = await dynamodb.send(queryLikesCommand);
        console.log('calculateUserStats: Found', likesResult.Count, 'likes for user', userId);
        // Count followers
        const scanFollowersCommand = new lib_dynamodb_1.ScanCommand({
            TableName: FOLLOWS_TABLE,
            FilterExpression: 'followingId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        });
        const followersResult = await dynamodb.send(scanFollowersCommand);
        // Count following
        const scanFollowingCommand = new lib_dynamodb_1.ScanCommand({
            TableName: FOLLOWS_TABLE,
            FilterExpression: 'followerId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        });
        const followingResult = await dynamodb.send(scanFollowingCommand);
        const stats = {
            postsCount: postsResult.Count || 0,
            likesCount: likesResult.Count || 0,
            followersCount: followersResult.Count || 0,
            followingCount: followingResult.Count || 0
        };
        console.log('calculateUserStats: Final stats for user', userId, ':', stats);
        return stats;
    }
    catch (error) {
        console.error('Error calculating user stats:', error);
        return {
            postsCount: 0,
            likesCount: 0,
            followersCount: 0,
            followingCount: 0
        };
    }
};
// Get user profile
const getProfile = async (event) => {
    try {
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Get user from Users table
        const getUserCommand = new lib_dynamodb_1.GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });
        const userResult = await dynamodb.send(getUserCommand);
        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }
        // Get user profile from UserProfiles table
        const getProfileCommand = new lib_dynamodb_1.GetCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: userId }
        });
        const profileResult = await dynamodb.send(getProfileCommand);
        const user = userResult.Item;
        const profile = profileResult.Item || {};
        // Calculate real-time statistics
        const stats = await calculateUserStats(userId);
        return createResponse(200, {
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                bio: profile.bio || '',
                avatarUrl: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || '',
                followersCount: stats.followersCount,
                followingCount: stats.followingCount,
                postsCount: stats.postsCount,
                likesCount: stats.likesCount,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            },
            stats: {
                posts: stats.postsCount,
                followers: stats.followersCount,
                following: stats.followingCount,
                likes: stats.likesCount
            }
        });
    }
    catch (error) {
        console.error('GetProfile error:', error);
        return createResponse(500, { error: 'Failed to get profile', details: error.message });
    }
};
// Update user profile
const updateProfile = async (event) => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { firstName, lastName, bio, location, website, avatarUrl } = JSON.parse(event.body);
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Update Users table
        const userUpdateExpression = [];
        const userExpressionAttributeValues = {};
        if (firstName !== undefined) {
            userUpdateExpression.push('firstName = :firstName');
            userExpressionAttributeValues[':firstName'] = firstName;
        }
        if (lastName !== undefined) {
            userUpdateExpression.push('lastName = :lastName');
            userExpressionAttributeValues[':lastName'] = lastName;
        }
        userUpdateExpression.push('updatedAt = :updatedAt');
        userExpressionAttributeValues[':updatedAt'] = new Date().toISOString();
        if (userUpdateExpression.length > 1) { // More than just updated_at
            const updateUserCommand = new lib_dynamodb_1.UpdateCommand({
                TableName: USERS_TABLE,
                Key: { id: userId },
                UpdateExpression: `SET ${userUpdateExpression.join(', ')}`,
                ExpressionAttributeValues: userExpressionAttributeValues
            });
            await dynamodb.send(updateUserCommand);
        }
        // Update or create UserProfiles table entry
        const profileUpdateExpression = [];
        const profileExpressionAttributeValues = {};
        if (bio !== undefined) {
            profileUpdateExpression.push('bio = :bio');
            profileExpressionAttributeValues[':bio'] = bio;
        }
        if (location !== undefined) {
            profileUpdateExpression.push('#location = :location');
            profileExpressionAttributeValues[':location'] = location;
        }
        if (website !== undefined) {
            profileUpdateExpression.push('website = :website');
            profileExpressionAttributeValues[':website'] = website;
        }
        if (avatarUrl !== undefined) {
            profileUpdateExpression.push('avatarUrl = :avatarUrl');
            profileExpressionAttributeValues[':avatarUrl'] = avatarUrl;
        }
        profileUpdateExpression.push('updatedAt = :updatedAt');
        profileExpressionAttributeValues[':updatedAt'] = new Date().toISOString();
        if (profileUpdateExpression.length > 0) {
            const updateProfileCommand = new lib_dynamodb_1.UpdateCommand({
                TableName: USER_PROFILES_TABLE,
                Key: { user_id: userId },
                UpdateExpression: `SET ${profileUpdateExpression.join(', ')}`,
                ExpressionAttributeNames: location !== undefined ? { '#location': 'location' } : undefined,
                ExpressionAttributeValues: profileExpressionAttributeValues
            });
            await dynamodb.send(updateProfileCommand);
        }
        return createResponse(200, { message: 'Profile updated successfully' });
    }
    catch (error) {
        console.error('UpdateProfile error:', error);
        return createResponse(500, { error: 'Failed to update profile', details: error.message });
    }
};
// Get user by ID
const getUser = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }
        const getUserCommand = new lib_dynamodb_1.GetCommand({
            TableName: USERS_TABLE,
            Key: { id }
        });
        const userResult = await dynamodb.send(getUserCommand);
        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }
        const getProfileCommand = new lib_dynamodb_1.GetCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { userId: id }
        });
        const profileResult = await dynamodb.send(getProfileCommand);
        const user = userResult.Item;
        const profile = profileResult.Item || {};
        // Calculate real-time statistics
        const stats = await calculateUserStats(id);
        return createResponse(200, {
            user: {
                id: user.id,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                bio: profile.bio || '',
                avatarUrl: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || '',
                followersCount: stats.followersCount,
                followingCount: stats.followingCount,
                postsCount: stats.postsCount,
                likesCount: stats.likesCount,
                createdAt: user.createdAt
            },
            stats: {
                posts: stats.postsCount,
                followers: stats.followersCount,
                following: stats.followingCount,
                likes: stats.likesCount
            }
        });
    }
    catch (error) {
        console.error('GetUser error:', error);
        return createResponse(500, { error: 'Failed to get user', details: error.message });
    }
};
// Follow user
const followUser = async (event) => {
    try {
        const { id } = event.pathParameters || {}; // user to follow
        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        if (userId === id) {
            return createResponse(400, { error: 'Cannot follow yourself' });
        }
        // Check if already following
        const getFollowCommand = new lib_dynamodb_1.GetCommand({
            TableName: FOLLOWS_TABLE,
            Key: { follower_id: userId, following_id: id }
        });
        const existingFollow = await dynamodb.send(getFollowCommand);
        if (existingFollow.Item) {
            return createResponse(400, { error: 'Already following this user' });
        }
        // Add follow relationship
        const putFollowCommand = new lib_dynamodb_1.PutCommand({
            TableName: FOLLOWS_TABLE,
            Item: {
                follower_id: userId,
                following_id: id,
                created_at: new Date().toISOString()
            }
        });
        await dynamodb.send(putFollowCommand);
        // Update follower's following count
        const updateFollowerCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: userId },
            UpdateExpression: 'ADD followingCount :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updateFollowerCommand);
        // Update followed user's followers count
        const updateFollowedCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: id },
            UpdateExpression: 'ADD followersCount :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updateFollowedCommand);
        return createResponse(200, { message: 'User followed successfully' });
    }
    catch (error) {
        console.error('FollowUser error:', error);
        return createResponse(500, { error: 'Failed to follow user', details: error.message });
    }
};
// Unfollow user
const unfollowUser = async (event) => {
    try {
        const { id } = event.pathParameters || {}; // user to unfollow
        if (!id) {
            return createResponse(400, { error: 'User ID is required' });
        }
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Remove follow relationship
        const deleteFollowCommand = new lib_dynamodb_1.DeleteCommand({
            TableName: FOLLOWS_TABLE,
            Key: { follower_id: userId, following_id: id }
        });
        await dynamodb.send(deleteFollowCommand);
        // Update follower's following count
        const updateFollowerCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: userId },
            UpdateExpression: 'ADD followingCount :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updateFollowerCommand);
        // Update followed user's followers count
        const updateFollowedCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: id },
            UpdateExpression: 'ADD followersCount :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updateFollowedCommand);
        return createResponse(200, { message: 'User unfollowed successfully' });
    }
    catch (error) {
        console.error('UnfollowUser error:', error);
        return createResponse(500, { error: 'Failed to unfollow user', details: error.message });
    }
};
// Get user's liked posts
const getUserLikedPosts = async (event) => {
    try {
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Parse query parameters
        const queryParams = event.queryStringParameters || {};
        const limit = parseInt(queryParams.limit || '20') || 20;
        const lastEvaluatedKey = queryParams.lastEvaluatedKey ? JSON.parse(decodeURIComponent(queryParams.lastEvaluatedKey)) : undefined;
        // Debug: Log the user ID being used
        console.log('getUserLikedPosts: Looking for likes for user ID:', userId);
        console.log('getUserLikedPosts: User ID type:', typeof userId);
        console.log('getUserLikedPosts: LIKES_TABLE:', LIKES_TABLE);
        console.log('getUserLikedPosts: lastEvaluatedKey:', lastEvaluatedKey);
        // Build query parameters
        const queryParams_db = {
            TableName: LIKES_TABLE,
            IndexName: 'userId-index',
            KeyConditionExpression: 'userId = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            ScanIndexForward: false, // Sort by most recent first
            Limit: limit
        };
        // Add pagination if provided
        if (lastEvaluatedKey) {
            queryParams_db.ExclusiveStartKey = lastEvaluatedKey;
        }
        // Get user's likes using GSI
        const queryLikesCommand = new lib_dynamodb_1.QueryCommand(queryParams_db);
        const likesResult = await dynamodb.send(queryLikesCommand);
        console.log('getUserLikedPosts: Found', likesResult.Items?.length || 0, 'likes for user', userId);
        console.log('getUserLikedPosts: Raw likes result:', JSON.stringify(likesResult.Items, null, 2));
        const likes = likesResult.Items || [];
        // Get post details for each like
        const likedPosts = await Promise.all(likes.map(async (like) => {
            try {
                // Get post details
                console.log('getUserLikedPosts: Getting post details for post ID:', like.postId);
                console.log('getUserLikedPosts: POSTS_TABLE:', POSTS_TABLE);
                console.log('getUserLikedPosts: DynamoDB get params:', JSON.stringify({
                    TableName: POSTS_TABLE,
                    Key: { id: like.postId }
                }, null, 2));
                console.log('getUserLikedPosts: AWS region:', process.env.AWS_REGION);
                // Use raw DynamoDB client (DocumentClient has issues in this function)
                const getPostCommand = new client_dynamodb_1.GetItemCommand({
                    TableName: POSTS_TABLE,
                    Key: (0, util_dynamodb_1.marshall)({ id: like.postId })
                });
                const rawPostResult = await dynamodbClient.send(getPostCommand);
                console.log('getUserLikedPosts: Raw DynamoDB result:', JSON.stringify(rawPostResult, null, 2));
                // Convert raw result to DocumentClient format
                const postResult = {
                    Item: rawPostResult.Item ? (0, util_dynamodb_1.unmarshall)(rawPostResult.Item) : null
                };
                if (!postResult.Item) {
                    return null; // Post might have been deleted
                }
                const post = postResult.Item;
                // Get user details for the post author using raw DynamoDB client
                // Use authorId or userId field (posts have both)
                const authorId = post.authorId || post.userId;
                console.log('getUserLikedPosts: Author ID:', authorId);
                let user = {};
                if (authorId) {
                    try {
                        const getUserCommand = new client_dynamodb_1.GetItemCommand({
                            TableName: USERS_TABLE,
                            Key: (0, util_dynamodb_1.marshall)({ id: authorId })
                        });
                        const rawUserResult = await dynamodbClient.send(getUserCommand);
                        user = rawUserResult.Item ? (0, util_dynamodb_1.unmarshall)(rawUserResult.Item) : {};
                        console.log('getUserLikedPosts: User lookup result:', JSON.stringify(user, null, 2));
                    }
                    catch (userError) {
                        console.error('getUserLikedPosts: Error getting user details:', userError);
                        user = {}; // Use empty user object if lookup fails
                    }
                }
                else {
                    console.log('getUserLikedPosts: No author ID found for post');
                }
                return {
                    id: like.postId,
                    userId: authorId,
                    content: post.content,
                    mediaUrl: post.media_url,
                    mediaType: post.media_type,
                    likeCount: post.likes || 0,
                    commentCount: post.comments || 0,
                    createdAt: post.createdAt,
                    likedAt: like.createdAt,
                    username: user.username,
                    displayName: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.username,
                    avatarUrl: null, // Would need to get from UserProfiles table if needed
                    isLikedByCurrentUser: true // Always true since these are the user's likes
                };
            }
            catch (error) {
                console.error(`Error getting post details for like ${like.postId}:`, error);
                return null;
            }
        }));
        // Filter out null results (deleted posts)
        const validLikedPosts = likedPosts.filter(post => post !== null);
        // Prepare response with proper pagination
        const response = {
            posts: validLikedPosts,
            count: validLikedPosts.length,
            hasMore: !!likesResult.LastEvaluatedKey
        };
        // Include pagination token if there are more results
        if (likesResult.LastEvaluatedKey) {
            response.lastEvaluatedKey = encodeURIComponent(JSON.stringify(likesResult.LastEvaluatedKey));
        }
        return createResponse(200, response);
    }
    catch (error) {
        console.error('GetUserLikedPosts error:', error);
        return createResponse(500, { error: 'Failed to get liked posts', details: error.message });
    }
};
// Main handler
const handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    const { httpMethod, path, pathParameters } = event;
    try {
        if (httpMethod === 'GET' && path === '/users/profile') {
            return await getProfile(event);
        }
        else if (httpMethod === 'PUT' && path === '/users/profile') {
            return await updateProfile(event);
        }
        else if (httpMethod === 'GET' && path === '/users/profile/liked-posts') {
            return await getUserLikedPosts(event);
        }
        else if (httpMethod === 'GET' && pathParameters && pathParameters.id) {
            return await getUser(event);
        }
        else if (httpMethod === 'POST' && path.includes('/follow')) {
            return await followUser(event);
        }
        else if (httpMethod === 'DELETE' && path.includes('/follow')) {
            return await unfollowUser(event);
        }
        else {
            return createResponse(404, { error: 'Not found' });
        }
    }
    catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
exports.handler = handler;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyJpbmRleC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSw4REFBMEU7QUFDMUUsd0RBQWdKO0FBQ2hKLGtEQUE4QztBQUM5QywwREFBOEQ7QUFJOUQsK0JBQStCO0FBQy9CLE1BQU0sU0FBUyxHQUFHO0lBQ2QsTUFBTSxFQUFFLE9BQU8sQ0FBQyxHQUFHLENBQUMsVUFBVSxJQUFJLFdBQVc7Q0FDaEQsQ0FBQztBQUVGLE1BQU0sY0FBYyxHQUFHLElBQUksZ0NBQWMsQ0FBQyxTQUFTLENBQUMsQ0FBQztBQUNyRCxNQUFNLFFBQVEsR0FBRyxxQ0FBc0IsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7QUFDN0QsTUFBTSxFQUFFLEdBQUcsSUFBSSxvQkFBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDO0FBRW5DLE1BQU0sV0FBVyxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDO0FBQzVDLE1BQU0sbUJBQW1CLEdBQUcsT0FBTyxDQUFDLEdBQUcsQ0FBQyxtQkFBbUIsQ0FBQztBQUM1RCxNQUFNLGFBQWEsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLGFBQWEsQ0FBQztBQUNoRCxNQUFNLFdBQVcsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLFdBQVcsQ0FBQztBQUM1QyxNQUFNLFdBQVcsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLFdBQVcsQ0FBQztBQUM1QyxNQUFNLGNBQWMsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLGNBQWMsQ0FBQztBQWdEbEQscUNBQXFDO0FBQ3JDLE1BQU0sY0FBYyxHQUFHLENBQUMsVUFBa0IsRUFBRSxJQUFTLEVBQXlCLEVBQUUsQ0FBQyxDQUFDO0lBQzlFLFVBQVU7SUFDVixPQUFPLEVBQUU7UUFDTCxjQUFjLEVBQUUsa0JBQWtCO1FBQ2xDLDZCQUE2QixFQUFFLEdBQUc7UUFDbEMsOEJBQThCLEVBQUUsc0VBQXNFO1FBQ3RHLDhCQUE4QixFQUFFLDZCQUE2QjtLQUNoRTtJQUNELElBQUksRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQztDQUM3QixDQUFDLENBQUM7QUFFSCx5REFBeUQ7QUFDekQsTUFBTSxvQkFBb0IsR0FBRyxDQUFDLEtBQTJCLEVBQWlCLEVBQUU7SUFDeEUsc0VBQXNFO0lBQ3RFLElBQUksS0FBSyxDQUFDLGNBQWMsSUFBSSxLQUFLLENBQUMsY0FBYyxDQUFDLFVBQVUsRUFBRSxDQUFDO1FBQzFELE9BQVEsS0FBSyxDQUFDLGNBQWMsQ0FBQyxVQUFrQixDQUFDLE1BQU0sQ0FBQztJQUMzRCxDQUFDO0lBQ0QsT0FBTyxJQUFJLENBQUM7QUFDaEIsQ0FBQyxDQUFDO0FBRUYsK0NBQStDO0FBQy9DLE1BQU0sa0JBQWtCLEdBQUcsS0FBSyxFQUFFLE1BQWMsRUFBc0IsRUFBRTtJQUNwRSxJQUFJLENBQUM7UUFDRCxPQUFPLENBQUMsR0FBRyxDQUFDLG9EQUFvRCxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBRTFFLHNCQUFzQjtRQUN0QixNQUFNLGdCQUFnQixHQUFHLElBQUksMEJBQVcsQ0FBQztZQUNyQyxTQUFTLEVBQUUsV0FBVztZQUN0QixnQkFBZ0IsRUFBRSw2Q0FBNkM7WUFDL0QseUJBQXlCLEVBQUU7Z0JBQ3ZCLFNBQVMsRUFBRSxNQUFNO2dCQUNqQixXQUFXLEVBQUUsSUFBSTthQUNwQjtZQUNELE1BQU0sRUFBRSxPQUFPO1NBQ2xCLENBQUMsQ0FBQztRQUNILE1BQU0sV0FBVyxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO1FBRTFELE9BQU8sQ0FBQyxHQUFHLENBQUMsMkJBQTJCLEVBQUUsV0FBVyxDQUFDLEtBQUssRUFBRSxnQkFBZ0IsRUFBRSxNQUFNLENBQUMsQ0FBQztRQUV0RixrQ0FBa0M7UUFDbEMsTUFBTSxpQkFBaUIsR0FBRyxJQUFJLDJCQUFZLENBQUM7WUFDdkMsU0FBUyxFQUFFLFdBQVc7WUFDdEIsU0FBUyxFQUFFLGNBQWM7WUFDekIsc0JBQXNCLEVBQUUsa0JBQWtCO1lBQzFDLHlCQUF5QixFQUFFO2dCQUN2QixTQUFTLEVBQUUsTUFBTTthQUNwQjtZQUNELE1BQU0sRUFBRSxPQUFPO1NBQ2xCLENBQUMsQ0FBQztRQUNILE1BQU0sV0FBVyxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1FBRTNELE9BQU8sQ0FBQyxHQUFHLENBQUMsMkJBQTJCLEVBQUUsV0FBVyxDQUFDLEtBQUssRUFBRSxnQkFBZ0IsRUFBRSxNQUFNLENBQUMsQ0FBQztRQUV0RixrQkFBa0I7UUFDbEIsTUFBTSxvQkFBb0IsR0FBRyxJQUFJLDBCQUFXLENBQUM7WUFDekMsU0FBUyxFQUFFLGFBQWE7WUFDeEIsZ0JBQWdCLEVBQUUsdUJBQXVCO1lBQ3pDLHlCQUF5QixFQUFFO2dCQUN2QixTQUFTLEVBQUUsTUFBTTthQUNwQjtZQUNELE1BQU0sRUFBRSxPQUFPO1NBQ2xCLENBQUMsQ0FBQztRQUNILE1BQU0sZUFBZSxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1FBRWxFLGtCQUFrQjtRQUNsQixNQUFNLG9CQUFvQixHQUFHLElBQUksMEJBQVcsQ0FBQztZQUN6QyxTQUFTLEVBQUUsYUFBYTtZQUN4QixnQkFBZ0IsRUFBRSxzQkFBc0I7WUFDeEMseUJBQXlCLEVBQUU7Z0JBQ3ZCLFNBQVMsRUFBRSxNQUFNO2FBQ3BCO1lBQ0QsTUFBTSxFQUFFLE9BQU87U0FDbEIsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxlQUFlLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLENBQUM7UUFFbEUsTUFBTSxLQUFLLEdBQWM7WUFDckIsVUFBVSxFQUFFLFdBQVcsQ0FBQyxLQUFLLElBQUksQ0FBQztZQUNsQyxVQUFVLEVBQUUsV0FBVyxDQUFDLEtBQUssSUFBSSxDQUFDO1lBQ2xDLGNBQWMsRUFBRSxlQUFlLENBQUMsS0FBSyxJQUFJLENBQUM7WUFDMUMsY0FBYyxFQUFFLGVBQWUsQ0FBQyxLQUFLLElBQUksQ0FBQztTQUM3QyxDQUFDO1FBRUYsT0FBTyxDQUFDLEdBQUcsQ0FBQywwQ0FBMEMsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLEtBQUssQ0FBQyxDQUFDO1FBRTVFLE9BQU8sS0FBSyxDQUFDO0lBQ2pCLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQywrQkFBK0IsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUN0RCxPQUFPO1lBQ0gsVUFBVSxFQUFFLENBQUM7WUFDYixVQUFVLEVBQUUsQ0FBQztZQUNiLGNBQWMsRUFBRSxDQUFDO1lBQ2pCLGNBQWMsRUFBRSxDQUFDO1NBQ3BCLENBQUM7SUFDTixDQUFDO0FBQ0wsQ0FBQyxDQUFDO0FBRUYsbUJBQW1CO0FBQ25CLE1BQU0sVUFBVSxHQUFHLEtBQUssRUFBRSxLQUEyQixFQUFrQyxFQUFFO0lBQ3JGLElBQUksQ0FBQztRQUNELHNDQUFzQztRQUN0QyxNQUFNLE1BQU0sR0FBRyxvQkFBb0IsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUUzQyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUM7WUFDVixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsd0JBQXdCLEVBQUUsQ0FBQyxDQUFDO1FBQ3BFLENBQUM7UUFFRCw0QkFBNEI7UUFDNUIsTUFBTSxjQUFjLEdBQUcsSUFBSSx5QkFBVSxDQUFDO1lBQ2xDLFNBQVMsRUFBRSxXQUFXO1lBQ3RCLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRSxNQUFNLEVBQUU7U0FDdEIsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxVQUFVLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1FBRXZELElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDbkIsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLGdCQUFnQixFQUFFLENBQUMsQ0FBQztRQUM1RCxDQUFDO1FBRUQsMkNBQTJDO1FBQzNDLE1BQU0saUJBQWlCLEdBQUcsSUFBSSx5QkFBVSxDQUFDO1lBQ3JDLFNBQVMsRUFBRSxtQkFBbUI7WUFDOUIsR0FBRyxFQUFFLEVBQUUsTUFBTSxFQUFFLE1BQU0sRUFBRTtTQUMxQixDQUFDLENBQUM7UUFDSCxNQUFNLGFBQWEsR0FBRyxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQztRQUU3RCxNQUFNLElBQUksR0FBRyxVQUFVLENBQUMsSUFBWSxDQUFDO1FBQ3JDLE1BQU0sT0FBTyxHQUFJLGFBQWEsQ0FBQyxJQUFvQixJQUFJLEVBQUUsQ0FBQztRQUUxRCxpQ0FBaUM7UUFDakMsTUFBTSxLQUFLLEdBQUcsTUFBTSxrQkFBa0IsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUUvQyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUU7WUFDdkIsSUFBSSxFQUFFO2dCQUNGLEVBQUUsRUFBRSxJQUFJLENBQUMsRUFBRTtnQkFDWCxLQUFLLEVBQUUsSUFBSSxDQUFDLEtBQUs7Z0JBQ2pCLFFBQVEsRUFBRSxJQUFJLENBQUMsUUFBUTtnQkFDdkIsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTO2dCQUN6QixRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVE7Z0JBQ3ZCLEdBQUcsRUFBRSxPQUFPLENBQUMsR0FBRyxJQUFJLEVBQUU7Z0JBQ3RCLFNBQVMsRUFBRSxPQUFPLENBQUMsU0FBUyxJQUFJLElBQUk7Z0JBQ3BDLFFBQVEsRUFBRSxPQUFPLENBQUMsUUFBUSxJQUFJLEVBQUU7Z0JBQ2hDLE9BQU8sRUFBRSxPQUFPLENBQUMsT0FBTyxJQUFJLEVBQUU7Z0JBQzlCLGNBQWMsRUFBRSxLQUFLLENBQUMsY0FBYztnQkFDcEMsY0FBYyxFQUFFLEtBQUssQ0FBQyxjQUFjO2dCQUNwQyxVQUFVLEVBQUUsS0FBSyxDQUFDLFVBQVU7Z0JBQzVCLFVBQVUsRUFBRSxLQUFLLENBQUMsVUFBVTtnQkFDNUIsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTO2dCQUN6QixTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVM7YUFDNUI7WUFDRCxLQUFLLEVBQUU7Z0JBQ0gsS0FBSyxFQUFFLEtBQUssQ0FBQyxVQUFVO2dCQUN2QixTQUFTLEVBQUUsS0FBSyxDQUFDLGNBQWM7Z0JBQy9CLFNBQVMsRUFBRSxLQUFLLENBQUMsY0FBYztnQkFDL0IsS0FBSyxFQUFFLEtBQUssQ0FBQyxVQUFVO2FBQzFCO1NBQ0osQ0FBQyxDQUFDO0lBRVAsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDYixPQUFPLENBQUMsS0FBSyxDQUFDLG1CQUFtQixFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQzFDLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSx1QkFBdUIsRUFBRSxPQUFPLEVBQUcsS0FBZSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDdEcsQ0FBQztBQUNMLENBQUMsQ0FBQztBQUVGLHNCQUFzQjtBQUN0QixNQUFNLGFBQWEsR0FBRyxLQUFLLEVBQUUsS0FBMkIsRUFBa0MsRUFBRTtJQUN4RixJQUFJLENBQUM7UUFDRCxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ2QsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLDBCQUEwQixFQUFFLENBQUMsQ0FBQztRQUN0RSxDQUFDO1FBRUQsTUFBTSxFQUFFLFNBQVMsRUFBRSxRQUFRLEVBQUUsR0FBRyxFQUFFLFFBQVEsRUFBRSxPQUFPLEVBQUUsU0FBUyxFQUFFLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUM7UUFFMUYsc0NBQXNDO1FBQ3RDLE1BQU0sTUFBTSxHQUFHLG9CQUFvQixDQUFDLEtBQUssQ0FBQyxDQUFDO1FBRTNDLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQztZQUNWLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSx3QkFBd0IsRUFBRSxDQUFDLENBQUM7UUFDcEUsQ0FBQztRQUVELHFCQUFxQjtRQUNyQixNQUFNLG9CQUFvQixHQUFhLEVBQUUsQ0FBQztRQUMxQyxNQUFNLDZCQUE2QixHQUF3QixFQUFFLENBQUM7UUFFOUQsSUFBSSxTQUFTLEtBQUssU0FBUyxFQUFFLENBQUM7WUFDMUIsb0JBQW9CLENBQUMsSUFBSSxDQUFDLHdCQUF3QixDQUFDLENBQUM7WUFDcEQsNkJBQTZCLENBQUMsWUFBWSxDQUFDLEdBQUcsU0FBUyxDQUFDO1FBQzVELENBQUM7UUFFRCxJQUFJLFFBQVEsS0FBSyxTQUFTLEVBQUUsQ0FBQztZQUN6QixvQkFBb0IsQ0FBQyxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztZQUNsRCw2QkFBNkIsQ0FBQyxXQUFXLENBQUMsR0FBRyxRQUFRLENBQUM7UUFDMUQsQ0FBQztRQUVELG9CQUFvQixDQUFDLElBQUksQ0FBQyx3QkFBd0IsQ0FBQyxDQUFDO1FBQ3BELDZCQUE2QixDQUFDLFlBQVksQ0FBQyxHQUFHLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFLENBQUM7UUFFdkUsSUFBSSxvQkFBb0IsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyw0QkFBNEI7WUFDL0QsTUFBTSxpQkFBaUIsR0FBRyxJQUFJLDRCQUFhLENBQUM7Z0JBQ3hDLFNBQVMsRUFBRSxXQUFXO2dCQUN0QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFO2dCQUNuQixnQkFBZ0IsRUFBRSxPQUFPLG9CQUFvQixDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDMUQseUJBQXlCLEVBQUUsNkJBQTZCO2FBQzNELENBQUMsQ0FBQztZQUNILE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1FBQzNDLENBQUM7UUFFRCw0Q0FBNEM7UUFDNUMsTUFBTSx1QkFBdUIsR0FBYSxFQUFFLENBQUM7UUFDN0MsTUFBTSxnQ0FBZ0MsR0FBd0IsRUFBRSxDQUFDO1FBRWpFLElBQUksR0FBRyxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQ3BCLHVCQUF1QixDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUMzQyxnQ0FBZ0MsQ0FBQyxNQUFNLENBQUMsR0FBRyxHQUFHLENBQUM7UUFDbkQsQ0FBQztRQUVELElBQUksUUFBUSxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQ3pCLHVCQUF1QixDQUFDLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDO1lBQ3RELGdDQUFnQyxDQUFDLFdBQVcsQ0FBQyxHQUFHLFFBQVEsQ0FBQztRQUM3RCxDQUFDO1FBRUQsSUFBSSxPQUFPLEtBQUssU0FBUyxFQUFFLENBQUM7WUFDeEIsdUJBQXVCLENBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLENBQUM7WUFDbkQsZ0NBQWdDLENBQUMsVUFBVSxDQUFDLEdBQUcsT0FBTyxDQUFDO1FBQzNELENBQUM7UUFFRCxJQUFJLFNBQVMsS0FBSyxTQUFTLEVBQUUsQ0FBQztZQUMxQix1QkFBdUIsQ0FBQyxJQUFJLENBQUMsd0JBQXdCLENBQUMsQ0FBQztZQUN2RCxnQ0FBZ0MsQ0FBQyxZQUFZLENBQUMsR0FBRyxTQUFTLENBQUM7UUFDL0QsQ0FBQztRQUVELHVCQUF1QixDQUFDLElBQUksQ0FBQyx3QkFBd0IsQ0FBQyxDQUFDO1FBQ3ZELGdDQUFnQyxDQUFDLFlBQVksQ0FBQyxHQUFHLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFLENBQUM7UUFFMUUsSUFBSSx1QkFBdUIsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDckMsTUFBTSxvQkFBb0IsR0FBRyxJQUFJLDRCQUFhLENBQUM7Z0JBQzNDLFNBQVMsRUFBRSxtQkFBbUI7Z0JBQzlCLEdBQUcsRUFBRSxFQUFFLE9BQU8sRUFBRSxNQUFNLEVBQUU7Z0JBQ3hCLGdCQUFnQixFQUFFLE9BQU8sdUJBQXVCLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUM3RCx3QkFBd0IsRUFBRSxRQUFRLEtBQUssU0FBUyxDQUFDLENBQUMsQ0FBQyxFQUFFLFdBQVcsRUFBRSxVQUFVLEVBQUUsQ0FBQyxDQUFDLENBQUMsU0FBUztnQkFDMUYseUJBQXlCLEVBQUUsZ0NBQWdDO2FBQzlELENBQUMsQ0FBQztZQUNILE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1FBQzlDLENBQUM7UUFFRCxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxPQUFPLEVBQUUsOEJBQThCLEVBQUUsQ0FBQyxDQUFDO0lBRTVFLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxzQkFBc0IsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUM3QyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsMEJBQTBCLEVBQUUsT0FBTyxFQUFHLEtBQWUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQ3pHLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRixpQkFBaUI7QUFDakIsTUFBTSxPQUFPLEdBQUcsS0FBSyxFQUFFLEtBQTJCLEVBQWtDLEVBQUU7SUFDbEYsSUFBSSxDQUFDO1FBQ0QsTUFBTSxFQUFFLEVBQUUsRUFBRSxHQUFHLEtBQUssQ0FBQyxjQUFjLElBQUksRUFBRSxDQUFDO1FBRTFDLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQztZQUNOLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSxxQkFBcUIsRUFBRSxDQUFDLENBQUM7UUFDakUsQ0FBQztRQUVELE1BQU0sY0FBYyxHQUFHLElBQUkseUJBQVUsQ0FBQztZQUNsQyxTQUFTLEVBQUUsV0FBVztZQUN0QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUU7U0FDZCxDQUFDLENBQUM7UUFDSCxNQUFNLFVBQVUsR0FBRyxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7UUFFdkQsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNuQixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsZ0JBQWdCLEVBQUUsQ0FBQyxDQUFDO1FBQzVELENBQUM7UUFFRCxNQUFNLGlCQUFpQixHQUFHLElBQUkseUJBQVUsQ0FBQztZQUNyQyxTQUFTLEVBQUUsbUJBQW1CO1lBQzlCLEdBQUcsRUFBRSxFQUFFLE1BQU0sRUFBRSxFQUFFLEVBQUU7U0FDdEIsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxhQUFhLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUM7UUFFN0QsTUFBTSxJQUFJLEdBQUcsVUFBVSxDQUFDLElBQVksQ0FBQztRQUNyQyxNQUFNLE9BQU8sR0FBSSxhQUFhLENBQUMsSUFBb0IsSUFBSSxFQUFFLENBQUM7UUFFMUQsaUNBQWlDO1FBQ2pDLE1BQU0sS0FBSyxHQUFHLE1BQU0sa0JBQWtCLENBQUMsRUFBRSxDQUFDLENBQUM7UUFFM0MsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFO1lBQ3ZCLElBQUksRUFBRTtnQkFDRixFQUFFLEVBQUUsSUFBSSxDQUFDLEVBQUU7Z0JBQ1gsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO2dCQUN2QixTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVM7Z0JBQ3pCLFFBQVEsRUFBRSxJQUFJLENBQUMsUUFBUTtnQkFDdkIsR0FBRyxFQUFFLE9BQU8sQ0FBQyxHQUFHLElBQUksRUFBRTtnQkFDdEIsU0FBUyxFQUFFLE9BQU8sQ0FBQyxTQUFTLElBQUksSUFBSTtnQkFDcEMsUUFBUSxFQUFFLE9BQU8sQ0FBQyxRQUFRLElBQUksRUFBRTtnQkFDaEMsT0FBTyxFQUFFLE9BQU8sQ0FBQyxPQUFPLElBQUksRUFBRTtnQkFDOUIsY0FBYyxFQUFFLEtBQUssQ0FBQyxjQUFjO2dCQUNwQyxjQUFjLEVBQUUsS0FBSyxDQUFDLGNBQWM7Z0JBQ3BDLFVBQVUsRUFBRSxLQUFLLENBQUMsVUFBVTtnQkFDNUIsVUFBVSxFQUFFLEtBQUssQ0FBQyxVQUFVO2dCQUM1QixTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVM7YUFDNUI7WUFDRCxLQUFLLEVBQUU7Z0JBQ0gsS0FBSyxFQUFFLEtBQUssQ0FBQyxVQUFVO2dCQUN2QixTQUFTLEVBQUUsS0FBSyxDQUFDLGNBQWM7Z0JBQy9CLFNBQVMsRUFBRSxLQUFLLENBQUMsY0FBYztnQkFDL0IsS0FBSyxFQUFFLEtBQUssQ0FBQyxVQUFVO2FBQzFCO1NBQ0osQ0FBQyxDQUFDO0lBRVAsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDYixPQUFPLENBQUMsS0FBSyxDQUFDLGdCQUFnQixFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQ3ZDLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSxvQkFBb0IsRUFBRSxPQUFPLEVBQUcsS0FBZSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDbkcsQ0FBQztBQUNMLENBQUMsQ0FBQztBQUVGLGNBQWM7QUFDZCxNQUFNLFVBQVUsR0FBRyxLQUFLLEVBQUUsS0FBMkIsRUFBa0MsRUFBRTtJQUNyRixJQUFJLENBQUM7UUFDRCxNQUFNLEVBQUUsRUFBRSxFQUFFLEdBQUcsS0FBSyxDQUFDLGNBQWMsSUFBSSxFQUFFLENBQUMsQ0FBQyxpQkFBaUI7UUFFNUQsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ04sT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztRQUNqRSxDQUFDO1FBRUQsc0NBQXNDO1FBQ3RDLE1BQU0sTUFBTSxHQUFHLG9CQUFvQixDQUFDLEtBQUssQ0FBQyxDQUFDO1FBRTNDLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQztZQUNWLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSx3QkFBd0IsRUFBRSxDQUFDLENBQUM7UUFDcEUsQ0FBQztRQUVELElBQUksTUFBTSxLQUFLLEVBQUUsRUFBRSxDQUFDO1lBQ2hCLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSx3QkFBd0IsRUFBRSxDQUFDLENBQUM7UUFDcEUsQ0FBQztRQUVELDZCQUE2QjtRQUM3QixNQUFNLGdCQUFnQixHQUFHLElBQUkseUJBQVUsQ0FBQztZQUNwQyxTQUFTLEVBQUUsYUFBYTtZQUN4QixHQUFHLEVBQUUsRUFBRSxXQUFXLEVBQUUsTUFBTSxFQUFFLFlBQVksRUFBRSxFQUFFLEVBQUU7U0FDakQsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxjQUFjLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUM7UUFFN0QsSUFBSSxjQUFjLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDdEIsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLDZCQUE2QixFQUFFLENBQUMsQ0FBQztRQUN6RSxDQUFDO1FBRUQsMEJBQTBCO1FBQzFCLE1BQU0sZ0JBQWdCLEdBQUcsSUFBSSx5QkFBVSxDQUFDO1lBQ3BDLFNBQVMsRUFBRSxhQUFhO1lBQ3hCLElBQUksRUFBRTtnQkFDRixXQUFXLEVBQUUsTUFBTTtnQkFDbkIsWUFBWSxFQUFFLEVBQUU7Z0JBQ2hCLFVBQVUsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRTthQUN2QztTQUNKLENBQUMsQ0FBQztRQUNILE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO1FBRXRDLG9DQUFvQztRQUNwQyxNQUFNLHFCQUFxQixHQUFHLElBQUksNEJBQWEsQ0FBQztZQUM1QyxTQUFTLEVBQUUsbUJBQW1CO1lBQzlCLEdBQUcsRUFBRSxFQUFFLE9BQU8sRUFBRSxNQUFNLEVBQUU7WUFDeEIsZ0JBQWdCLEVBQUUseUJBQXlCO1lBQzNDLHlCQUF5QixFQUFFLEVBQUUsTUFBTSxFQUFFLENBQUMsRUFBRTtTQUMzQyxDQUFDLENBQUM7UUFDSCxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUUzQyx5Q0FBeUM7UUFDekMsTUFBTSxxQkFBcUIsR0FBRyxJQUFJLDRCQUFhLENBQUM7WUFDNUMsU0FBUyxFQUFFLG1CQUFtQjtZQUM5QixHQUFHLEVBQUUsRUFBRSxPQUFPLEVBQUUsRUFBRSxFQUFFO1lBQ3BCLGdCQUFnQixFQUFFLHlCQUF5QjtZQUMzQyx5QkFBeUIsRUFBRSxFQUFFLE1BQU0sRUFBRSxDQUFDLEVBQUU7U0FDM0MsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLHFCQUFxQixDQUFDLENBQUM7UUFFM0MsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsT0FBTyxFQUFFLDRCQUE0QixFQUFFLENBQUMsQ0FBQztJQUUxRSxDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsbUJBQW1CLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDMUMsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHVCQUF1QixFQUFFLE9BQU8sRUFBRyxLQUFlLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztJQUN0RyxDQUFDO0FBQ0wsQ0FBQyxDQUFDO0FBRUYsZ0JBQWdCO0FBQ2hCLE1BQU0sWUFBWSxHQUFHLEtBQUssRUFBRSxLQUEyQixFQUFrQyxFQUFFO0lBQ3ZGLElBQUksQ0FBQztRQUNELE1BQU0sRUFBRSxFQUFFLEVBQUUsR0FBRyxLQUFLLENBQUMsY0FBYyxJQUFJLEVBQUUsQ0FBQyxDQUFDLG1CQUFtQjtRQUU5RCxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUM7WUFDTixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUscUJBQXFCLEVBQUUsQ0FBQyxDQUFDO1FBQ2pFLENBQUM7UUFFRCxzQ0FBc0M7UUFDdEMsTUFBTSxNQUFNLEdBQUcsb0JBQW9CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFM0MsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQ1YsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBRUQsNkJBQTZCO1FBQzdCLE1BQU0sbUJBQW1CLEdBQUcsSUFBSSw0QkFBYSxDQUFDO1lBQzFDLFNBQVMsRUFBRSxhQUFhO1lBQ3hCLEdBQUcsRUFBRSxFQUFFLFdBQVcsRUFBRSxNQUFNLEVBQUUsWUFBWSxFQUFFLEVBQUUsRUFBRTtTQUNqRCxDQUFDLENBQUM7UUFDSCxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQztRQUV6QyxvQ0FBb0M7UUFDcEMsTUFBTSxxQkFBcUIsR0FBRyxJQUFJLDRCQUFhLENBQUM7WUFDNUMsU0FBUyxFQUFFLG1CQUFtQjtZQUM5QixHQUFHLEVBQUUsRUFBRSxPQUFPLEVBQUUsTUFBTSxFQUFFO1lBQ3hCLGdCQUFnQixFQUFFLHlCQUF5QjtZQUMzQyx5QkFBeUIsRUFBRSxFQUFFLE1BQU0sRUFBRSxDQUFDLENBQUMsRUFBRTtTQUM1QyxDQUFDLENBQUM7UUFDSCxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUUzQyx5Q0FBeUM7UUFDekMsTUFBTSxxQkFBcUIsR0FBRyxJQUFJLDRCQUFhLENBQUM7WUFDNUMsU0FBUyxFQUFFLG1CQUFtQjtZQUM5QixHQUFHLEVBQUUsRUFBRSxPQUFPLEVBQUUsRUFBRSxFQUFFO1lBQ3BCLGdCQUFnQixFQUFFLHlCQUF5QjtZQUMzQyx5QkFBeUIsRUFBRSxFQUFFLE1BQU0sRUFBRSxDQUFDLENBQUMsRUFBRTtTQUM1QyxDQUFDLENBQUM7UUFDSCxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUUzQyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxPQUFPLEVBQUUsOEJBQThCLEVBQUUsQ0FBQyxDQUFDO0lBRTVFLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxxQkFBcUIsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUM1QyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUseUJBQXlCLEVBQUUsT0FBTyxFQUFHLEtBQWUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQ3hHLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRix5QkFBeUI7QUFDekIsTUFBTSxpQkFBaUIsR0FBRyxLQUFLLEVBQUUsS0FBMkIsRUFBa0MsRUFBRTtJQUM1RixJQUFJLENBQUM7UUFDRCxzQ0FBc0M7UUFDdEMsTUFBTSxNQUFNLEdBQUcsb0JBQW9CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFM0MsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQ1YsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBRUQseUJBQXlCO1FBQ3pCLE1BQU0sV0FBVyxHQUFHLEtBQUssQ0FBQyxxQkFBcUIsSUFBSSxFQUFFLENBQUM7UUFDdEQsTUFBTSxLQUFLLEdBQUcsUUFBUSxDQUFDLFdBQVcsQ0FBQyxLQUFLLElBQUksSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDO1FBQ3hELE1BQU0sZ0JBQWdCLEdBQUcsV0FBVyxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLGtCQUFrQixDQUFDLFdBQVcsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQztRQUVqSSxvQ0FBb0M7UUFDcEMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxtREFBbUQsRUFBRSxNQUFNLENBQUMsQ0FBQztRQUN6RSxPQUFPLENBQUMsR0FBRyxDQUFDLGtDQUFrQyxFQUFFLE9BQU8sTUFBTSxDQUFDLENBQUM7UUFDL0QsT0FBTyxDQUFDLEdBQUcsQ0FBQyxpQ0FBaUMsRUFBRSxXQUFXLENBQUMsQ0FBQztRQUM1RCxPQUFPLENBQUMsR0FBRyxDQUFDLHNDQUFzQyxFQUFFLGdCQUFnQixDQUFDLENBQUM7UUFFdEUseUJBQXlCO1FBQ3pCLE1BQU0sY0FBYyxHQUFRO1lBQ3hCLFNBQVMsRUFBRSxXQUFXO1lBQ3RCLFNBQVMsRUFBRSxjQUFjO1lBQ3pCLHNCQUFzQixFQUFFLGtCQUFrQjtZQUMxQyx5QkFBeUIsRUFBRTtnQkFDdkIsU0FBUyxFQUFFLE1BQU07YUFDcEI7WUFDRCxnQkFBZ0IsRUFBRSxLQUFLLEVBQUUsNEJBQTRCO1lBQ3JELEtBQUssRUFBRSxLQUFLO1NBQ2YsQ0FBQztRQUVGLDZCQUE2QjtRQUM3QixJQUFJLGdCQUFnQixFQUFFLENBQUM7WUFDbkIsY0FBYyxDQUFDLGlCQUFpQixHQUFHLGdCQUFnQixDQUFDO1FBQ3hELENBQUM7UUFFRCw2QkFBNkI7UUFDN0IsTUFBTSxpQkFBaUIsR0FBRyxJQUFJLDJCQUFZLENBQUMsY0FBYyxDQUFDLENBQUM7UUFDM0QsTUFBTSxXQUFXLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUM7UUFFM0QsT0FBTyxDQUFDLEdBQUcsQ0FBQywwQkFBMEIsRUFBRSxXQUFXLENBQUMsS0FBSyxFQUFFLE1BQU0sSUFBSSxDQUFDLEVBQUUsZ0JBQWdCLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFDbEcsT0FBTyxDQUFDLEdBQUcsQ0FBQyxzQ0FBc0MsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxLQUFLLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFFaEcsTUFBTSxLQUFLLEdBQUcsV0FBVyxDQUFDLEtBQUssSUFBSSxFQUFFLENBQUM7UUFFdEMsaUNBQWlDO1FBQ2pDLE1BQU0sVUFBVSxHQUFHLE1BQU0sT0FBTyxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLEtBQUssRUFBRSxJQUFTLEVBQUUsRUFBRTtZQUMvRCxJQUFJLENBQUM7Z0JBQ0QsbUJBQW1CO2dCQUNuQixPQUFPLENBQUMsR0FBRyxDQUFDLHNEQUFzRCxFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztnQkFDakYsT0FBTyxDQUFDLEdBQUcsQ0FBQyxpQ0FBaUMsRUFBRSxXQUFXLENBQUMsQ0FBQztnQkFDNUQsT0FBTyxDQUFDLEdBQUcsQ0FBQyx5Q0FBeUMsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDO29CQUNsRSxTQUFTLEVBQUUsV0FBVztvQkFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFFLElBQUksQ0FBQyxNQUFNLEVBQUU7aUJBQzNCLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7Z0JBQ2IsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnQ0FBZ0MsRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxDQUFDO2dCQUV0RSx1RUFBdUU7Z0JBQ3ZFLE1BQU0sY0FBYyxHQUFHLElBQUksZ0NBQWMsQ0FBQztvQkFDdEMsU0FBUyxFQUFFLFdBQVc7b0JBQ3RCLEdBQUcsRUFBRSxJQUFBLHdCQUFRLEVBQUMsRUFBRSxFQUFFLEVBQUUsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDO2lCQUNyQyxDQUFDLENBQUM7Z0JBQ0gsTUFBTSxhQUFhLEdBQUcsTUFBTSxjQUFjLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO2dCQUVoRSxPQUFPLENBQUMsR0FBRyxDQUFDLHlDQUF5QyxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxFQUFFLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO2dCQUUvRiw4Q0FBOEM7Z0JBQzlDLE1BQU0sVUFBVSxHQUFHO29CQUNmLElBQUksRUFBRSxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxJQUFBLDBCQUFVLEVBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJO2lCQUNuRSxDQUFDO2dCQUVGLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxFQUFFLENBQUM7b0JBQ25CLE9BQU8sSUFBSSxDQUFDLENBQUMsK0JBQStCO2dCQUNoRCxDQUFDO2dCQUVELE1BQU0sSUFBSSxHQUFHLFVBQVUsQ0FBQyxJQUFJLENBQUM7Z0JBRTdCLGlFQUFpRTtnQkFDakUsaURBQWlEO2dCQUNqRCxNQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsUUFBUSxJQUFJLElBQUksQ0FBQyxNQUFNLENBQUM7Z0JBQzlDLE9BQU8sQ0FBQyxHQUFHLENBQUMsK0JBQStCLEVBQUUsUUFBUSxDQUFDLENBQUM7Z0JBRXZELElBQUksSUFBSSxHQUFRLEVBQUUsQ0FBQztnQkFDbkIsSUFBSSxRQUFRLEVBQUUsQ0FBQztvQkFDWCxJQUFJLENBQUM7d0JBQ0QsTUFBTSxjQUFjLEdBQUcsSUFBSSxnQ0FBYyxDQUFDOzRCQUN0QyxTQUFTLEVBQUUsV0FBVzs0QkFDdEIsR0FBRyxFQUFFLElBQUEsd0JBQVEsRUFBQyxFQUFFLEVBQUUsRUFBRSxRQUFRLEVBQUUsQ0FBQzt5QkFDbEMsQ0FBQyxDQUFDO3dCQUNILE1BQU0sYUFBYSxHQUFHLE1BQU0sY0FBYyxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQzt3QkFDaEUsSUFBSSxHQUFHLGFBQWEsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUEsMEJBQVUsRUFBQyxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQzt3QkFDaEUsT0FBTyxDQUFDLEdBQUcsQ0FBQyx3Q0FBd0MsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztvQkFDekYsQ0FBQztvQkFBQyxPQUFPLFNBQVMsRUFBRSxDQUFDO3dCQUNqQixPQUFPLENBQUMsS0FBSyxDQUFDLGdEQUFnRCxFQUFFLFNBQVMsQ0FBQyxDQUFDO3dCQUMzRSxJQUFJLEdBQUcsRUFBRSxDQUFDLENBQUMsd0NBQXdDO29CQUN2RCxDQUFDO2dCQUNMLENBQUM7cUJBQU0sQ0FBQztvQkFDSixPQUFPLENBQUMsR0FBRyxDQUFDLGdEQUFnRCxDQUFDLENBQUM7Z0JBQ2xFLENBQUM7Z0JBRUQsT0FBTztvQkFDSCxFQUFFLEVBQUUsSUFBSSxDQUFDLE1BQU07b0JBQ2YsTUFBTSxFQUFFLFFBQVE7b0JBQ2hCLE9BQU8sRUFBRSxJQUFJLENBQUMsT0FBTztvQkFDckIsUUFBUSxFQUFFLElBQUksQ0FBQyxTQUFTO29CQUN4QixTQUFTLEVBQUUsSUFBSSxDQUFDLFVBQVU7b0JBQzFCLFNBQVMsRUFBRSxJQUFJLENBQUMsS0FBSyxJQUFJLENBQUM7b0JBQzFCLFlBQVksRUFBRSxJQUFJLENBQUMsUUFBUSxJQUFJLENBQUM7b0JBQ2hDLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUztvQkFDekIsT0FBTyxFQUFFLElBQUksQ0FBQyxTQUFTO29CQUN2QixRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVE7b0JBQ3ZCLFdBQVcsRUFBRSxJQUFJLENBQUMsU0FBUyxJQUFJLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLFNBQVMsSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxRQUFRO29CQUNuRyxTQUFTLEVBQUUsSUFBSSxFQUFFLHNEQUFzRDtvQkFDdkUsb0JBQW9CLEVBQUUsSUFBSSxDQUFDLCtDQUErQztpQkFDN0UsQ0FBQztZQUNOLENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsdUNBQXVDLElBQUksQ0FBQyxNQUFNLEdBQUcsRUFBRSxLQUFLLENBQUMsQ0FBQztnQkFDNUUsT0FBTyxJQUFJLENBQUM7WUFDaEIsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFFSiwwQ0FBMEM7UUFDMUMsTUFBTSxlQUFlLEdBQUcsVUFBVSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksS0FBSyxJQUFJLENBQUMsQ0FBQztRQUVqRSwwQ0FBMEM7UUFDMUMsTUFBTSxRQUFRLEdBQVE7WUFDbEIsS0FBSyxFQUFFLGVBQWU7WUFDdEIsS0FBSyxFQUFFLGVBQWUsQ0FBQyxNQUFNO1lBQzdCLE9BQU8sRUFBRSxDQUFDLENBQUMsV0FBVyxDQUFDLGdCQUFnQjtTQUMxQyxDQUFDO1FBRUYscURBQXFEO1FBQ3JELElBQUksV0FBVyxDQUFDLGdCQUFnQixFQUFFLENBQUM7WUFDL0IsUUFBUSxDQUFDLGdCQUFnQixHQUFHLGtCQUFrQixDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVyxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQztRQUNqRyxDQUFDO1FBRUQsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLFFBQVEsQ0FBQyxDQUFDO0lBRXpDLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQywwQkFBMEIsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUNqRCxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsMkJBQTJCLEVBQUUsT0FBTyxFQUFHLEtBQWUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQzFHLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRixlQUFlO0FBQ1IsTUFBTSxPQUFPLEdBQUcsS0FBSyxFQUFFLEtBQTJCLEVBQWtDLEVBQUU7SUFDekYsT0FBTyxDQUFDLEdBQUcsQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFFdEQsTUFBTSxFQUFFLFVBQVUsRUFBRSxJQUFJLEVBQUUsY0FBYyxFQUFFLEdBQUcsS0FBSyxDQUFDO0lBRW5ELElBQUksQ0FBQztRQUNELElBQUksVUFBVSxLQUFLLEtBQUssSUFBSSxJQUFJLEtBQUssZ0JBQWdCLEVBQUUsQ0FBQztZQUNwRCxPQUFPLE1BQU0sVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ25DLENBQUM7YUFBTSxJQUFJLFVBQVUsS0FBSyxLQUFLLElBQUksSUFBSSxLQUFLLGdCQUFnQixFQUFFLENBQUM7WUFDM0QsT0FBTyxNQUFNLGFBQWEsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUN0QyxDQUFDO2FBQU0sSUFBSSxVQUFVLEtBQUssS0FBSyxJQUFJLElBQUksS0FBSyw0QkFBNEIsRUFBRSxDQUFDO1lBQ3ZFLE9BQU8sTUFBTSxpQkFBaUIsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUMxQyxDQUFDO2FBQU0sSUFBSSxVQUFVLEtBQUssS0FBSyxJQUFJLGNBQWMsSUFBSSxjQUFjLENBQUMsRUFBRSxFQUFFLENBQUM7WUFDckUsT0FBTyxNQUFNLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNoQyxDQUFDO2FBQU0sSUFBSSxVQUFVLEtBQUssTUFBTSxJQUFJLElBQUksQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQztZQUMzRCxPQUFPLE1BQU0sVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ25DLENBQUM7YUFBTSxJQUFJLFVBQVUsS0FBSyxRQUFRLElBQUksSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDO1lBQzdELE9BQU8sTUFBTSxZQUFZLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDckMsQ0FBQzthQUFNLENBQUM7WUFDSixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsV0FBVyxFQUFFLENBQUMsQ0FBQztRQUN2RCxDQUFDO0lBQ0wsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDYixPQUFPLENBQUMsS0FBSyxDQUFDLGdCQUFnQixFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQ3ZDLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSx1QkFBdUIsRUFBRSxPQUFPLEVBQUcsS0FBZSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDdEcsQ0FBQztBQUNMLENBQUMsQ0FBQztBQXpCVyxRQUFBLE9BQU8sV0F5QmxCIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRHluYW1vREJDbGllbnQsIEdldEl0ZW1Db21tYW5kIH0gZnJvbSAnQGF3cy1zZGsvY2xpZW50LWR5bmFtb2RiJztcbmltcG9ydCB7IER5bmFtb0RCRG9jdW1lbnRDbGllbnQsIFNjYW5Db21tYW5kLCBHZXRDb21tYW5kLCBQdXRDb21tYW5kLCBVcGRhdGVDb21tYW5kLCBEZWxldGVDb21tYW5kLCBRdWVyeUNvbW1hbmQgfSBmcm9tICdAYXdzLXNkay9saWItZHluYW1vZGInO1xuaW1wb3J0IHsgUzNDbGllbnQgfSBmcm9tICdAYXdzLXNkay9jbGllbnQtczMnO1xuaW1wb3J0IHsgbWFyc2hhbGwsIHVubWFyc2hhbGwgfSBmcm9tICdAYXdzLXNkay91dGlsLWR5bmFtb2RiJztcbmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnO1xuaW1wb3J0IHsgQVBJR2F0ZXdheVByb3h5RXZlbnQsIEFQSUdhdGV3YXlQcm94eVJlc3VsdCB9IGZyb20gJ2F3cy1sYW1iZGEnO1xuXG4vLyBDb25maWd1cmUgQVdTIFNESyB2MyBjbGllbnRzXG5jb25zdCBhd3NDb25maWcgPSB7XG4gICAgcmVnaW9uOiBwcm9jZXNzLmVudi5BV1NfUkVHSU9OIHx8ICd1cy1lYXN0LTEnXG59O1xuXG5jb25zdCBkeW5hbW9kYkNsaWVudCA9IG5ldyBEeW5hbW9EQkNsaWVudChhd3NDb25maWcpO1xuY29uc3QgZHluYW1vZGIgPSBEeW5hbW9EQkRvY3VtZW50Q2xpZW50LmZyb20oZHluYW1vZGJDbGllbnQpO1xuY29uc3QgczMgPSBuZXcgUzNDbGllbnQoYXdzQ29uZmlnKTtcblxuY29uc3QgVVNFUlNfVEFCTEUgPSBwcm9jZXNzLmVudi5VU0VSU19UQUJMRTtcbmNvbnN0IFVTRVJfUFJPRklMRVNfVEFCTEUgPSBwcm9jZXNzLmVudi5VU0VSX1BST0ZJTEVTX1RBQkxFO1xuY29uc3QgRk9MTE9XU19UQUJMRSA9IHByb2Nlc3MuZW52LkZPTExPV1NfVEFCTEU7XG5jb25zdCBQT1NUU19UQUJMRSA9IHByb2Nlc3MuZW52LlBPU1RTX1RBQkxFO1xuY29uc3QgTElLRVNfVEFCTEUgPSBwcm9jZXNzLmVudi5MSUtFU19UQUJMRTtcbmNvbnN0IEFWQVRBUlNfQlVDS0VUID0gcHJvY2Vzcy5lbnYuQVZBVEFSU19CVUNLRVQ7XG5cbi8vIFR5cGVTY3JpcHQgaW50ZXJmYWNlc1xuaW50ZXJmYWNlIFVzZXIge1xuICAgIGlkOiBzdHJpbmc7XG4gICAgZW1haWw6IHN0cmluZztcbiAgICB1c2VybmFtZTogc3RyaW5nO1xuICAgIGZpcnN0TmFtZT86IHN0cmluZztcbiAgICBsYXN0TmFtZT86IHN0cmluZztcbiAgICBjb2duaXRvVXNlcklkOiBzdHJpbmc7XG4gICAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gICAgdXBkYXRlZEF0OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBVc2VyUHJvZmlsZSB7XG4gICAgdXNlcl9pZDogc3RyaW5nO1xuICAgIGJpbz86IHN0cmluZztcbiAgICBhdmF0YXJVcmw/OiBzdHJpbmc7XG4gICAgbG9jYXRpb24/OiBzdHJpbmc7XG4gICAgd2Vic2l0ZT86IHN0cmluZztcbiAgICBmb2xsb3dlcnNDb3VudD86IG51bWJlcjtcbiAgICBmb2xsb3dpbmdDb3VudD86IG51bWJlcjtcbiAgICBjcmVhdGVkQXQ/OiBzdHJpbmc7XG4gICAgdXBkYXRlZEF0OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBVc2VyU3RhdHMge1xuICAgIHBvc3RzQ291bnQ6IG51bWJlcjtcbiAgICBsaWtlc0NvdW50OiBudW1iZXI7XG4gICAgZm9sbG93ZXJzQ291bnQ6IG51bWJlcjtcbiAgICBmb2xsb3dpbmdDb3VudDogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgRm9sbG93IHtcbiAgICBpZDogc3RyaW5nO1xuICAgIGZvbGxvd2VySWQ6IHN0cmluZztcbiAgICBmb2xsb3dpbmdJZDogc3RyaW5nO1xuICAgIGNyZWF0ZWRBdDogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgTGlrZSB7XG4gICAgcG9zdElkOiBzdHJpbmc7XG4gICAgdXNlcklkOiBzdHJpbmc7XG4gICAgcG9zdF9pZDogc3RyaW5nO1xuICAgIHVzZXJfaWQ6IHN0cmluZztcbiAgICBjcmVhdGVkQXQ6IHN0cmluZztcbn1cblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGNyZWF0ZSByZXNwb25zZVxuY29uc3QgY3JlYXRlUmVzcG9uc2UgPSAoc3RhdHVzQ29kZTogbnVtYmVyLCBib2R5OiBhbnkpOiBBUElHYXRld2F5UHJveHlSZXN1bHQgPT4gKHtcbiAgICBzdGF0dXNDb2RlLFxuICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgJ0FjY2Vzcy1Db250cm9sLUFsbG93LU9yaWdpbic6ICcqJyxcbiAgICAgICAgJ0FjY2Vzcy1Db250cm9sLUFsbG93LUhlYWRlcnMnOiAnQ29udGVudC1UeXBlLFgtQW16LURhdGUsQXV0aG9yaXphdGlvbixYLUFwaS1LZXksWC1BbXotU2VjdXJpdHktVG9rZW4nLFxuICAgICAgICAnQWNjZXNzLUNvbnRyb2wtQWxsb3ctTWV0aG9kcyc6ICdHRVQsUE9TVCxQVVQsREVMRVRFLE9QVElPTlMnXG4gICAgfSxcbiAgICBib2R5OiBKU09OLnN0cmluZ2lmeShib2R5KVxufSk7XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgdXNlciBJRCBmcm9tIGF1dGhvcml6ZXIgY29udGV4dFxuY29uc3QgZ2V0VXNlcklkRnJvbUNvbnRleHQgPSAoZXZlbnQ6IEFQSUdhdGV3YXlQcm94eUV2ZW50KTogc3RyaW5nIHwgbnVsbCA9PiB7XG4gICAgLy8gV2hlbiB1c2luZyBMYW1iZGEgYXV0aG9yaXplciwgdXNlciBpbmZvIGlzIGF2YWlsYWJsZSBpbiB0aGUgY29udGV4dFxuICAgIGlmIChldmVudC5yZXF1ZXN0Q29udGV4dCAmJiBldmVudC5yZXF1ZXN0Q29udGV4dC5hdXRob3JpemVyKSB7XG4gICAgICAgIHJldHVybiAoZXZlbnQucmVxdWVzdENvbnRleHQuYXV0aG9yaXplciBhcyBhbnkpLnVzZXJJZDtcbiAgICB9XG4gICAgcmV0dXJuIG51bGw7XG59O1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gY2FsY3VsYXRlIHVzZXIgc3RhdGlzdGljc1xuY29uc3QgY2FsY3VsYXRlVXNlclN0YXRzID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxVc2VyU3RhdHM+ID0+IHtcbiAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygnY2FsY3VsYXRlVXNlclN0YXRzOiBDYWxjdWxhdGluZyBzdGF0cyBmb3IgdXNlciBJRDonLCB1c2VySWQpO1xuXG4gICAgICAgIC8vIENvdW50IHBvc3RzIGJ5IHVzZXJcbiAgICAgICAgY29uc3Qgc2NhblBvc3RzQ29tbWFuZCA9IG5ldyBTY2FuQ29tbWFuZCh7XG4gICAgICAgICAgICBUYWJsZU5hbWU6IFBPU1RTX1RBQkxFLFxuICAgICAgICAgICAgRmlsdGVyRXhwcmVzc2lvbjogJ3VzZXJfaWQgPSA6dXNlcklkIEFORCBpc19hY3RpdmUgPSA6aXNBY3RpdmUnLFxuICAgICAgICAgICAgRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlczoge1xuICAgICAgICAgICAgICAgICc6dXNlcklkJzogdXNlcklkLFxuICAgICAgICAgICAgICAgICc6aXNBY3RpdmUnOiB0cnVlXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgU2VsZWN0OiAnQ09VTlQnXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBwb3N0c1Jlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoc2NhblBvc3RzQ29tbWFuZCk7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ2NhbGN1bGF0ZVVzZXJTdGF0czogRm91bmQnLCBwb3N0c1Jlc3VsdC5Db3VudCwgJ3Bvc3RzIGZvciB1c2VyJywgdXNlcklkKTtcblxuICAgICAgICAvLyBDb3VudCBsaWtlcyBieSB1c2VyICh1c2luZyBHU0kpXG4gICAgICAgIGNvbnN0IHF1ZXJ5TGlrZXNDb21tYW5kID0gbmV3IFF1ZXJ5Q29tbWFuZCh7XG4gICAgICAgICAgICBUYWJsZU5hbWU6IExJS0VTX1RBQkxFLFxuICAgICAgICAgICAgSW5kZXhOYW1lOiAndXNlcklkLWluZGV4JyxcbiAgICAgICAgICAgIEtleUNvbmRpdGlvbkV4cHJlc3Npb246ICd1c2VySWQgPSA6dXNlcklkJyxcbiAgICAgICAgICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXM6IHtcbiAgICAgICAgICAgICAgICAnOnVzZXJJZCc6IHVzZXJJZFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIFNlbGVjdDogJ0NPVU5UJ1xuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgbGlrZXNSZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKHF1ZXJ5TGlrZXNDb21tYW5kKTtcblxuICAgICAgICBjb25zb2xlLmxvZygnY2FsY3VsYXRlVXNlclN0YXRzOiBGb3VuZCcsIGxpa2VzUmVzdWx0LkNvdW50LCAnbGlrZXMgZm9yIHVzZXInLCB1c2VySWQpO1xuXG4gICAgICAgIC8vIENvdW50IGZvbGxvd2Vyc1xuICAgICAgICBjb25zdCBzY2FuRm9sbG93ZXJzQ29tbWFuZCA9IG5ldyBTY2FuQ29tbWFuZCh7XG4gICAgICAgICAgICBUYWJsZU5hbWU6IEZPTExPV1NfVEFCTEUsXG4gICAgICAgICAgICBGaWx0ZXJFeHByZXNzaW9uOiAnZm9sbG93aW5nSWQgPSA6dXNlcklkJyxcbiAgICAgICAgICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXM6IHtcbiAgICAgICAgICAgICAgICAnOnVzZXJJZCc6IHVzZXJJZFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIFNlbGVjdDogJ0NPVU5UJ1xuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgZm9sbG93ZXJzUmVzdWx0ID0gYXdhaXQgZHluYW1vZGIuc2VuZChzY2FuRm9sbG93ZXJzQ29tbWFuZCk7XG5cbiAgICAgICAgLy8gQ291bnQgZm9sbG93aW5nXG4gICAgICAgIGNvbnN0IHNjYW5Gb2xsb3dpbmdDb21tYW5kID0gbmV3IFNjYW5Db21tYW5kKHtcbiAgICAgICAgICAgIFRhYmxlTmFtZTogRk9MTE9XU19UQUJMRSxcbiAgICAgICAgICAgIEZpbHRlckV4cHJlc3Npb246ICdmb2xsb3dlcklkID0gOnVzZXJJZCcsXG4gICAgICAgICAgICBFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzOiB7XG4gICAgICAgICAgICAgICAgJzp1c2VySWQnOiB1c2VySWRcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBTZWxlY3Q6ICdDT1VOVCdcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IGZvbGxvd2luZ1Jlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoc2NhbkZvbGxvd2luZ0NvbW1hbmQpO1xuXG4gICAgICAgIGNvbnN0IHN0YXRzOiBVc2VyU3RhdHMgPSB7XG4gICAgICAgICAgICBwb3N0c0NvdW50OiBwb3N0c1Jlc3VsdC5Db3VudCB8fCAwLFxuICAgICAgICAgICAgbGlrZXNDb3VudDogbGlrZXNSZXN1bHQuQ291bnQgfHwgMCxcbiAgICAgICAgICAgIGZvbGxvd2Vyc0NvdW50OiBmb2xsb3dlcnNSZXN1bHQuQ291bnQgfHwgMCxcbiAgICAgICAgICAgIGZvbGxvd2luZ0NvdW50OiBmb2xsb3dpbmdSZXN1bHQuQ291bnQgfHwgMFxuICAgICAgICB9O1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCdjYWxjdWxhdGVVc2VyU3RhdHM6IEZpbmFsIHN0YXRzIGZvciB1c2VyJywgdXNlcklkLCAnOicsIHN0YXRzKTtcblxuICAgICAgICByZXR1cm4gc3RhdHM7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2FsY3VsYXRpbmcgdXNlciBzdGF0czonLCBlcnJvcik7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBwb3N0c0NvdW50OiAwLFxuICAgICAgICAgICAgbGlrZXNDb3VudDogMCxcbiAgICAgICAgICAgIGZvbGxvd2Vyc0NvdW50OiAwLFxuICAgICAgICAgICAgZm9sbG93aW5nQ291bnQ6IDBcbiAgICAgICAgfTtcbiAgICB9XG59O1xuXG4vLyBHZXQgdXNlciBwcm9maWxlXG5jb25zdCBnZXRQcm9maWxlID0gYXN5bmMgKGV2ZW50OiBBUElHYXRld2F5UHJveHlFdmVudCk6IFByb21pc2U8QVBJR2F0ZXdheVByb3h5UmVzdWx0PiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgLy8gR2V0IHVzZXIgSUQgZnJvbSBhdXRob3JpemVyIGNvbnRleHRcbiAgICAgICAgY29uc3QgdXNlcklkID0gZ2V0VXNlcklkRnJvbUNvbnRleHQoZXZlbnQpO1xuXG4gICAgICAgIGlmICghdXNlcklkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAxLCB7IGVycm9yOiAnVXNlciBub3QgYXV0aGVudGljYXRlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBHZXQgdXNlciBmcm9tIFVzZXJzIHRhYmxlXG4gICAgICAgIGNvbnN0IGdldFVzZXJDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBVU0VSU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBpZDogdXNlcklkIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IHVzZXJSZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKGdldFVzZXJDb21tYW5kKTtcblxuICAgICAgICBpZiAoIXVzZXJSZXN1bHQuSXRlbSkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwNCwgeyBlcnJvcjogJ1VzZXIgbm90IGZvdW5kJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEdldCB1c2VyIHByb2ZpbGUgZnJvbSBVc2VyUHJvZmlsZXMgdGFibGVcbiAgICAgICAgY29uc3QgZ2V0UHJvZmlsZUNvbW1hbmQgPSBuZXcgR2V0Q29tbWFuZCh7XG4gICAgICAgICAgICBUYWJsZU5hbWU6IFVTRVJfUFJPRklMRVNfVEFCTEUsXG4gICAgICAgICAgICBLZXk6IHsgdXNlcklkOiB1c2VySWQgfVxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgcHJvZmlsZVJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoZ2V0UHJvZmlsZUNvbW1hbmQpO1xuXG4gICAgICAgIGNvbnN0IHVzZXIgPSB1c2VyUmVzdWx0Lkl0ZW0gYXMgVXNlcjtcbiAgICAgICAgY29uc3QgcHJvZmlsZSA9IChwcm9maWxlUmVzdWx0Lkl0ZW0gYXMgVXNlclByb2ZpbGUpIHx8IHt9O1xuXG4gICAgICAgIC8vIENhbGN1bGF0ZSByZWFsLXRpbWUgc3RhdGlzdGljc1xuICAgICAgICBjb25zdCBzdGF0cyA9IGF3YWl0IGNhbGN1bGF0ZVVzZXJTdGF0cyh1c2VySWQpO1xuXG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSgyMDAsIHtcbiAgICAgICAgICAgIHVzZXI6IHtcbiAgICAgICAgICAgICAgICBpZDogdXNlci5pZCxcbiAgICAgICAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcbiAgICAgICAgICAgICAgICB1c2VybmFtZTogdXNlci51c2VybmFtZSxcbiAgICAgICAgICAgICAgICBmaXJzdE5hbWU6IHVzZXIuZmlyc3ROYW1lLFxuICAgICAgICAgICAgICAgIGxhc3ROYW1lOiB1c2VyLmxhc3ROYW1lLFxuICAgICAgICAgICAgICAgIGJpbzogcHJvZmlsZS5iaW8gfHwgJycsXG4gICAgICAgICAgICAgICAgYXZhdGFyVXJsOiBwcm9maWxlLmF2YXRhclVybCB8fCBudWxsLFxuICAgICAgICAgICAgICAgIGxvY2F0aW9uOiBwcm9maWxlLmxvY2F0aW9uIHx8ICcnLFxuICAgICAgICAgICAgICAgIHdlYnNpdGU6IHByb2ZpbGUud2Vic2l0ZSB8fCAnJyxcbiAgICAgICAgICAgICAgICBmb2xsb3dlcnNDb3VudDogc3RhdHMuZm9sbG93ZXJzQ291bnQsXG4gICAgICAgICAgICAgICAgZm9sbG93aW5nQ291bnQ6IHN0YXRzLmZvbGxvd2luZ0NvdW50LFxuICAgICAgICAgICAgICAgIHBvc3RzQ291bnQ6IHN0YXRzLnBvc3RzQ291bnQsXG4gICAgICAgICAgICAgICAgbGlrZXNDb3VudDogc3RhdHMubGlrZXNDb3VudCxcbiAgICAgICAgICAgICAgICBjcmVhdGVkQXQ6IHVzZXIuY3JlYXRlZEF0LFxuICAgICAgICAgICAgICAgIHVwZGF0ZWRBdDogdXNlci51cGRhdGVkQXRcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzdGF0czoge1xuICAgICAgICAgICAgICAgIHBvc3RzOiBzdGF0cy5wb3N0c0NvdW50LFxuICAgICAgICAgICAgICAgIGZvbGxvd2Vyczogc3RhdHMuZm9sbG93ZXJzQ291bnQsXG4gICAgICAgICAgICAgICAgZm9sbG93aW5nOiBzdGF0cy5mb2xsb3dpbmdDb3VudCxcbiAgICAgICAgICAgICAgICBsaWtlczogc3RhdHMubGlrZXNDb3VudFxuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0dldFByb2ZpbGUgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNTAwLCB7IGVycm9yOiAnRmFpbGVkIHRvIGdldCBwcm9maWxlJywgZGV0YWlsczogKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlIH0pO1xuICAgIH1cbn07XG5cbi8vIFVwZGF0ZSB1c2VyIHByb2ZpbGVcbmNvbnN0IHVwZGF0ZVByb2ZpbGUgPSBhc3luYyAoZXZlbnQ6IEFQSUdhdGV3YXlQcm94eUV2ZW50KTogUHJvbWlzZTxBUElHYXRld2F5UHJveHlSZXN1bHQ+ID0+IHtcbiAgICB0cnkge1xuICAgICAgICBpZiAoIWV2ZW50LmJvZHkpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdSZXF1ZXN0IGJvZHkgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgeyBmaXJzdE5hbWUsIGxhc3ROYW1lLCBiaW8sIGxvY2F0aW9uLCB3ZWJzaXRlLCBhdmF0YXJVcmwgfSA9IEpTT04ucGFyc2UoZXZlbnQuYm9keSk7XG5cbiAgICAgICAgLy8gR2V0IHVzZXIgSUQgZnJvbSBhdXRob3JpemVyIGNvbnRleHRcbiAgICAgICAgY29uc3QgdXNlcklkID0gZ2V0VXNlcklkRnJvbUNvbnRleHQoZXZlbnQpO1xuXG4gICAgICAgIGlmICghdXNlcklkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAxLCB7IGVycm9yOiAnVXNlciBub3QgYXV0aGVudGljYXRlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBVcGRhdGUgVXNlcnMgdGFibGVcbiAgICAgICAgY29uc3QgdXNlclVwZGF0ZUV4cHJlc3Npb246IHN0cmluZ1tdID0gW107XG4gICAgICAgIGNvbnN0IHVzZXJFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+ID0ge307XG5cbiAgICAgICAgaWYgKGZpcnN0TmFtZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB1c2VyVXBkYXRlRXhwcmVzc2lvbi5wdXNoKCdmaXJzdE5hbWUgPSA6Zmlyc3ROYW1lJyk7XG4gICAgICAgICAgICB1c2VyRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlc1snOmZpcnN0TmFtZSddID0gZmlyc3ROYW1lO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGxhc3ROYW1lICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHVzZXJVcGRhdGVFeHByZXNzaW9uLnB1c2goJ2xhc3ROYW1lID0gOmxhc3ROYW1lJyk7XG4gICAgICAgICAgICB1c2VyRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlc1snOmxhc3ROYW1lJ10gPSBsYXN0TmFtZTtcbiAgICAgICAgfVxuXG4gICAgICAgIHVzZXJVcGRhdGVFeHByZXNzaW9uLnB1c2goJ3VwZGF0ZWRBdCA9IDp1cGRhdGVkQXQnKTtcbiAgICAgICAgdXNlckV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXNbJzp1cGRhdGVkQXQnXSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKTtcblxuICAgICAgICBpZiAodXNlclVwZGF0ZUV4cHJlc3Npb24ubGVuZ3RoID4gMSkgeyAvLyBNb3JlIHRoYW4ganVzdCB1cGRhdGVkX2F0XG4gICAgICAgICAgICBjb25zdCB1cGRhdGVVc2VyQ29tbWFuZCA9IG5ldyBVcGRhdGVDb21tYW5kKHtcbiAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IFVTRVJTX1RBQkxFLFxuICAgICAgICAgICAgICAgIEtleTogeyBpZDogdXNlcklkIH0sXG4gICAgICAgICAgICAgICAgVXBkYXRlRXhwcmVzc2lvbjogYFNFVCAke3VzZXJVcGRhdGVFeHByZXNzaW9uLmpvaW4oJywgJyl9YCxcbiAgICAgICAgICAgICAgICBFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzOiB1c2VyRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlc1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBhd2FpdCBkeW5hbW9kYi5zZW5kKHVwZGF0ZVVzZXJDb21tYW5kKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFVwZGF0ZSBvciBjcmVhdGUgVXNlclByb2ZpbGVzIHRhYmxlIGVudHJ5XG4gICAgICAgIGNvbnN0IHByb2ZpbGVVcGRhdGVFeHByZXNzaW9uOiBzdHJpbmdbXSA9IFtdO1xuICAgICAgICBjb25zdCBwcm9maWxlRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlczogUmVjb3JkPHN0cmluZywgYW55PiA9IHt9O1xuXG4gICAgICAgIGlmIChiaW8gIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgcHJvZmlsZVVwZGF0ZUV4cHJlc3Npb24ucHVzaCgnYmlvID0gOmJpbycpO1xuICAgICAgICAgICAgcHJvZmlsZUV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXNbJzpiaW8nXSA9IGJpbztcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChsb2NhdGlvbiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBwcm9maWxlVXBkYXRlRXhwcmVzc2lvbi5wdXNoKCcjbG9jYXRpb24gPSA6bG9jYXRpb24nKTtcbiAgICAgICAgICAgIHByb2ZpbGVFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzWyc6bG9jYXRpb24nXSA9IGxvY2F0aW9uO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKHdlYnNpdGUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgcHJvZmlsZVVwZGF0ZUV4cHJlc3Npb24ucHVzaCgnd2Vic2l0ZSA9IDp3ZWJzaXRlJyk7XG4gICAgICAgICAgICBwcm9maWxlRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlc1snOndlYnNpdGUnXSA9IHdlYnNpdGU7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoYXZhdGFyVXJsICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHByb2ZpbGVVcGRhdGVFeHByZXNzaW9uLnB1c2goJ2F2YXRhclVybCA9IDphdmF0YXJVcmwnKTtcbiAgICAgICAgICAgIHByb2ZpbGVFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzWyc6YXZhdGFyVXJsJ10gPSBhdmF0YXJVcmw7XG4gICAgICAgIH1cblxuICAgICAgICBwcm9maWxlVXBkYXRlRXhwcmVzc2lvbi5wdXNoKCd1cGRhdGVkQXQgPSA6dXBkYXRlZEF0Jyk7XG4gICAgICAgIHByb2ZpbGVFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzWyc6dXBkYXRlZEF0J10gPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCk7XG5cbiAgICAgICAgaWYgKHByb2ZpbGVVcGRhdGVFeHByZXNzaW9uLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZVByb2ZpbGVDb21tYW5kID0gbmV3IFVwZGF0ZUNvbW1hbmQoe1xuICAgICAgICAgICAgICAgIFRhYmxlTmFtZTogVVNFUl9QUk9GSUxFU19UQUJMRSxcbiAgICAgICAgICAgICAgICBLZXk6IHsgdXNlcl9pZDogdXNlcklkIH0sXG4gICAgICAgICAgICAgICAgVXBkYXRlRXhwcmVzc2lvbjogYFNFVCAke3Byb2ZpbGVVcGRhdGVFeHByZXNzaW9uLmpvaW4oJywgJyl9YCxcbiAgICAgICAgICAgICAgICBFeHByZXNzaW9uQXR0cmlidXRlTmFtZXM6IGxvY2F0aW9uICE9PSB1bmRlZmluZWQgPyB7ICcjbG9jYXRpb24nOiAnbG9jYXRpb24nIH0gOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgICAgRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlczogcHJvZmlsZUV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXNcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgYXdhaXQgZHluYW1vZGIuc2VuZCh1cGRhdGVQcm9maWxlQ29tbWFuZCk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoMjAwLCB7IG1lc3NhZ2U6ICdQcm9maWxlIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JyB9KTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VwZGF0ZVByb2ZpbGUgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNTAwLCB7IGVycm9yOiAnRmFpbGVkIHRvIHVwZGF0ZSBwcm9maWxlJywgZGV0YWlsczogKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlIH0pO1xuICAgIH1cbn07XG5cbi8vIEdldCB1c2VyIGJ5IElEXG5jb25zdCBnZXRVc2VyID0gYXN5bmMgKGV2ZW50OiBBUElHYXRld2F5UHJveHlFdmVudCk6IFByb21pc2U8QVBJR2F0ZXdheVByb3h5UmVzdWx0PiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgeyBpZCB9ID0gZXZlbnQucGF0aFBhcmFtZXRlcnMgfHwge307XG5cbiAgICAgICAgaWYgKCFpZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ1VzZXIgSUQgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgZ2V0VXNlckNvbW1hbmQgPSBuZXcgR2V0Q29tbWFuZCh7XG4gICAgICAgICAgICBUYWJsZU5hbWU6IFVTRVJTX1RBQkxFLFxuICAgICAgICAgICAgS2V5OiB7IGlkIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IHVzZXJSZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKGdldFVzZXJDb21tYW5kKTtcblxuICAgICAgICBpZiAoIXVzZXJSZXN1bHQuSXRlbSkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwNCwgeyBlcnJvcjogJ1VzZXIgbm90IGZvdW5kJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGdldFByb2ZpbGVDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBVU0VSX1BST0ZJTEVTX1RBQkxFLFxuICAgICAgICAgICAgS2V5OiB7IHVzZXJJZDogaWQgfVxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgcHJvZmlsZVJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoZ2V0UHJvZmlsZUNvbW1hbmQpO1xuXG4gICAgICAgIGNvbnN0IHVzZXIgPSB1c2VyUmVzdWx0Lkl0ZW0gYXMgVXNlcjtcbiAgICAgICAgY29uc3QgcHJvZmlsZSA9IChwcm9maWxlUmVzdWx0Lkl0ZW0gYXMgVXNlclByb2ZpbGUpIHx8IHt9O1xuXG4gICAgICAgIC8vIENhbGN1bGF0ZSByZWFsLXRpbWUgc3RhdGlzdGljc1xuICAgICAgICBjb25zdCBzdGF0cyA9IGF3YWl0IGNhbGN1bGF0ZVVzZXJTdGF0cyhpZCk7XG5cbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDIwMCwge1xuICAgICAgICAgICAgdXNlcjoge1xuICAgICAgICAgICAgICAgIGlkOiB1c2VyLmlkLFxuICAgICAgICAgICAgICAgIHVzZXJuYW1lOiB1c2VyLnVzZXJuYW1lLFxuICAgICAgICAgICAgICAgIGZpcnN0TmFtZTogdXNlci5maXJzdE5hbWUsXG4gICAgICAgICAgICAgICAgbGFzdE5hbWU6IHVzZXIubGFzdE5hbWUsXG4gICAgICAgICAgICAgICAgYmlvOiBwcm9maWxlLmJpbyB8fCAnJyxcbiAgICAgICAgICAgICAgICBhdmF0YXJVcmw6IHByb2ZpbGUuYXZhdGFyVXJsIHx8IG51bGwsXG4gICAgICAgICAgICAgICAgbG9jYXRpb246IHByb2ZpbGUubG9jYXRpb24gfHwgJycsXG4gICAgICAgICAgICAgICAgd2Vic2l0ZTogcHJvZmlsZS53ZWJzaXRlIHx8ICcnLFxuICAgICAgICAgICAgICAgIGZvbGxvd2Vyc0NvdW50OiBzdGF0cy5mb2xsb3dlcnNDb3VudCxcbiAgICAgICAgICAgICAgICBmb2xsb3dpbmdDb3VudDogc3RhdHMuZm9sbG93aW5nQ291bnQsXG4gICAgICAgICAgICAgICAgcG9zdHNDb3VudDogc3RhdHMucG9zdHNDb3VudCxcbiAgICAgICAgICAgICAgICBsaWtlc0NvdW50OiBzdGF0cy5saWtlc0NvdW50LFxuICAgICAgICAgICAgICAgIGNyZWF0ZWRBdDogdXNlci5jcmVhdGVkQXRcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzdGF0czoge1xuICAgICAgICAgICAgICAgIHBvc3RzOiBzdGF0cy5wb3N0c0NvdW50LFxuICAgICAgICAgICAgICAgIGZvbGxvd2Vyczogc3RhdHMuZm9sbG93ZXJzQ291bnQsXG4gICAgICAgICAgICAgICAgZm9sbG93aW5nOiBzdGF0cy5mb2xsb3dpbmdDb3VudCxcbiAgICAgICAgICAgICAgICBsaWtlczogc3RhdHMubGlrZXNDb3VudFxuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0dldFVzZXIgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNTAwLCB7IGVycm9yOiAnRmFpbGVkIHRvIGdldCB1c2VyJywgZGV0YWlsczogKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlIH0pO1xuICAgIH1cbn07XG5cbi8vIEZvbGxvdyB1c2VyXG5jb25zdCBmb2xsb3dVc2VyID0gYXN5bmMgKGV2ZW50OiBBUElHYXRld2F5UHJveHlFdmVudCk6IFByb21pc2U8QVBJR2F0ZXdheVByb3h5UmVzdWx0PiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgeyBpZCB9ID0gZXZlbnQucGF0aFBhcmFtZXRlcnMgfHwge307IC8vIHVzZXIgdG8gZm9sbG93XG5cbiAgICAgICAgaWYgKCFpZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ1VzZXIgSUQgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gR2V0IHVzZXIgSUQgZnJvbSBhdXRob3JpemVyIGNvbnRleHRcbiAgICAgICAgY29uc3QgdXNlcklkID0gZ2V0VXNlcklkRnJvbUNvbnRleHQoZXZlbnQpO1xuXG4gICAgICAgIGlmICghdXNlcklkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAxLCB7IGVycm9yOiAnVXNlciBub3QgYXV0aGVudGljYXRlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAodXNlcklkID09PSBpZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ0Nhbm5vdCBmb2xsb3cgeW91cnNlbGYnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQ2hlY2sgaWYgYWxyZWFkeSBmb2xsb3dpbmdcbiAgICAgICAgY29uc3QgZ2V0Rm9sbG93Q29tbWFuZCA9IG5ldyBHZXRDb21tYW5kKHtcbiAgICAgICAgICAgIFRhYmxlTmFtZTogRk9MTE9XU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBmb2xsb3dlcl9pZDogdXNlcklkLCBmb2xsb3dpbmdfaWQ6IGlkIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IGV4aXN0aW5nRm9sbG93ID0gYXdhaXQgZHluYW1vZGIuc2VuZChnZXRGb2xsb3dDb21tYW5kKTtcblxuICAgICAgICBpZiAoZXhpc3RpbmdGb2xsb3cuSXRlbSkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ0FscmVhZHkgZm9sbG93aW5nIHRoaXMgdXNlcicgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBBZGQgZm9sbG93IHJlbGF0aW9uc2hpcFxuICAgICAgICBjb25zdCBwdXRGb2xsb3dDb21tYW5kID0gbmV3IFB1dENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBGT0xMT1dTX1RBQkxFLFxuICAgICAgICAgICAgSXRlbToge1xuICAgICAgICAgICAgICAgIGZvbGxvd2VyX2lkOiB1c2VySWQsXG4gICAgICAgICAgICAgICAgZm9sbG93aW5nX2lkOiBpZCxcbiAgICAgICAgICAgICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGF3YWl0IGR5bmFtb2RiLnNlbmQocHV0Rm9sbG93Q29tbWFuZCk7XG5cbiAgICAgICAgLy8gVXBkYXRlIGZvbGxvd2VyJ3MgZm9sbG93aW5nIGNvdW50XG4gICAgICAgIGNvbnN0IHVwZGF0ZUZvbGxvd2VyQ29tbWFuZCA9IG5ldyBVcGRhdGVDb21tYW5kKHtcbiAgICAgICAgICAgIFRhYmxlTmFtZTogVVNFUl9QUk9GSUxFU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyB1c2VyX2lkOiB1c2VySWQgfSxcbiAgICAgICAgICAgIFVwZGF0ZUV4cHJlc3Npb246ICdBREQgZm9sbG93aW5nQ291bnQgOmluYycsXG4gICAgICAgICAgICBFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzOiB7ICc6aW5jJzogMSB9XG4gICAgICAgIH0pO1xuICAgICAgICBhd2FpdCBkeW5hbW9kYi5zZW5kKHVwZGF0ZUZvbGxvd2VyQ29tbWFuZCk7XG5cbiAgICAgICAgLy8gVXBkYXRlIGZvbGxvd2VkIHVzZXIncyBmb2xsb3dlcnMgY291bnRcbiAgICAgICAgY29uc3QgdXBkYXRlRm9sbG93ZWRDb21tYW5kID0gbmV3IFVwZGF0ZUNvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBVU0VSX1BST0ZJTEVTX1RBQkxFLFxuICAgICAgICAgICAgS2V5OiB7IHVzZXJfaWQ6IGlkIH0sXG4gICAgICAgICAgICBVcGRhdGVFeHByZXNzaW9uOiAnQUREIGZvbGxvd2Vyc0NvdW50IDppbmMnLFxuICAgICAgICAgICAgRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlczogeyAnOmluYyc6IDEgfVxuICAgICAgICB9KTtcbiAgICAgICAgYXdhaXQgZHluYW1vZGIuc2VuZCh1cGRhdGVGb2xsb3dlZENvbW1hbmQpO1xuXG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSgyMDAsIHsgbWVzc2FnZTogJ1VzZXIgZm9sbG93ZWQgc3VjY2Vzc2Z1bGx5JyB9KTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZvbGxvd1VzZXIgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNTAwLCB7IGVycm9yOiAnRmFpbGVkIHRvIGZvbGxvdyB1c2VyJywgZGV0YWlsczogKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlIH0pO1xuICAgIH1cbn07XG5cbi8vIFVuZm9sbG93IHVzZXJcbmNvbnN0IHVuZm9sbG93VXNlciA9IGFzeW5jIChldmVudDogQVBJR2F0ZXdheVByb3h5RXZlbnQpOiBQcm9taXNlPEFQSUdhdGV3YXlQcm94eVJlc3VsdD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHsgaWQgfSA9IGV2ZW50LnBhdGhQYXJhbWV0ZXJzIHx8IHt9OyAvLyB1c2VyIHRvIHVuZm9sbG93XG5cbiAgICAgICAgaWYgKCFpZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ1VzZXIgSUQgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gR2V0IHVzZXIgSUQgZnJvbSBhdXRob3JpemVyIGNvbnRleHRcbiAgICAgICAgY29uc3QgdXNlcklkID0gZ2V0VXNlcklkRnJvbUNvbnRleHQoZXZlbnQpO1xuXG4gICAgICAgIGlmICghdXNlcklkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAxLCB7IGVycm9yOiAnVXNlciBub3QgYXV0aGVudGljYXRlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBSZW1vdmUgZm9sbG93IHJlbGF0aW9uc2hpcFxuICAgICAgICBjb25zdCBkZWxldGVGb2xsb3dDb21tYW5kID0gbmV3IERlbGV0ZUNvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBGT0xMT1dTX1RBQkxFLFxuICAgICAgICAgICAgS2V5OiB7IGZvbGxvd2VyX2lkOiB1c2VySWQsIGZvbGxvd2luZ19pZDogaWQgfVxuICAgICAgICB9KTtcbiAgICAgICAgYXdhaXQgZHluYW1vZGIuc2VuZChkZWxldGVGb2xsb3dDb21tYW5kKTtcblxuICAgICAgICAvLyBVcGRhdGUgZm9sbG93ZXIncyBmb2xsb3dpbmcgY291bnRcbiAgICAgICAgY29uc3QgdXBkYXRlRm9sbG93ZXJDb21tYW5kID0gbmV3IFVwZGF0ZUNvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBVU0VSX1BST0ZJTEVTX1RBQkxFLFxuICAgICAgICAgICAgS2V5OiB7IHVzZXJfaWQ6IHVzZXJJZCB9LFxuICAgICAgICAgICAgVXBkYXRlRXhwcmVzc2lvbjogJ0FERCBmb2xsb3dpbmdDb3VudCA6ZGVjJyxcbiAgICAgICAgICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXM6IHsgJzpkZWMnOiAtMSB9XG4gICAgICAgIH0pO1xuICAgICAgICBhd2FpdCBkeW5hbW9kYi5zZW5kKHVwZGF0ZUZvbGxvd2VyQ29tbWFuZCk7XG5cbiAgICAgICAgLy8gVXBkYXRlIGZvbGxvd2VkIHVzZXIncyBmb2xsb3dlcnMgY291bnRcbiAgICAgICAgY29uc3QgdXBkYXRlRm9sbG93ZWRDb21tYW5kID0gbmV3IFVwZGF0ZUNvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBVU0VSX1BST0ZJTEVTX1RBQkxFLFxuICAgICAgICAgICAgS2V5OiB7IHVzZXJfaWQ6IGlkIH0sXG4gICAgICAgICAgICBVcGRhdGVFeHByZXNzaW9uOiAnQUREIGZvbGxvd2Vyc0NvdW50IDpkZWMnLFxuICAgICAgICAgICAgRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlczogeyAnOmRlYyc6IC0xIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGF3YWl0IGR5bmFtb2RiLnNlbmQodXBkYXRlRm9sbG93ZWRDb21tYW5kKTtcblxuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoMjAwLCB7IG1lc3NhZ2U6ICdVc2VyIHVuZm9sbG93ZWQgc3VjY2Vzc2Z1bGx5JyB9KTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VuZm9sbG93VXNlciBlcnJvcjonLCBlcnJvcik7XG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg1MDAsIHsgZXJyb3I6ICdGYWlsZWQgdG8gdW5mb2xsb3cgdXNlcicsIGRldGFpbHM6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9KTtcbiAgICB9XG59O1xuXG4vLyBHZXQgdXNlcidzIGxpa2VkIHBvc3RzXG5jb25zdCBnZXRVc2VyTGlrZWRQb3N0cyA9IGFzeW5jIChldmVudDogQVBJR2F0ZXdheVByb3h5RXZlbnQpOiBQcm9taXNlPEFQSUdhdGV3YXlQcm94eVJlc3VsdD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAgIC8vIEdldCB1c2VyIElEIGZyb20gYXV0aG9yaXplciBjb250ZXh0XG4gICAgICAgIGNvbnN0IHVzZXJJZCA9IGdldFVzZXJJZEZyb21Db250ZXh0KGV2ZW50KTtcblxuICAgICAgICBpZiAoIXVzZXJJZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMSwgeyBlcnJvcjogJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gUGFyc2UgcXVlcnkgcGFyYW1ldGVyc1xuICAgICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IGV2ZW50LnF1ZXJ5U3RyaW5nUGFyYW1ldGVycyB8fCB7fTtcbiAgICAgICAgY29uc3QgbGltaXQgPSBwYXJzZUludChxdWVyeVBhcmFtcy5saW1pdCB8fCAnMjAnKSB8fCAyMDtcbiAgICAgICAgY29uc3QgbGFzdEV2YWx1YXRlZEtleSA9IHF1ZXJ5UGFyYW1zLmxhc3RFdmFsdWF0ZWRLZXkgPyBKU09OLnBhcnNlKGRlY29kZVVSSUNvbXBvbmVudChxdWVyeVBhcmFtcy5sYXN0RXZhbHVhdGVkS2V5KSkgOiB1bmRlZmluZWQ7XG5cbiAgICAgICAgLy8gRGVidWc6IExvZyB0aGUgdXNlciBJRCBiZWluZyB1c2VkXG4gICAgICAgIGNvbnNvbGUubG9nKCdnZXRVc2VyTGlrZWRQb3N0czogTG9va2luZyBmb3IgbGlrZXMgZm9yIHVzZXIgSUQ6JywgdXNlcklkKTtcbiAgICAgICAgY29uc29sZS5sb2coJ2dldFVzZXJMaWtlZFBvc3RzOiBVc2VyIElEIHR5cGU6JywgdHlwZW9mIHVzZXJJZCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdnZXRVc2VyTGlrZWRQb3N0czogTElLRVNfVEFCTEU6JywgTElLRVNfVEFCTEUpO1xuICAgICAgICBjb25zb2xlLmxvZygnZ2V0VXNlckxpa2VkUG9zdHM6IGxhc3RFdmFsdWF0ZWRLZXk6JywgbGFzdEV2YWx1YXRlZEtleSk7XG5cbiAgICAgICAgLy8gQnVpbGQgcXVlcnkgcGFyYW1ldGVyc1xuICAgICAgICBjb25zdCBxdWVyeVBhcmFtc19kYjogYW55ID0ge1xuICAgICAgICAgICAgVGFibGVOYW1lOiBMSUtFU19UQUJMRSxcbiAgICAgICAgICAgIEluZGV4TmFtZTogJ3VzZXJJZC1pbmRleCcsXG4gICAgICAgICAgICBLZXlDb25kaXRpb25FeHByZXNzaW9uOiAndXNlcklkID0gOnVzZXJJZCcsXG4gICAgICAgICAgICBFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzOiB7XG4gICAgICAgICAgICAgICAgJzp1c2VySWQnOiB1c2VySWRcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBTY2FuSW5kZXhGb3J3YXJkOiBmYWxzZSwgLy8gU29ydCBieSBtb3N0IHJlY2VudCBmaXJzdFxuICAgICAgICAgICAgTGltaXQ6IGxpbWl0XG4gICAgICAgIH07XG5cbiAgICAgICAgLy8gQWRkIHBhZ2luYXRpb24gaWYgcHJvdmlkZWRcbiAgICAgICAgaWYgKGxhc3RFdmFsdWF0ZWRLZXkpIHtcbiAgICAgICAgICAgIHF1ZXJ5UGFyYW1zX2RiLkV4Y2x1c2l2ZVN0YXJ0S2V5ID0gbGFzdEV2YWx1YXRlZEtleTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEdldCB1c2VyJ3MgbGlrZXMgdXNpbmcgR1NJXG4gICAgICAgIGNvbnN0IHF1ZXJ5TGlrZXNDb21tYW5kID0gbmV3IFF1ZXJ5Q29tbWFuZChxdWVyeVBhcmFtc19kYik7XG4gICAgICAgIGNvbnN0IGxpa2VzUmVzdWx0ID0gYXdhaXQgZHluYW1vZGIuc2VuZChxdWVyeUxpa2VzQ29tbWFuZCk7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ2dldFVzZXJMaWtlZFBvc3RzOiBGb3VuZCcsIGxpa2VzUmVzdWx0Lkl0ZW1zPy5sZW5ndGggfHwgMCwgJ2xpa2VzIGZvciB1c2VyJywgdXNlcklkKTtcbiAgICAgICAgY29uc29sZS5sb2coJ2dldFVzZXJMaWtlZFBvc3RzOiBSYXcgbGlrZXMgcmVzdWx0OicsIEpTT04uc3RyaW5naWZ5KGxpa2VzUmVzdWx0Lkl0ZW1zLCBudWxsLCAyKSk7XG5cbiAgICAgICAgY29uc3QgbGlrZXMgPSBsaWtlc1Jlc3VsdC5JdGVtcyB8fCBbXTtcblxuICAgICAgICAvLyBHZXQgcG9zdCBkZXRhaWxzIGZvciBlYWNoIGxpa2VcbiAgICAgICAgY29uc3QgbGlrZWRQb3N0cyA9IGF3YWl0IFByb21pc2UuYWxsKGxpa2VzLm1hcChhc3luYyAobGlrZTogYW55KSA9PiB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIC8vIEdldCBwb3N0IGRldGFpbHNcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnZ2V0VXNlckxpa2VkUG9zdHM6IEdldHRpbmcgcG9zdCBkZXRhaWxzIGZvciBwb3N0IElEOicsIGxpa2UucG9zdElkKTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnZ2V0VXNlckxpa2VkUG9zdHM6IFBPU1RTX1RBQkxFOicsIFBPU1RTX1RBQkxFKTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnZ2V0VXNlckxpa2VkUG9zdHM6IER5bmFtb0RCIGdldCBwYXJhbXM6JywgSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IFBPU1RTX1RBQkxFLFxuICAgICAgICAgICAgICAgICAgICBLZXk6IHsgaWQ6IGxpa2UucG9zdElkIH1cbiAgICAgICAgICAgICAgICB9LCBudWxsLCAyKSk7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2dldFVzZXJMaWtlZFBvc3RzOiBBV1MgcmVnaW9uOicsIHByb2Nlc3MuZW52LkFXU19SRUdJT04pO1xuXG4gICAgICAgICAgICAgICAgLy8gVXNlIHJhdyBEeW5hbW9EQiBjbGllbnQgKERvY3VtZW50Q2xpZW50IGhhcyBpc3N1ZXMgaW4gdGhpcyBmdW5jdGlvbilcbiAgICAgICAgICAgICAgICBjb25zdCBnZXRQb3N0Q29tbWFuZCA9IG5ldyBHZXRJdGVtQ29tbWFuZCh7XG4gICAgICAgICAgICAgICAgICAgIFRhYmxlTmFtZTogUE9TVFNfVEFCTEUsXG4gICAgICAgICAgICAgICAgICAgIEtleTogbWFyc2hhbGwoeyBpZDogbGlrZS5wb3N0SWQgfSlcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCByYXdQb3N0UmVzdWx0ID0gYXdhaXQgZHluYW1vZGJDbGllbnQuc2VuZChnZXRQb3N0Q29tbWFuZCk7XG5cbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnZ2V0VXNlckxpa2VkUG9zdHM6IFJhdyBEeW5hbW9EQiByZXN1bHQ6JywgSlNPTi5zdHJpbmdpZnkocmF3UG9zdFJlc3VsdCwgbnVsbCwgMikpO1xuXG4gICAgICAgICAgICAgICAgLy8gQ29udmVydCByYXcgcmVzdWx0IHRvIERvY3VtZW50Q2xpZW50IGZvcm1hdFxuICAgICAgICAgICAgICAgIGNvbnN0IHBvc3RSZXN1bHQgPSB7XG4gICAgICAgICAgICAgICAgICAgIEl0ZW06IHJhd1Bvc3RSZXN1bHQuSXRlbSA/IHVubWFyc2hhbGwocmF3UG9zdFJlc3VsdC5JdGVtKSA6IG51bGxcbiAgICAgICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICAgICAgaWYgKCFwb3N0UmVzdWx0Lkl0ZW0pIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7IC8vIFBvc3QgbWlnaHQgaGF2ZSBiZWVuIGRlbGV0ZWRcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICBjb25zdCBwb3N0ID0gcG9zdFJlc3VsdC5JdGVtO1xuXG4gICAgICAgICAgICAgICAgLy8gR2V0IHVzZXIgZGV0YWlscyBmb3IgdGhlIHBvc3QgYXV0aG9yIHVzaW5nIHJhdyBEeW5hbW9EQiBjbGllbnRcbiAgICAgICAgICAgICAgICAvLyBVc2UgYXV0aG9ySWQgb3IgdXNlcklkIGZpZWxkIChwb3N0cyBoYXZlIGJvdGgpXG4gICAgICAgICAgICAgICAgY29uc3QgYXV0aG9ySWQgPSBwb3N0LmF1dGhvcklkIHx8IHBvc3QudXNlcklkO1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdnZXRVc2VyTGlrZWRQb3N0czogQXV0aG9yIElEOicsIGF1dGhvcklkKTtcblxuICAgICAgICAgICAgICAgIGxldCB1c2VyOiBhbnkgPSB7fTtcbiAgICAgICAgICAgICAgICBpZiAoYXV0aG9ySWQpIHtcbiAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGdldFVzZXJDb21tYW5kID0gbmV3IEdldEl0ZW1Db21tYW5kKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IFVTRVJTX1RBQkxFLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEtleTogbWFyc2hhbGwoeyBpZDogYXV0aG9ySWQgfSlcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmF3VXNlclJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiQ2xpZW50LnNlbmQoZ2V0VXNlckNvbW1hbmQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdXNlciA9IHJhd1VzZXJSZXN1bHQuSXRlbSA/IHVubWFyc2hhbGwocmF3VXNlclJlc3VsdC5JdGVtKSA6IHt9O1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2dldFVzZXJMaWtlZFBvc3RzOiBVc2VyIGxvb2t1cCByZXN1bHQ6JywgSlNPTi5zdHJpbmdpZnkodXNlciwgbnVsbCwgMikpO1xuICAgICAgICAgICAgICAgICAgICB9IGNhdGNoICh1c2VyRXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ2dldFVzZXJMaWtlZFBvc3RzOiBFcnJvciBnZXR0aW5nIHVzZXIgZGV0YWlsczonLCB1c2VyRXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdXNlciA9IHt9OyAvLyBVc2UgZW1wdHkgdXNlciBvYmplY3QgaWYgbG9va3VwIGZhaWxzXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnZ2V0VXNlckxpa2VkUG9zdHM6IE5vIGF1dGhvciBJRCBmb3VuZCBmb3IgcG9zdCcpO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIGlkOiBsaWtlLnBvc3RJZCxcbiAgICAgICAgICAgICAgICAgICAgdXNlcklkOiBhdXRob3JJZCxcbiAgICAgICAgICAgICAgICAgICAgY29udGVudDogcG9zdC5jb250ZW50LFxuICAgICAgICAgICAgICAgICAgICBtZWRpYVVybDogcG9zdC5tZWRpYV91cmwsXG4gICAgICAgICAgICAgICAgICAgIG1lZGlhVHlwZTogcG9zdC5tZWRpYV90eXBlLFxuICAgICAgICAgICAgICAgICAgICBsaWtlQ291bnQ6IHBvc3QubGlrZXMgfHwgMCxcbiAgICAgICAgICAgICAgICAgICAgY29tbWVudENvdW50OiBwb3N0LmNvbW1lbnRzIHx8IDAsXG4gICAgICAgICAgICAgICAgICAgIGNyZWF0ZWRBdDogcG9zdC5jcmVhdGVkQXQsXG4gICAgICAgICAgICAgICAgICAgIGxpa2VkQXQ6IGxpa2UuY3JlYXRlZEF0LFxuICAgICAgICAgICAgICAgICAgICB1c2VybmFtZTogdXNlci51c2VybmFtZSxcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheU5hbWU6IHVzZXIuZmlyc3ROYW1lICYmIHVzZXIubGFzdE5hbWUgPyBgJHt1c2VyLmZpcnN0TmFtZX0gJHt1c2VyLmxhc3ROYW1lfWAgOiB1c2VyLnVzZXJuYW1lLFxuICAgICAgICAgICAgICAgICAgICBhdmF0YXJVcmw6IG51bGwsIC8vIFdvdWxkIG5lZWQgdG8gZ2V0IGZyb20gVXNlclByb2ZpbGVzIHRhYmxlIGlmIG5lZWRlZFxuICAgICAgICAgICAgICAgICAgICBpc0xpa2VkQnlDdXJyZW50VXNlcjogdHJ1ZSAvLyBBbHdheXMgdHJ1ZSBzaW5jZSB0aGVzZSBhcmUgdGhlIHVzZXIncyBsaWtlc1xuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGdldHRpbmcgcG9zdCBkZXRhaWxzIGZvciBsaWtlICR7bGlrZS5wb3N0SWR9OmAsIGVycm9yKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSkpO1xuXG4gICAgICAgIC8vIEZpbHRlciBvdXQgbnVsbCByZXN1bHRzIChkZWxldGVkIHBvc3RzKVxuICAgICAgICBjb25zdCB2YWxpZExpa2VkUG9zdHMgPSBsaWtlZFBvc3RzLmZpbHRlcihwb3N0ID0+IHBvc3QgIT09IG51bGwpO1xuXG4gICAgICAgIC8vIFByZXBhcmUgcmVzcG9uc2Ugd2l0aCBwcm9wZXIgcGFnaW5hdGlvblxuICAgICAgICBjb25zdCByZXNwb25zZTogYW55ID0ge1xuICAgICAgICAgICAgcG9zdHM6IHZhbGlkTGlrZWRQb3N0cyxcbiAgICAgICAgICAgIGNvdW50OiB2YWxpZExpa2VkUG9zdHMubGVuZ3RoLFxuICAgICAgICAgICAgaGFzTW9yZTogISFsaWtlc1Jlc3VsdC5MYXN0RXZhbHVhdGVkS2V5XG4gICAgICAgIH07XG5cbiAgICAgICAgLy8gSW5jbHVkZSBwYWdpbmF0aW9uIHRva2VuIGlmIHRoZXJlIGFyZSBtb3JlIHJlc3VsdHNcbiAgICAgICAgaWYgKGxpa2VzUmVzdWx0Lkxhc3RFdmFsdWF0ZWRLZXkpIHtcbiAgICAgICAgICAgIHJlc3BvbnNlLmxhc3RFdmFsdWF0ZWRLZXkgPSBlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkobGlrZXNSZXN1bHQuTGFzdEV2YWx1YXRlZEtleSkpO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDIwMCwgcmVzcG9uc2UpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignR2V0VXNlckxpa2VkUG9zdHMgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNTAwLCB7IGVycm9yOiAnRmFpbGVkIHRvIGdldCBsaWtlZCBwb3N0cycsIGRldGFpbHM6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9KTtcbiAgICB9XG59O1xuXG4vLyBNYWluIGhhbmRsZXJcbmV4cG9ydCBjb25zdCBoYW5kbGVyID0gYXN5bmMgKGV2ZW50OiBBUElHYXRld2F5UHJveHlFdmVudCk6IFByb21pc2U8QVBJR2F0ZXdheVByb3h5UmVzdWx0PiA9PiB7XG4gICAgY29uc29sZS5sb2coJ0V2ZW50OicsIEpTT04uc3RyaW5naWZ5KGV2ZW50LCBudWxsLCAyKSk7XG5cbiAgICBjb25zdCB7IGh0dHBNZXRob2QsIHBhdGgsIHBhdGhQYXJhbWV0ZXJzIH0gPSBldmVudDtcblxuICAgIHRyeSB7XG4gICAgICAgIGlmIChodHRwTWV0aG9kID09PSAnR0VUJyAmJiBwYXRoID09PSAnL3VzZXJzL3Byb2ZpbGUnKSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgZ2V0UHJvZmlsZShldmVudCk7XG4gICAgICAgIH0gZWxzZSBpZiAoaHR0cE1ldGhvZCA9PT0gJ1BVVCcgJiYgcGF0aCA9PT0gJy91c2Vycy9wcm9maWxlJykge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IHVwZGF0ZVByb2ZpbGUoZXZlbnQpO1xuICAgICAgICB9IGVsc2UgaWYgKGh0dHBNZXRob2QgPT09ICdHRVQnICYmIHBhdGggPT09ICcvdXNlcnMvcHJvZmlsZS9saWtlZC1wb3N0cycpIHtcbiAgICAgICAgICAgIHJldHVybiBhd2FpdCBnZXRVc2VyTGlrZWRQb3N0cyhldmVudCk7XG4gICAgICAgIH0gZWxzZSBpZiAoaHR0cE1ldGhvZCA9PT0gJ0dFVCcgJiYgcGF0aFBhcmFtZXRlcnMgJiYgcGF0aFBhcmFtZXRlcnMuaWQpIHtcbiAgICAgICAgICAgIHJldHVybiBhd2FpdCBnZXRVc2VyKGV2ZW50KTtcbiAgICAgICAgfSBlbHNlIGlmIChodHRwTWV0aG9kID09PSAnUE9TVCcgJiYgcGF0aC5pbmNsdWRlcygnL2ZvbGxvdycpKSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgZm9sbG93VXNlcihldmVudCk7XG4gICAgICAgIH0gZWxzZSBpZiAoaHR0cE1ldGhvZCA9PT0gJ0RFTEVURScgJiYgcGF0aC5pbmNsdWRlcygnL2ZvbGxvdycpKSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgdW5mb2xsb3dVc2VyKGV2ZW50KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDQsIHsgZXJyb3I6ICdOb3QgZm91bmQnIH0pO1xuICAgICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignSGFuZGxlciBlcnJvcjonLCBlcnJvcik7XG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg1MDAsIHsgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InLCBkZXRhaWxzOiAoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2UgfSk7XG4gICAgfVxufTtcbiJdfQ==