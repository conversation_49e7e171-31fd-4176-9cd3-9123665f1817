"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const uuid_1 = require("uuid");
// Configure AWS SDK v3 clients
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};
const dynamodbClient = new client_dynamodb_1.DynamoDBClient(awsConfig);
const dynamodb = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamodbClient);
// Environment variables
const CHANNELS_TABLE = process.env.CHANNELS_TABLE;
const CHANNEL_MEMBERS_TABLE = process.env.CHANNEL_MEMBERS_TABLE;
const USERS_TABLE = process.env.USERS_TABLE;
const POSTS_TABLE = process.env.POSTS_TABLE;
const MEDIA_TABLE = process.env.MEDIA_TABLE;
// CORS headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
};
// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: corsHeaders,
    body: JSON.stringify(body)
});
// Helper function to get user from context
const getUserFromContext = (event) => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return {
            userId: event.requestContext.authorizer.userId,
            email: event.requestContext.authorizer.email || null,
            username: event.requestContext.authorizer.username || null
        };
    }
    throw new Error('User not authenticated');
};
// Get all channels with pagination
const getChannels = async (event) => {
    try {
        const { limit = '20', offset = '0' } = event.queryStringParameters || {};
        const user = getUserFromContext(event);
        console.log(`Getting channels - limit: ${limit}, offset: ${offset}`);
        // Scan channels table (in production, consider using a GSI for better performance)
        const scanCommand = new lib_dynamodb_1.ScanCommand({
            TableName: CHANNELS_TABLE,
            Limit: parseInt(limit),
            ExclusiveStartKey: offset !== '0' ? JSON.parse(Buffer.from(offset, 'base64').toString()) : undefined
        });
        const result = await dynamodb.send(scanCommand);
        // For each channel, check if user is a member and get owner info
        const channelsWithMembership = await Promise.all((result.Items || []).map(async (channel) => {
            // Check if user is a member
            const getMembershipCommand = new lib_dynamodb_1.GetCommand({
                TableName: CHANNEL_MEMBERS_TABLE,
                Key: {
                    channelId: channel.id,
                    userId: user.userId
                }
            });
            const membershipResult = await dynamodb.send(getMembershipCommand);
            const isUserMember = !!membershipResult.Item;
            const userRole = membershipResult.Item?.role || null;
            // Get owner information
            const getOwnerCommand = new lib_dynamodb_1.GetCommand({
                TableName: USERS_TABLE,
                Key: { id: channel.ownerId }
            });
            const ownerResult = await dynamodb.send(getOwnerCommand);
            const owner = ownerResult.Item;
            // Get channel icon from media table if iconMediaId exists
            let iconUrl = null;
            if (channel.iconMediaId) {
                const getIconCommand = new lib_dynamodb_1.GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: channel.iconMediaId }
                });
                const iconResult = await dynamodb.send(getIconCommand);
                iconUrl = iconResult.Item?.url || null;
            }
            return {
                ...channel,
                ownerUsername: owner?.username,
                ownerDisplayName: owner?.displayName || `${owner?.firstName || ''} ${owner?.lastName || ''}`.trim(),
                ownerAvatarUrl: owner?.avatarUrl,
                iconUrl: iconUrl,
                isUserMember: isUserMember,
                userRole: userRole
            };
        }));
        const nextOffset = result.LastEvaluatedKey
            ? Buffer.from(JSON.stringify(result.LastEvaluatedKey)).toString('base64')
            : null;
        return createResponse(200, {
            channels: channelsWithMembership,
            hasMore: !!result.LastEvaluatedKey,
            nextOffset
        });
    }
    catch (error) {
        console.error('Error getting channels:', error);
        return createResponse(500, {
            error: 'Failed to get channels',
            details: error.message
        });
    }
};
// Create a new channel
const createChannel = async (event) => {
    try {
        const user = getUserFromContext(event);
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const body = JSON.parse(event.body);
        const { name, description, isPublic = true } = body;
        if (!name || name.trim().length === 0) {
            return createResponse(400, { error: 'Channel name is required' });
        }
        const channelId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        // Create channel
        const putChannelCommand = new lib_dynamodb_1.PutCommand({
            TableName: CHANNELS_TABLE,
            Item: {
                id: channelId,
                name: name.trim(),
                description: description?.trim() || null,
                ownerId: user.userId,
                isPublic: isPublic,
                isActive: true,
                memberCount: 1, // Owner is automatically a member
                createdAt: now,
                updatedAt: now
            }
        });
        await dynamodb.send(putChannelCommand);
        // Add owner as a member with 'owner' role
        const putMemberCommand = new lib_dynamodb_1.PutCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Item: {
                channelId: channelId,
                userId: user.userId,
                role: 'owner',
                joinedAt: now
            }
        });
        await dynamodb.send(putMemberCommand);
        console.log(`Channel created: ${channelId} by user: ${user.userId}`);
        return createResponse(201, {
            id: channelId,
            name: name.trim(),
            description: description?.trim() || null,
            ownerId: user.userId,
            isPublic: isPublic,
            isActive: true,
            memberCount: 1,
            createdAt: now,
            updatedAt: now,
            isUserMember: true,
            userRole: 'owner'
        });
    }
    catch (error) {
        console.error('Error creating channel:', error);
        return createResponse(500, {
            error: 'Failed to create channel',
            details: error.message
        });
    }
};
// Get a specific channel
const getChannel = async (event) => {
    try {
        const channelId = event.pathParameters?.id;
        if (!channelId) {
            return createResponse(400, { error: 'Channel ID is required' });
        }
        const user = getUserFromContext(event);
        const getChannelCommand = new lib_dynamodb_1.GetCommand({
            TableName: CHANNELS_TABLE,
            Key: { id: channelId }
        });
        const result = await dynamodb.send(getChannelCommand);
        if (!result.Item) {
            return createResponse(404, { error: 'Channel not found' });
        }
        const channel = result.Item;
        // Check if user is a member
        const getMembershipCommand = new lib_dynamodb_1.GetCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channelId: channelId,
                userId: user.userId
            }
        });
        const membershipResult = await dynamodb.send(getMembershipCommand);
        const isUserMember = !!membershipResult.Item;
        const userRole = membershipResult.Item?.role || null;
        // Get owner information
        const getOwnerCommand = new lib_dynamodb_1.GetCommand({
            TableName: USERS_TABLE,
            Key: { id: channel.ownerId }
        });
        const ownerResult = await dynamodb.send(getOwnerCommand);
        const owner = ownerResult.Item;
        // Get channel icon from media table if iconMediaId exists
        let iconUrl = null;
        if (channel.iconMediaId) {
            const getIconCommand = new lib_dynamodb_1.GetCommand({
                TableName: MEDIA_TABLE,
                Key: { id: channel.iconMediaId }
            });
            const iconResult = await dynamodb.send(getIconCommand);
            iconUrl = iconResult.Item?.url || null;
        }
        return createResponse(200, {
            ...channel,
            ownerUsername: owner?.username,
            ownerDisplayName: owner?.displayName || `${owner?.firstName || ''} ${owner?.lastName || ''}`.trim(),
            ownerAvatarUrl: owner?.avatarUrl,
            iconUrl: iconUrl,
            isUserMember: isUserMember,
            userRole: userRole
        });
    }
    catch (error) {
        console.error('Error getting channel:', error);
        return createResponse(500, {
            error: 'Failed to get channel',
            details: error.message
        });
    }
};
// Join a channel
const joinChannel = async (event) => {
    try {
        const channelId = event.pathParameters?.id;
        if (!channelId) {
            return createResponse(400, { error: 'Channel ID is required' });
        }
        const user = getUserFromContext(event);
        // Check if channel exists and is public
        const getChannelCommand = new lib_dynamodb_1.GetCommand({
            TableName: CHANNELS_TABLE,
            Key: { id: channelId }
        });
        const channelResult = await dynamodb.send(getChannelCommand);
        if (!channelResult.Item) {
            return createResponse(404, { error: 'Channel not found' });
        }
        const channel = channelResult.Item;
        if (!channel.isPublic) {
            return createResponse(403, { error: 'Cannot join private channel' });
        }
        // Check if user is already a member
        const getMembershipCommand = new lib_dynamodb_1.GetCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channelId: channelId,
                userId: user.userId
            }
        });
        const membershipResult = await dynamodb.send(getMembershipCommand);
        if (membershipResult.Item) {
            return createResponse(400, { error: 'Already a member of this channel' });
        }
        // Add user as member
        const now = new Date().toISOString();
        const putMemberCommand = new lib_dynamodb_1.PutCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Item: {
                channelId: channelId,
                userId: user.userId,
                role: 'member',
                joinedAt: now
            }
        });
        await dynamodb.send(putMemberCommand);
        // Update member count
        const updateChannelCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: CHANNELS_TABLE,
            Key: { id: channelId },
            UpdateExpression: 'ADD memberCount :inc SET updatedAt = :now',
            ExpressionAttributeValues: {
                ':inc': 1,
                ':now': now
            }
        });
        await dynamodb.send(updateChannelCommand);
        console.log(`User ${user.userId} joined channel ${channelId}`);
        return createResponse(200, {
            message: 'Successfully joined channel',
            channelId: channelId,
            userRole: 'member'
        });
    }
    catch (error) {
        console.error('Error joining channel:', error);
        return createResponse(500, {
            error: 'Failed to join channel',
            details: error.message
        });
    }
};
// Leave a channel
const leaveChannel = async (event) => {
    try {
        const channelId = event.pathParameters?.id;
        if (!channelId) {
            return createResponse(400, { error: 'Channel ID is required' });
        }
        const user = getUserFromContext(event);
        // Check if user is a member
        const getMembershipCommand = new lib_dynamodb_1.GetCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channelId: channelId,
                userId: user.userId
            }
        });
        const membershipResult = await dynamodb.send(getMembershipCommand);
        if (!membershipResult.Item) {
            return createResponse(400, { error: 'Not a member of this channel' });
        }
        const membership = membershipResult.Item;
        // Check if user is the owner
        if (membership.role === 'owner') {
            return createResponse(400, { error: 'Channel owner cannot leave channel' });
        }
        // Remove membership
        const deleteMemberCommand = new lib_dynamodb_1.DeleteCommand({
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channelId: channelId,
                userId: user.userId
            }
        });
        await dynamodb.send(deleteMemberCommand);
        // Update member count
        const now = new Date().toISOString();
        const updateChannelCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: CHANNELS_TABLE,
            Key: { id: channelId },
            UpdateExpression: 'ADD memberCount :dec SET updatedAt = :now',
            ExpressionAttributeValues: {
                ':dec': -1,
                ':now': now
            }
        });
        await dynamodb.send(updateChannelCommand);
        console.log(`User ${user.userId} left channel ${channelId}`);
        return createResponse(200, {
            message: 'Successfully left channel',
            channel_id: channelId
        });
    }
    catch (error) {
        console.error('Error leaving channel:', error);
        return createResponse(500, {
            error: 'Failed to leave channel',
            details: error.message
        });
    }
};
// Main handler
const handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    // Handle CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }
    try {
        const { httpMethod, resource } = event;
        switch (httpMethod) {
            case 'GET':
                if (resource === '/channels') {
                    return await getChannels(event);
                }
                else if (resource === '/channels/{id}') {
                    return await getChannel(event);
                }
                else if (resource === '/channels/{id}/posts') {
                    // TODO: Implement getChannelPosts
                    return createResponse(501, { error: 'Channel posts not implemented yet' });
                }
                break;
            case 'POST':
                if (resource === '/channels') {
                    return await createChannel(event);
                }
                else if (resource === '/channels/{id}/join') {
                    return await joinChannel(event);
                }
                else if (resource === '/channels/{id}/leave') {
                    return await leaveChannel(event);
                }
                break;
            case 'PUT':
                if (resource === '/channels/{id}') {
                    // TODO: Implement updateChannel
                    return createResponse(501, { error: 'Update channel not implemented yet' });
                }
                break;
            case 'DELETE':
                if (resource === '/channels/{id}') {
                    // TODO: Implement deleteChannel
                    return createResponse(501, { error: 'Delete channel not implemented yet' });
                }
                break;
            default:
                return createResponse(405, { error: 'Method not allowed' });
        }
        return createResponse(404, { error: 'Endpoint not found' });
    }
    catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: error.message
        });
    }
};
exports.handler = handler;
//# sourceMappingURL=data:application/json;base64,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