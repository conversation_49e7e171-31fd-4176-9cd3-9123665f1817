{"version": "tree-0.1", "tree": {"id": "App", "path": "", "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.206.0"}, "children": {"gameflex-development": {"id": "gameflex-development", "path": "gameflex-development", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.206.0"}, "children": {"R2Secret": {"id": "R2Secret", "path": "gameflex-development/R2Secret", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": []}}, "AppConfigSecret": {"id": "AppConfigSecret", "path": "gameflex-development/AppConfigSecret", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": []}}, "UserPool": {"id": "UserPool", "path": "gameflex-development/UserPool", "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.UserPool", "version": "2.206.0", "metadata": [{"userPoolName": "*", "autoVerify": {"email": true}, "signInAliases": {"email": true}, "passwordPolicy": {"minLength": "*", "requireUppercase": true, "requireLowercase": true, "requireDigits": true, "requireSymbols": false}, "deletionProtection": false, "removalPolicy": "destroy"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/UserPool/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.CfnUserPool", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Cognito::UserPool", "aws:cdk:cloudformation:props": {"accountRecoverySetting": {"recoveryMechanisms": [{"name": "verified_phone_number", "priority": 1}, {"name": "verified_email", "priority": 2}]}, "adminCreateUserConfig": {"allowAdminCreateUserOnly": true}, "autoVerifiedAttributes": ["email"], "deletionProtection": "INACTIVE", "emailVerificationMessage": "The verification code to your new account is {####}", "emailVerificationSubject": "Verify your new account", "policies": {"passwordPolicy": {"minimumLength": 8, "requireLowercase": true, "requireUppercase": true, "requireNumbers": true, "requireSymbols": false}}, "smsVerificationMessage": "The verification code to your new account is {####}", "usernameAttributes": ["email"], "userPoolName": "gameflex-users-development", "verificationMessageTemplate": {"defaultEmailOption": "CONFIRM_WITH_CODE", "emailMessage": "The verification code to your new account is {####}", "emailSubject": "Verify your new account", "smsMessage": "The verification code to your new account is {####}"}}}}}}, "UserPoolClient": {"id": "UserPoolClient", "path": "gameflex-development/UserPoolClient", "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.UserPoolClient", "version": "2.206.0", "metadata": [{"userPool": "*", "userPoolClientName": "*", "generateSecret": false, "authFlows": {"adminUserPassword": true, "userPassword": true, "userSrp": true}, "refreshTokenValidity": "*", "accessTokenValidity": "*", "idTokenValidity": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/UserPoolClient/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.CfnUserPoolClient", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Cognito::UserPoolClient", "aws:cdk:cloudformation:props": {"accessTokenValidity": 60, "allowedOAuthFlows": ["implicit", "code"], "allowedOAuthFlowsUserPoolClient": true, "allowedOAuthScopes": ["profile", "phone", "email", "openid", "aws.cognito.signin.user.admin"], "callbackUrLs": ["https://example.com"], "clientName": "gameflex-client-development", "explicitAuthFlows": ["ALLOW_USER_PASSWORD_AUTH", "ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"], "generateSecret": false, "idTokenValidity": 60, "refreshTokenValidity": 43200, "supportedIdentityProviders": ["COGNITO"], "tokenValidityUnits": {"idToken": "minutes", "accessToken": "minutes", "refreshToken": "minutes"}, "userPoolId": {"Ref": "UserPool6BA7E5F2"}}}}}}, "PostsTable": {"id": "PostsTable", "path": "gameflex-development/PostsTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.206.0", "metadata": [{"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/PostsTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "id", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "deletionProtectionEnabled": false, "keySchema": [{"attributeName": "id", "keyType": "HASH"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": false}, "tableName": "gameflex-development-Posts"}}}, "ScalingRole": {"id": "ScalingRole", "path": "gameflex-development/PostsTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}}}, "MediaTable": {"id": "MediaTable", "path": "gameflex-development/MediaTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.206.0", "metadata": [{"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/MediaTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "id", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "deletionProtectionEnabled": false, "keySchema": [{"attributeName": "id", "keyType": "HASH"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": false}, "tableName": "gameflex-development-Media"}}}, "ScalingRole": {"id": "ScalingRole", "path": "gameflex-development/MediaTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}}}, "UserProfilesTable": {"id": "UserProfilesTable", "path": "gameflex-development/UserProfilesTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.206.0", "metadata": [{"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/UserProfilesTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "userId", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "deletionProtectionEnabled": false, "keySchema": [{"attributeName": "userId", "keyType": "HASH"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": false}, "tableName": "gameflex-development-UserProfiles"}}}, "ScalingRole": {"id": "ScalingRole", "path": "gameflex-development/UserProfilesTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}}}, "CommentsTable": {"id": "CommentsTable", "path": "gameflex-development/CommentsTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.206.0", "metadata": [{"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}, {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/CommentsTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "id", "attributeType": "S"}, {"attributeName": "postId", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "deletionProtectionEnabled": false, "globalSecondaryIndexes": [{"indexName": "postId-index", "keySchema": [{"attributeName": "postId", "keyType": "HASH"}], "projection": {"projectionType": "ALL"}}], "keySchema": [{"attributeName": "id", "keyType": "HASH"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": false}, "tableName": "gameflex-development-Comments"}}}, "ScalingRole": {"id": "ScalingRole", "path": "gameflex-development/CommentsTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}}}, "LikesTable": {"id": "LikesTable", "path": "gameflex-development/LikesTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.206.0", "metadata": [{"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}, "sortKey": {"name": "*", "type": "S"}}, {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/LikesTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "postId", "attributeType": "S"}, {"attributeName": "userId", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "deletionProtectionEnabled": false, "globalSecondaryIndexes": [{"indexName": "userId-index", "keySchema": [{"attributeName": "userId", "keyType": "HASH"}], "projection": {"projectionType": "ALL"}}], "keySchema": [{"attributeName": "postId", "keyType": "HASH"}, {"attributeName": "userId", "keyType": "RANGE"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": false}, "tableName": "gameflex-development-Likes"}}}, "ScalingRole": {"id": "ScalingRole", "path": "gameflex-development/LikesTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}}}, "FollowsTable": {"id": "FollowsTable", "path": "gameflex-development/FollowsTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.206.0", "metadata": [{"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}, "sortKey": {"name": "*", "type": "S"}}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/FollowsTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "followerId", "attributeType": "S"}, {"attributeName": "followingId", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "deletionProtectionEnabled": false, "keySchema": [{"attributeName": "followerId", "keyType": "HASH"}, {"attributeName": "followingId", "keyType": "RANGE"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": false}, "tableName": "gameflex-development-Follows"}}}, "ScalingRole": {"id": "ScalingRole", "path": "gameflex-development/FollowsTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}}}, "ChannelsTable": {"id": "ChannelsTable", "path": "gameflex-development/ChannelsTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.206.0", "metadata": [{"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}, {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/ChannelsTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "id", "attributeType": "S"}, {"attributeName": "ownerId", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "deletionProtectionEnabled": false, "globalSecondaryIndexes": [{"indexName": "ownerId-index", "keySchema": [{"attributeName": "ownerId", "keyType": "HASH"}], "projection": {"projectionType": "ALL"}}], "keySchema": [{"attributeName": "id", "keyType": "HASH"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": false}, "tableName": "gameflex-development-Channels"}}}, "ScalingRole": {"id": "ScalingRole", "path": "gameflex-development/ChannelsTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}}}, "ChannelMembersTable": {"id": "ChannelMembersTable", "path": "gameflex-development/ChannelMembersTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.206.0", "metadata": [{"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}, "sortKey": {"name": "*", "type": "S"}}, {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/ChannelMembersTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "channelId", "attributeType": "S"}, {"attributeName": "userId", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "deletionProtectionEnabled": false, "globalSecondaryIndexes": [{"indexName": "userId-index", "keySchema": [{"attributeName": "userId", "keyType": "HASH"}], "projection": {"projectionType": "ALL"}}], "keySchema": [{"attributeName": "channelId", "keyType": "HASH"}, {"attributeName": "userId", "keyType": "RANGE"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": false}, "tableName": "gameflex-development-ChannelMembers"}}}, "ScalingRole": {"id": "ScalingRole", "path": "gameflex-development/ChannelMembersTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}}}, "ReflexesTable": {"id": "ReflexesTable", "path": "gameflex-development/ReflexesTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.206.0", "metadata": [{"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}, {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}, {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/ReflexesTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "id", "attributeType": "S"}, {"attributeName": "postId", "attributeType": "S"}, {"attributeName": "userId", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "deletionProtectionEnabled": false, "globalSecondaryIndexes": [{"indexName": "postId-index", "keySchema": [{"attributeName": "postId", "keyType": "HASH"}], "projection": {"projectionType": "ALL"}}, {"indexName": "userId-index", "keySchema": [{"attributeName": "userId", "keyType": "HASH"}], "projection": {"projectionType": "ALL"}}], "keySchema": [{"attributeName": "id", "keyType": "HASH"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": false}, "tableName": "gameflex-development-Reflexes"}}}, "ScalingRole": {"id": "ScalingRole", "path": "gameflex-development/ReflexesTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}}}, "UsersTable": {"id": "UsersTable", "path": "gameflex-development/UsersTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.206.0", "metadata": [{"billingMode": "PAY_PER_REQUEST", "deletionProtection": false, "pointInTimeRecovery": false, "removalPolicy": "destroy", "tableName": "*", "partitionKey": {"name": "*", "type": "S"}}, {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}, {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}, {"addGlobalSecondaryIndex": [{"indexName": "*", "partitionKey": {"name": "*", "type": "S"}}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/UsersTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "id", "attributeType": "S"}, {"attributeName": "email", "attributeType": "S"}, {"attributeName": "username", "attributeType": "S"}, {"attributeName": "cognitoUserId", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "deletionProtectionEnabled": false, "globalSecondaryIndexes": [{"indexName": "EmailIndex", "keySchema": [{"attributeName": "email", "keyType": "HASH"}], "projection": {"projectionType": "ALL"}}, {"indexName": "UsernameIndex", "keySchema": [{"attributeName": "username", "keyType": "HASH"}], "projection": {"projectionType": "ALL"}}, {"indexName": "CognitoUserIdIndex", "keySchema": [{"attributeName": "cognitoUserId", "keyType": "HASH"}], "projection": {"projectionType": "ALL"}}], "keySchema": [{"attributeName": "id", "keyType": "HASH"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": false}, "tableName": "gameflex-development-Users"}}}, "ScalingRole": {"id": "ScalingRole", "path": "gameflex-development/UsersTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}}}, "AuthorizerFunction": {"id": "AuthorizerFunction", "path": "gameflex-development/AuthorizerFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.206.0", "metadata": [{"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}]}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "gameflex-development/AuthorizerFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.206.0", "metadata": [{"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}, {"addToPrincipalPolicy": [{}]}, {"attachInlinePolicy": ["*"]}, {"attachInlinePolicy": ["*"]}]}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "gameflex-development/AuthorizerFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}, "Resource": {"id": "Resource", "path": "gameflex-development/AuthorizerFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "gameflex-development/AuthorizerFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.206.0", "metadata": ["*", {"attachToRole": ["*"]}, {"attachToRole": ["*"]}, {"addStatements": [{}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/AuthorizerFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": "cognito-idp:Get<PERSON>ser", "Effect": "Allow", "Resource": {"Fn::GetAtt": ["UserPool6BA7E5F2", "<PERSON><PERSON>"]}}], "Version": "2012-10-17"}, "policyName": "AuthorizerFunctionServiceRoleDefaultPolicy2C75A4EA", "roles": [{"Ref": "AuthorizerFunctionServiceRole5B2A061B"}]}}}}}}}, "Code": {"id": "Code", "path": "gameflex-development/AuthorizerFunction/Code", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.206.0"}, "children": {"Stage": {"id": "Stage", "path": "gameflex-development/AuthorizerFunction/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.206.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "gameflex-development/AuthorizerFunction/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.206.0", "metadata": []}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/AuthorizerFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "s3Key": "dc160347e535143f47254e1e24af8f1807dc8d0933b88e37a426c013a5055e1d.zip"}, "environment": {"variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "functionName": "gameflex-authorizer-development", "handler": "index.handler", "memorySize": 256, "role": {"Fn::GetAtt": ["AuthorizerFunctionServiceRole5B2A061B", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "timeout": 30}}}, "LogGroup": {"id": "LogGroup", "path": "gameflex-development/AuthorizerFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.206.0", "metadata": [{"logGroupName": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/AuthorizerFunction/LogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "AuthorizerFunctionB4DBAA43"}]]}, "retentionInDays": 731}}}}}, "gameflexdevelopmentDefaultAuthorizerCA84F39E:Permissions": {"id": "gameflexdevelopmentDefaultAuthorizerCA84F39E:Permissions", "path": "gameflex-development/AuthorizerFunction/gameflexdevelopmentDefaultAuthorizerCA84F39E:Permissions", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["AuthorizerFunctionB4DBAA43", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/authorizers/", {"Ref": "DefaultAuthorizerCA0170E0"}]]}}}}}}, "AuthFunction": {"id": "AuthFunction", "path": "gameflex-development/AuthFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.206.0", "metadata": [{"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}]}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "gameflex-development/AuthFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.206.0", "metadata": [{"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}, {"addToPrincipalPolicy": [{}]}, {"attachInlinePolicy": ["*"]}, {"attachInlinePolicy": ["*"]}, {"addToPrincipalPolicy": [{}]}]}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "gameflex-development/AuthFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}, "Resource": {"id": "Resource", "path": "gameflex-development/AuthFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "gameflex-development/AuthFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.206.0", "metadata": ["*", {"attachToRole": ["*"]}, {"attachToRole": ["*"]}, {"addStatements": [{}]}, {"addStatements": [{}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/AuthFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}]}, {"Action": "cognito-idp:*", "Effect": "Allow", "Resource": {"Fn::GetAtt": ["UserPool6BA7E5F2", "<PERSON><PERSON>"]}}], "Version": "2012-10-17"}, "policyName": "AuthFunctionServiceRoleDefaultPolicy4198AD5B", "roles": [{"Ref": "AuthFunctionServiceRole87A7A68C"}]}}}}}}}, "Code": {"id": "Code", "path": "gameflex-development/AuthFunction/Code", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.206.0"}, "children": {"Stage": {"id": "Stage", "path": "gameflex-development/AuthFunction/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.206.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "gameflex-development/AuthFunction/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.206.0", "metadata": []}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/AuthFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "s3Key": "ae58682233a278e182bdf174d49db13bd82e7f600c0d6ba017bcf1fe7e15c67c.zip"}, "environment": {"variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "functionName": "gameflex-auth-development", "handler": "index.handler", "memorySize": 256, "role": {"Fn::GetAtt": ["AuthFunctionServiceRole87A7A68C", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "timeout": 30}}}, "LogGroup": {"id": "LogGroup", "path": "gameflex-development/AuthFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.206.0", "metadata": [{"logGroupName": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/AuthFunction/LogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "AuthFunctionA1CD5E0F"}]]}, "retentionInDays": 731}}}}}}}, "PostsFunction": {"id": "PostsFunction", "path": "gameflex-development/PostsFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.206.0", "metadata": [{"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}]}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "gameflex-development/PostsFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.206.0", "metadata": [{"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}, {"addToPrincipalPolicy": [{}]}, {"attachInlinePolicy": ["*"]}, {"attachInlinePolicy": ["*"]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}]}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "gameflex-development/PostsFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}, "Resource": {"id": "Resource", "path": "gameflex-development/PostsFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "gameflex-development/PostsFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.206.0", "metadata": ["*", {"attachToRole": ["*"]}, {"attachToRole": ["*"]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/PostsFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["CommentsTableBBDBF0A8", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["LikesTable9511B0A4", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["MediaTableCFC93525", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["PostsTableC82B36F0", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["ReflexesTableD80A0AD3", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["CommentsTableBBDBF0A8", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["LikesTable9511B0A4", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ReflexesTableD80A0AD3", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Ref": "AWS::NoValue"}]}], "Version": "2012-10-17"}, "policyName": "PostsFunctionServiceRoleDefaultPolicyACA3B729", "roles": [{"Ref": "PostsFunctionServiceRoleF2DB8406"}]}}}}}}}, "Code": {"id": "Code", "path": "gameflex-development/PostsFunction/Code", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.206.0"}, "children": {"Stage": {"id": "Stage", "path": "gameflex-development/PostsFunction/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.206.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "gameflex-development/PostsFunction/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.206.0", "metadata": []}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/PostsFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "s3Key": "b868217bdf455fb562dd8a8ee6cd293e524b9fa4f8282824678df88bec1b6375.zip"}, "environment": {"variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "functionName": "gameflex-posts-development", "handler": "index.handler", "memorySize": 256, "role": {"Fn::GetAtt": ["PostsFunctionServiceRoleF2DB8406", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "timeout": 30}}}, "LogGroup": {"id": "LogGroup", "path": "gameflex-development/PostsFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.206.0", "metadata": [{"logGroupName": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/PostsFunction/LogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "PostsFunction3DC4AEDB"}]]}, "retentionInDays": 731}}}}}}}, "MediaFunction": {"id": "MediaFunction", "path": "gameflex-development/MediaFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.206.0", "metadata": [{"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}]}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "gameflex-development/MediaFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.206.0", "metadata": [{"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}, {"addToPrincipalPolicy": [{}]}, {"attachInlinePolicy": ["*"]}, {"attachInlinePolicy": ["*"]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}]}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "gameflex-development/MediaFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}, "Resource": {"id": "Resource", "path": "gameflex-development/MediaFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "gameflex-development/MediaFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.206.0", "metadata": ["*", {"attachToRole": ["*"]}, {"attachToRole": ["*"]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/MediaFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["MediaTableCFC93525", "<PERSON><PERSON>"]}, {"Ref": "AWS::NoValue"}]}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": ["arn:aws:secretsmanager:us-west-2:405316604661:secret:gameflex-app-config-development-??????", "arn:aws:secretsmanager:us-west-2:405316604661:secret:gameflex-r2-config-development-??????"]}], "Version": "2012-10-17"}, "policyName": "MediaFunctionServiceRoleDefaultPolicy8C41214C", "roles": [{"Ref": "MediaFunctionServiceRole8E5A8C78"}]}}}}}}}, "Code": {"id": "Code", "path": "gameflex-development/MediaFunction/Code", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.206.0"}, "children": {"Stage": {"id": "Stage", "path": "gameflex-development/MediaFunction/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.206.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "gameflex-development/MediaFunction/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.206.0", "metadata": []}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/MediaFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "s3Key": "85ac8ea8f2e87310bb51e3b76b3c5e1818dab94587199dcccdf5bac79696dfc6.zip"}, "environment": {"variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "functionName": "gameflex-media-development", "handler": "index.handler", "memorySize": 256, "role": {"Fn::GetAtt": ["MediaFunctionServiceRole8E5A8C78", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "timeout": 30}}}, "LogGroup": {"id": "LogGroup", "path": "gameflex-development/MediaFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.206.0", "metadata": [{"logGroupName": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/MediaFunction/LogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "MediaFunctionD33228E9"}]]}, "retentionInDays": 731}}}}}}}, "UsersFunction": {"id": "UsersFunction", "path": "gameflex-development/UsersFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.206.0", "metadata": [{"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}]}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "gameflex-development/UsersFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.206.0", "metadata": [{"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}, {"addToPrincipalPolicy": [{}]}, {"attachInlinePolicy": ["*"]}, {"attachInlinePolicy": ["*"]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}]}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "gameflex-development/UsersFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}, "Resource": {"id": "Resource", "path": "gameflex-development/UsersFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "gameflex-development/UsersFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.206.0", "metadata": ["*", {"attachToRole": ["*"]}, {"attachToRole": ["*"]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/UsersFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["FollowsTable7B81FE10", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["LikesTable9511B0A4", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["PostsTableC82B36F0", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UserProfilesTableF49D814C", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["LikesTable9511B0A4", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Ref": "AWS::NoValue"}]}], "Version": "2012-10-17"}, "policyName": "UsersFunctionServiceRoleDefaultPolicyDAA4211F", "roles": [{"Ref": "UsersFunctionServiceRoleAA0FFEE7"}]}}}}}}}, "Code": {"id": "Code", "path": "gameflex-development/UsersFunction/Code", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.206.0"}, "children": {"Stage": {"id": "Stage", "path": "gameflex-development/UsersFunction/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.206.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "gameflex-development/UsersFunction/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.206.0", "metadata": []}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/UsersFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "s3Key": "06b7d8ab0d4621a2b976de40da2d2d29f2fc5dcd2e5322c4b0eb5694c06c6845.zip"}, "environment": {"variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "functionName": "gameflex-users-development", "handler": "index.handler", "memorySize": 256, "role": {"Fn::GetAtt": ["UsersFunctionServiceRoleAA0FFEE7", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "timeout": 30}}}, "LogGroup": {"id": "LogGroup", "path": "gameflex-development/UsersFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.206.0", "metadata": [{"logGroupName": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/UsersFunction/LogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "UsersFunction1976AE51"}]]}, "retentionInDays": 731}}}}}}}, "HealthFunction": {"id": "HealthFunction", "path": "gameflex-development/HealthFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.206.0", "metadata": [{"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}]}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "gameflex-development/HealthFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.206.0", "metadata": [{"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}, {"addToPrincipalPolicy": [{}]}, {"attachInlinePolicy": ["*"]}, {"attachInlinePolicy": ["*"]}]}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "gameflex-development/HealthFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}, "Resource": {"id": "Resource", "path": "gameflex-development/HealthFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "gameflex-development/HealthFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.206.0", "metadata": ["*", {"attachToRole": ["*"]}, {"attachToRole": ["*"]}, {"addStatements": [{}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/HealthFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:ConditionCheckItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:Query", "dynamodb:<PERSON><PERSON>"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}]}], "Version": "2012-10-17"}, "policyName": "HealthFunctionServiceRoleDefaultPolicy57228674", "roles": [{"Ref": "HealthFunctionServiceRole04552894"}]}}}}}}}, "Code": {"id": "Code", "path": "gameflex-development/HealthFunction/Code", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.206.0"}, "children": {"Stage": {"id": "Stage", "path": "gameflex-development/HealthFunction/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.206.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "gameflex-development/HealthFunction/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.206.0", "metadata": []}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/HealthFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "s3Key": "a436ae863fe1b0734db0e07398b70d1b8615769d3a580d0eb63569b74907d9f3.zip"}, "environment": {"variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "functionName": "gameflex-health-development", "handler": "index.handler", "memorySize": 256, "role": {"Fn::GetAtt": ["HealthFunctionServiceRole04552894", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "timeout": 30}}}, "LogGroup": {"id": "LogGroup", "path": "gameflex-development/HealthFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.206.0", "metadata": [{"logGroupName": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/HealthFunction/LogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "HealthFunction19D7724A"}]]}, "retentionInDays": 731}}}}}}}, "ReflexesFunction": {"id": "ReflexesFunction", "path": "gameflex-development/ReflexesFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.206.0", "metadata": [{"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}]}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "gameflex-development/ReflexesFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.206.0", "metadata": [{"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}, {"addToPrincipalPolicy": [{}]}, {"attachInlinePolicy": ["*"]}, {"attachInlinePolicy": ["*"]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}]}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "gameflex-development/ReflexesFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}, "Resource": {"id": "Resource", "path": "gameflex-development/ReflexesFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "gameflex-development/ReflexesFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.206.0", "metadata": ["*", {"attachToRole": ["*"]}, {"attachToRole": ["*"]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/ReflexesFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["MediaTableCFC93525", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["PostsTableC82B36F0", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["ReflexesTableD80A0AD3", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ReflexesTableD80A0AD3", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Ref": "AWS::NoValue"}]}], "Version": "2012-10-17"}, "policyName": "ReflexesFunctionServiceRoleDefaultPolicyEBCECFAA", "roles": [{"Ref": "ReflexesFunctionServiceRole768786C8"}]}}}}}}}, "Code": {"id": "Code", "path": "gameflex-development/ReflexesFunction/Code", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.206.0"}, "children": {"Stage": {"id": "Stage", "path": "gameflex-development/ReflexesFunction/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.206.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "gameflex-development/ReflexesFunction/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.206.0", "metadata": []}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/ReflexesFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "s3Key": "f1e4164a456d2b28d440eb37f99aa8016eb94f92c9458380b112ff73b87df2cc.zip"}, "environment": {"variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "functionName": "gameflex-reflexes-development", "handler": "index.handler", "memorySize": 256, "role": {"Fn::GetAtt": ["ReflexesFunctionServiceRole768786C8", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "timeout": 30}}}, "LogGroup": {"id": "LogGroup", "path": "gameflex-development/ReflexesFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.206.0", "metadata": [{"logGroupName": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/ReflexesFunction/LogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "ReflexesFunction5F91D3B2"}]]}, "retentionInDays": 731}}}}}}}, "ChannelsFunction": {"id": "ChannelsFunction", "path": "gameflex-development/ChannelsFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.206.0", "metadata": [{"functionName": "*", "runtime": "*", "handler": "*", "code": "*", "timeout": "*", "memorySize": "*", "environment": "*"}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}, {"addEnvironment": ["*", "*"]}]}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "gameflex-development/ChannelsFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.206.0", "metadata": [{"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "managedPolicies": [{"managedPolicyArn": "*"}]}, {"addToPrincipalPolicy": [{}]}, {"attachInlinePolicy": ["*"]}, {"attachInlinePolicy": ["*"]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}, {"addToPrincipalPolicy": [{}]}]}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "gameflex-development/ChannelsFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.206.0", "metadata": ["*"]}}, "Resource": {"id": "Resource", "path": "gameflex-development/ChannelsFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "gameflex-development/ChannelsFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.206.0", "metadata": ["*", {"attachToRole": ["*"]}, {"attachToRole": ["*"]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}, {"addStatements": [{}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/ChannelsFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ChannelMembersTable93E36D7C", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["ChannelsTable6C883730", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["MediaTableCFC93525", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["PostsTableC82B36F0", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ChannelMembersTable93E36D7C", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ChannelsTable6C883730", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Ref": "AWS::NoValue"}]}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": ["arn:aws:secretsmanager:us-west-2:405316604661:secret:gameflex-app-config-development-??????", "arn:aws:secretsmanager:us-west-2:405316604661:secret:gameflex-r2-config-development-??????"]}], "Version": "2012-10-17"}, "policyName": "ChannelsFunctionServiceRoleDefaultPolicy6E3FD81C", "roles": [{"Ref": "ChannelsFunctionServiceRole0A782FB9"}]}}}}}}}, "Code": {"id": "Code", "path": "gameflex-development/ChannelsFunction/Code", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.206.0"}, "children": {"Stage": {"id": "Stage", "path": "gameflex-development/ChannelsFunction/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.206.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "gameflex-development/ChannelsFunction/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.206.0", "metadata": []}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/ChannelsFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "s3Key": "3755b8241ac0ed1969259e60e80337e004fe247c518848c42e22ee0527cb9e9b.zip"}, "environment": {"variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "functionName": "gameflex-channels-development", "handler": "index.handler", "memorySize": 256, "role": {"Fn::GetAtt": ["ChannelsFunctionServiceRole0A782FB9", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "timeout": 30}}}, "LogGroup": {"id": "LogGroup", "path": "gameflex-development/ChannelsFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.206.0", "metadata": [{"logGroupName": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/ChannelsFunction/LogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "ChannelsFunction9B818B1B"}]]}, "retentionInDays": 731}}}}}}}, "GameFlexApi": {"id": "GameFlexApi", "path": "gameflex-development/GameFlexApi", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.RestApi", "version": "2.206.0", "metadata": [{"restApiName": "*", "description": "*", "defaultCorsPreflightOptions": {"allowOrigins": "*", "allowMethods": "*", "allowHeaders": "*"}, "deployOptions": {"stageName": "*", "throttlingRateLimit": "*", "throttlingBurstLimit": "*"}}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnRestApi", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::RestApi", "aws:cdk:cloudformation:props": {"description": "GameFlex API for development environment", "name": "gameflex-api-development"}}}, "Deployment": {"id": "Deployment", "path": "gameflex-development/GameFlexApi/Deployment", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Deployment", "version": "2.206.0", "metadata": [{"description": "*", "api": "*", "retainDeployments": "*"}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}, {"addToLogicalId": [{}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Deployment/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnDeployment", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Deployment", "aws:cdk:cloudformation:props": {"description": "GameFlex API for development environment", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "DeploymentStage.v1": {"id": "DeploymentStage.v1", "path": "gameflex-development/GameFlexApi/DeploymentStage.v1", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Stage", "version": "2.206.0", "metadata": [{"deployment": "*", "stageName": "*", "throttlingRateLimit": "*", "throttlingBurstLimit": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/DeploymentStage.v1/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnStage", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Stage", "aws:cdk:cloudformation:props": {"deploymentId": {"Ref": "GameFlexApiDeployment8960269Dd23fb76cc0ff6720e32e2ac231ca298a"}, "methodSettings": [{"httpMethod": "*", "resourcePath": "/*", "dataTraceEnabled": false, "throttlingBurstLimit": 2000, "throttlingRateLimit": 1000}], "restApiId": {"Ref": "GameFlexApiC965E6C8"}, "stageName": "v1"}}}}}, "Endpoint": {"id": "Endpoint", "path": "gameflex-development/GameFlexApi/Endpoint", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "Default": {"id": "<PERSON><PERSON><PERSON>", "path": "gameflex-development/GameFlexApi/Default", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.ResourceBase", "version": "2.206.0", "metadata": ["*"]}, "children": {"OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "health": {"id": "health", "path": "gameflex-development/GameFlexApi/Default/health", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/health/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "pathPart": "health", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/health/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/health/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApihealth412F2DFA"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/health/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": "*"}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..health": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..health", "path": "gameflex-development/GameFlexApi/Default/health/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..health", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["HealthFunction19D7724A", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/health"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..health": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..health", "path": "gameflex-development/GameFlexApi/Default/health/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..health", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["HealthFunction19D7724A", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/health"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/health/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "NONE", "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["HealthFunction19D7724A", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApihealth412F2DFA"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}, "auth": {"id": "auth", "path": "gameflex-development/GameFlexApi/Default/auth", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/auth/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "pathPart": "auth", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/auth/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/auth/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApiauth34DB0BF1"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "POST": {"id": "POST", "path": "gameflex-development/GameFlexApi/Default/auth/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": "*"}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..auth": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..auth", "path": "gameflex-development/GameFlexApi/Default/auth/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..auth", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/auth"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..auth": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..auth", "path": "gameflex-development/GameFlexApi/Default/auth/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..auth", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/auth"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/auth/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "NONE", "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiauth34DB0BF1"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/auth/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": "*"}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..auth": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..auth", "path": "gameflex-development/GameFlexApi/Default/auth/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..auth", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/auth"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..auth": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..auth", "path": "gameflex-development/GameFlexApi/Default/auth/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..auth", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/auth"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/auth/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "NONE", "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiauth34DB0BF1"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}, "posts": {"id": "posts", "path": "gameflex-development/GameFlexApi/Default/posts", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "pathPart": "posts", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/posts/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApiposts137B57F5"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/posts/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts", "path": "gameflex-development/GameFlexApi/Default/posts/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/posts"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts", "path": "gameflex-development/GameFlexApi/Default/posts/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/posts"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiposts137B57F5"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "POST": {"id": "POST", "path": "gameflex-development/GameFlexApi/Default/posts/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts", "path": "gameflex-development/GameFlexApi/Default/posts/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/posts"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts", "path": "gameflex-development/GameFlexApi/Default/posts/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/posts"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiposts137B57F5"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "draft": {"id": "draft", "path": "gameflex-development/GameFlexApi/Default/posts/draft", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/draft/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApiposts137B57F5"}, "pathPart": "draft", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/posts/draft/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/draft/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApipostsdraftAAEA2551"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "POST": {"id": "POST", "path": "gameflex-development/GameFlexApi/Default/posts/draft/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.draft": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.draft", "path": "gameflex-development/GameFlexApi/Default/posts/draft/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.draft", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/posts/draft"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.draft": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.draft", "path": "gameflex-development/GameFlexApi/Default/posts/draft/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.draft", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/posts/draft"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/draft/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApipostsdraftAAEA2551"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}, "{id}": {"id": "{id}", "path": "gameflex-development/GameFlexApi/Default/posts/{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApiposts137B57F5"}, "pathPart": "{id}", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApipostsidEA3F073A"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/posts/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/posts/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApipostsidEA3F073A"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "PUT": {"id": "PUT", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/PUT", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/posts/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/posts/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/PUT/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "PUT", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApipostsidEA3F073A"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "DELETE": {"id": "DELETE", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/DELETE", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/DELETE/posts/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/DELETE/posts/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/DELETE/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "DELETE", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApipostsidEA3F073A"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "media": {"id": "media", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/media", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApipostsidEA3F073A"}, "pathPart": "media", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApipostsidmediaA8DE1E9D"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "PUT": {"id": "PUT", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.media": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.media", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.media", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/posts/*/media"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.media": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.media", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.media", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/posts/*/media"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "PUT", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApipostsidmediaA8DE1E9D"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}, "publish": {"id": "publish", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApipostsidEA3F073A"}, "pathPart": "publish", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApipostsidpublish67AF9D0C"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "PUT": {"id": "PUT", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.publish": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.publish", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.publish", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/posts/*/publish"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.publish": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.publish", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.publish", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/posts/*/publish"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "PUT", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApipostsidpublish67AF9D0C"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}, "like": {"id": "like", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApipostsidEA3F073A"}, "pathPart": "like", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApipostsidlikeCDEA7366"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "POST": {"id": "POST", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.like": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.like", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.like", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/posts/*/like"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.like": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.like", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.like", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/posts/*/like"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApipostsidlikeCDEA7366"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "DELETE": {"id": "DELETE", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}.like": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}.like", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}.like", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/DELETE/posts/*/like"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}.like": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}.like", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}.like", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/DELETE/posts/*/like"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "DELETE", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApipostsidlikeCDEA7366"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}, "comments": {"id": "comments", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApipostsidEA3F073A"}, "pathPart": "comments", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApipostsidcomments3BE1F05E"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}.comments": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}.comments", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}.comments", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/posts/*/comments"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}.comments": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}.comments", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}.comments", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/posts/*/comments"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApipostsidcomments3BE1F05E"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "POST": {"id": "POST", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.comments": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.comments", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.comments", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/posts/*/comments"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.comments": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.comments", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.comments", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/posts/*/comments"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApipostsidcomments3BE1F05E"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}}}}}, "users": {"id": "users", "path": "gameflex-development/GameFlexApi/Default/users", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "pathPart": "users", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/users/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApiusers539BBD8B"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/users/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users", "path": "gameflex-development/GameFlexApi/Default/users/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/users"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users", "path": "gameflex-development/GameFlexApi/Default/users/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/users"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiusers539BBD8B"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "POST": {"id": "POST", "path": "gameflex-development/GameFlexApi/Default/users/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..users": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..users", "path": "gameflex-development/GameFlexApi/Default/users/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..users", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/users"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..users": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..users", "path": "gameflex-development/GameFlexApi/Default/users/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..users", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/users"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiusers539BBD8B"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "{id}": {"id": "{id}", "path": "gameflex-development/GameFlexApi/Default/users/{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/{id}/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApiusers539BBD8B"}, "pathPart": "{id}", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/users/{id}/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/{id}/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApiusersidDF782D5C"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/users/{id}/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.{id}", "path": "gameflex-development/GameFlexApi/Default/users/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/users/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.{id}", "path": "gameflex-development/GameFlexApi/Default/users/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/users/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/{id}/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiusersidDF782D5C"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "PUT": {"id": "PUT", "path": "gameflex-development/GameFlexApi/Default/users/{id}/PUT", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.{id}", "path": "gameflex-development/GameFlexApi/Default/users/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/users/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.{id}", "path": "gameflex-development/GameFlexApi/Default/users/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/users/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/{id}/PUT/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "PUT", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiusersidDF782D5C"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}, "profile": {"id": "profile", "path": "gameflex-development/GameFlexApi/Default/users/profile", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/profile/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApiusers539BBD8B"}, "pathPart": "profile", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/users/profile/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/profile/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApiusersprofileE72A3ECA"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/users/profile/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile", "path": "gameflex-development/GameFlexApi/Default/users/profile/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/users/profile"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile", "path": "gameflex-development/GameFlexApi/Default/users/profile/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/users/profile"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/profile/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiusersprofileE72A3ECA"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "PUT": {"id": "PUT", "path": "gameflex-development/GameFlexApi/Default/users/profile/PUT", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.profile": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.profile", "path": "gameflex-development/GameFlexApi/Default/users/profile/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.profile", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/users/profile"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.profile": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.profile", "path": "gameflex-development/GameFlexApi/Default/users/profile/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.profile", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/users/profile"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/profile/PUT/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "PUT", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiusersprofileE72A3ECA"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "liked-posts": {"id": "liked-posts", "path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApiusersprofileE72A3ECA"}, "pathPart": "liked-posts", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApiusersprofilelikedposts922C1EB7"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile.liked-posts": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile.liked-posts", "path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile.liked-posts", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/users/profile/liked-posts"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile.liked-posts": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile.liked-posts", "path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile.liked-posts", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/users/profile/liked-posts"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApiusersprofilelikedposts922C1EB7"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}}}}}, "media": {"id": "media", "path": "gameflex-development/GameFlexApi/Default/media", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "pathPart": "media", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/media/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApimedia4CFF2055"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/media/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..media": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..media", "path": "gameflex-development/GameFlexApi/Default/media/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..media", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/media"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..media": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..media", "path": "gameflex-development/GameFlexApi/Default/media/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..media", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/media"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApimedia4CFF2055"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "POST": {"id": "POST", "path": "gameflex-development/GameFlexApi/Default/media/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..media": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..media", "path": "gameflex-development/GameFlexApi/Default/media/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..media", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/media"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..media": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..media", "path": "gameflex-development/GameFlexApi/Default/media/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..media", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/media"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApimedia4CFF2055"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "upload": {"id": "upload", "path": "gameflex-development/GameFlexApi/Default/media/upload", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/upload/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApimedia4CFF2055"}, "pathPart": "upload", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/media/upload/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/upload/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApimediaupload246052B2"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "POST": {"id": "POST", "path": "gameflex-development/GameFlexApi/Default/media/upload/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..media.upload": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..media.upload", "path": "gameflex-development/GameFlexApi/Default/media/upload/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..media.upload", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/media/upload"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..media.upload": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..media.upload", "path": "gameflex-development/GameFlexApi/Default/media/upload/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..media.upload", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/media/upload"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/upload/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApimediaupload246052B2"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}, "{id}": {"id": "{id}", "path": "gameflex-development/GameFlexApi/Default/media/{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/{id}/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApimedia4CFF2055"}, "pathPart": "{id}", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/media/{id}/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/{id}/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApimediaidB68B38E9"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/media/{id}/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..media.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..media.{id}", "path": "gameflex-development/GameFlexApi/Default/media/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..media.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/media/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..media.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..media.{id}", "path": "gameflex-development/GameFlexApi/Default/media/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..media.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/media/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/{id}/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApimediaidB68B38E9"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "PUT": {"id": "PUT", "path": "gameflex-development/GameFlexApi/Default/media/{id}/PUT", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..media.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..media.{id}", "path": "gameflex-development/GameFlexApi/Default/media/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..media.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/media/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..media.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..media.{id}", "path": "gameflex-development/GameFlexApi/Default/media/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..media.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/media/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/{id}/PUT/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "PUT", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApimediaidB68B38E9"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "DELETE": {"id": "DELETE", "path": "gameflex-development/GameFlexApi/Default/media/{id}/DELETE", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..media.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..media.{id}", "path": "gameflex-development/GameFlexApi/Default/media/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..media.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/DELETE/media/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..media.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..media.{id}", "path": "gameflex-development/GameFlexApi/Default/media/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..media.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/DELETE/media/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/media/{id}/DELETE/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "DELETE", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApimediaidB68B38E9"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}}}, "reflexes": {"id": "reflexes", "path": "gameflex-development/GameFlexApi/Default/reflexes", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/reflexes/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "pathPart": "reflexes", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/reflexes/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/reflexes/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApireflexesF9B892EC"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/reflexes/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes", "path": "gameflex-development/GameFlexApi/Default/reflexes/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/reflexes"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes", "path": "gameflex-development/GameFlexApi/Default/reflexes/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/reflexes"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/reflexes/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApireflexesF9B892EC"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "POST": {"id": "POST", "path": "gameflex-development/GameFlexApi/Default/reflexes/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..reflexes": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..reflexes", "path": "gameflex-development/GameFlexApi/Default/reflexes/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..reflexes", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/reflexes"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..reflexes": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..reflexes", "path": "gameflex-development/GameFlexApi/Default/reflexes/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..reflexes", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/reflexes"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/reflexes/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApireflexesF9B892EC"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "{id}": {"id": "{id}", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApireflexesF9B892EC"}, "pathPart": "{id}", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApireflexesidFB646CA8"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes.{id}", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/reflexes/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes.{id}", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/reflexes/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApireflexesidFB646CA8"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "PUT": {"id": "PUT", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..reflexes.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..reflexes.{id}", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..reflexes.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/reflexes/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..reflexes.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..reflexes.{id}", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..reflexes.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/reflexes/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "PUT", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApireflexesidFB646CA8"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "DELETE": {"id": "DELETE", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..reflexes.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..reflexes.{id}", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..reflexes.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/DELETE/reflexes/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..reflexes.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..reflexes.{id}", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..reflexes.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/DELETE/reflexes/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "DELETE", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApireflexesidFB646CA8"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}}}, "channels": {"id": "channels", "path": "gameflex-development/GameFlexApi/Default/channels", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/channels/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "pathPart": "channels", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/channels/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/channels/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApichannels7A12BEA5"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/channels/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels", "path": "gameflex-development/GameFlexApi/Default/channels/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/channels"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels", "path": "gameflex-development/GameFlexApi/Default/channels/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/channels"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/channels/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApichannels7A12BEA5"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "POST": {"id": "POST", "path": "gameflex-development/GameFlexApi/Default/channels/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..channels": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..channels", "path": "gameflex-development/GameFlexApi/Default/channels/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..channels", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/channels"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..channels": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..channels", "path": "gameflex-development/GameFlexApi/Default/channels/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..channels", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/channels"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/channels/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApichannels7A12BEA5"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "{id}": {"id": "{id}", "path": "gameflex-development/GameFlexApi/Default/channels/{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.206.0", "metadata": [{"parent": "*", "pathPart": "*"}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Ref": "GameFlexApichannels7A12BEA5"}, "pathPart": "{id}", "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": {"authorizerId": "*", "authorizationType": "*"}, "apiKeyRequired": false, "authorizationType": "*", "methodResponses": [{"statusCode": "*", "responseParameters": "*"}]}}, {"addMethodResponse": [{"statusCode": "*", "responseParameters": "*"}]}]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "GameFlexApichannelsidA7085A68"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "GET": {"id": "GET", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels.{id}", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/channels/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels.{id}", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/channels/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "GET", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApichannelsidA7085A68"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "PUT": {"id": "PUT", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/PUT", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..channels.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..channels.{id}", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..channels.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/channels/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..channels.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..channels.{id}", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..channels.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/channels/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/PUT/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "PUT", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApichannelsidA7085A68"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}, "DELETE": {"id": "DELETE", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/DELETE", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.206.0", "metadata": [{"resource": "*", "httpMethod": "*", "integration": "*", "options": {"authorizer": "*"}}]}, "children": {"ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..channels.{id}": {"id": "ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..channels.{id}", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..channels.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/DELETE/channels/*"]]}}}}, "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..channels.{id}": {"id": "ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..channels.{id}", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..channels.{id}", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/DELETE/channels/*"]]}}}}, "Resource": {"id": "Resource", "path": "gameflex-development/GameFlexApi/Default/channels/{id}/DELETE/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "CUSTOM", "authorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "httpMethod": "DELETE", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "GameFlexApichannelsidA7085A68"}, "restApiId": {"Ref": "GameFlexApiC965E6C8"}}}}}}}}}}}}}}, "DefaultAuthorizer": {"id": "DefaultAuthorizer", "path": "gameflex-development/DefaultAuthorizer", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.TokenAuthorizer", "version": "2.206.0", "metadata": ["*"]}, "children": {"Resource": {"id": "Resource", "path": "gameflex-development/DefaultAuthorizer/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnAuthorizer", "version": "2.206.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Authorizer", "aws:cdk:cloudformation:props": {"authorizerResultTtlInSeconds": 300, "authorizerUri": {"Fn::Join": ["", ["arn:", {"Fn::Select": [1, {"Fn::Split": [":", {"Fn::GetAtt": ["AuthorizerFunctionB4DBAA43", "<PERSON><PERSON>"]}]}]}, ":apigateway:", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["AuthorizerFunctionB4DBAA43", "<PERSON><PERSON>"]}]}]}, ":lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["AuthorizerFunctionB4DBAA43", "<PERSON><PERSON>"]}, "/invocations"]]}, "identitySource": "method.request.header.Authorization", "name": "DefaultAuthorizer", "restApiId": {"Ref": "GameFlexApiC965E6C8"}, "type": "TOKEN"}}}}}, "ApiGatewayUrl": {"id": "ApiGatewayUrl", "path": "gameflex-development/ApiGatewayUrl", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "UserPoolId": {"id": "UserPoolId", "path": "gameflex-development/UserPoolId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "UserPoolClientId": {"id": "UserPoolClientId", "path": "gameflex-development/UserPoolClientId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "R2SecretArn": {"id": "R2SecretArn", "path": "gameflex-development/R2SecretArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "AppConfigSecretArn": {"id": "AppConfigSecretArn", "path": "gameflex-development/AppConfigSecretArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "PostsTableName": {"id": "PostsTableName", "path": "gameflex-development/PostsTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "MediaTableName": {"id": "MediaTableName", "path": "gameflex-development/MediaTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "UserProfilesTableName": {"id": "UserProfilesTableName", "path": "gameflex-development/UserProfilesTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "CommentsTableName": {"id": "CommentsTableName", "path": "gameflex-development/CommentsTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "LikesTableName": {"id": "LikesTableName", "path": "gameflex-development/LikesTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "FollowsTableName": {"id": "FollowsTable<PERSON>ame", "path": "gameflex-development/FollowsTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "ChannelsTableName": {"id": "ChannelsTableName", "path": "gameflex-development/ChannelsTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "ChannelMembersTableName": {"id": "ChannelMembersTableName", "path": "gameflex-development/ChannelMembersTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "ReflexesTableName": {"id": "ReflexesTableName", "path": "gameflex-development/ReflexesTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "UsersTableName": {"id": "UsersTableName", "path": "gameflex-development/UsersTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.206.0"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "gameflex-development/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "gameflex-development/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.206.0"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "gameflex-development/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.206.0"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "gameflex-development/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.206.0"}}}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}}}