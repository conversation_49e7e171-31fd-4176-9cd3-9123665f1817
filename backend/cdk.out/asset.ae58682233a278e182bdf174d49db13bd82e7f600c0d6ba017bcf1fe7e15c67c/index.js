"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_cognito_identity_provider_1 = require("@aws-sdk/client-cognito-identity-provider");
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
// Configure AWS SDK for SAM local
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};
// For local development with SAM, we need to set the endpoint if AWS_SAM_LOCAL is set
if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}
const cognitoClient = new client_cognito_identity_provider_1.CognitoIdentityProviderClient(awsConfig);
const dynamoClient = new client_dynamodb_1.DynamoDBClient(awsConfig);
const dynamodb = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamoClient);
// Environment variables - check multiple possible names for compatibility
const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;
console.log('Environment variables:');
console.log('USER_POOL_ID:', USER_POOL_ID);
console.log('USER_POOL_CLIENT_ID:', USER_POOL_CLIENT_ID);
console.log('USERS_TABLE:', USERS_TABLE);
// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});
// Sign up function
const signUp = async (event) => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { email, password, username, firstName, lastName } = JSON.parse(event.body);
        if (!email || !password || !username) {
            return createResponse(400, { error: 'Email, password, and username are required' });
        }
        if (!USER_POOL_ID) {
            return createResponse(500, { error: 'User pool configuration missing' });
        }
        // Create user in Cognito
        const cognitoParams = {
            UserPoolId: USER_POOL_ID,
            Username: email,
            TemporaryPassword: password,
            MessageAction: 'SUPPRESS',
            UserAttributes: [
                { Name: 'email', Value: email },
                { Name: 'email_verified', Value: 'true' },
                { Name: 'given_name', Value: firstName || '' },
                { Name: 'family_name', Value: lastName || '' }
            ]
        };
        const createUserCommand = new client_cognito_identity_provider_1.AdminCreateUserCommand(cognitoParams);
        const cognitoUser = await cognitoClient.send(createUserCommand);
        // Set permanent password
        const setPasswordCommand = new client_cognito_identity_provider_1.AdminSetUserPasswordCommand({
            UserPoolId: USER_POOL_ID,
            Username: email,
            Password: password,
            Permanent: true
        });
        await cognitoClient.send(setPasswordCommand);
        // Get the user details to extract the sub (user ID)
        const getUserCommand = new client_cognito_identity_provider_1.AdminGetUserCommand({
            UserPoolId: USER_POOL_ID,
            Username: email
        });
        const userDetails = await cognitoClient.send(getUserCommand);
        // Extract the sub attribute which will be our user ID
        const userAttributes = {};
        userDetails.UserAttributes?.forEach((attr) => {
            if (attr.Name && attr.Value) {
                userAttributes[attr.Name] = attr.Value;
            }
        });
        const userId = userAttributes.sub;
        if (!userId) {
            return createResponse(500, { error: 'Failed to get user ID from Cognito' });
        }
        const userRecord = {
            id: userId,
            email,
            username,
            firstName: firstName || '',
            lastName: lastName || '',
            cognitoUserId: cognitoUser.User?.Username || email,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }
        const putCommand = new lib_dynamodb_1.PutCommand({
            TableName: USERS_TABLE,
            Item: userRecord
        });
        await dynamodb.send(putCommand);
        return createResponse(201, {
            message: 'User created successfully',
            user: {
                id: userId,
                email,
                username,
                firstName: firstName || '',
                lastName: lastName || ''
            }
        });
    }
    catch (error) {
        console.error('SignUp error:', error);
        return createResponse(500, { error: 'Failed to create user', details: error.message });
    }
};
// Sign in function
const signIn = async (event) => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { email, password } = JSON.parse(event.body);
        if (!email || !password) {
            return createResponse(400, { error: 'Email and password are required' });
        }
        if (!USER_POOL_ID || !USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'Cognito configuration missing' });
        }
        // Authenticate with Cognito
        const authParams = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'ADMIN_USER_PASSWORD_AUTH',
            AuthParameters: {
                USERNAME: email,
                PASSWORD: password
            }
        };
        const authCommand = new client_cognito_identity_provider_1.AdminInitiateAuthCommand(authParams);
        const authResult = await cognitoClient.send(authCommand);
        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }
        // Get user details from DynamoDB
        const queryCommand = new lib_dynamodb_1.QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'EmailIndex',
            KeyConditionExpression: 'email = :email',
            ExpressionAttributeValues: {
                ':email': email
            }
        });
        const userResult = await dynamodb.send(queryCommand);
        if (!userResult.Items || userResult.Items.length === 0) {
            return createResponse(404, { error: 'User not found' });
        }
        const user = userResult.Items[0];
        if (!authResult.AuthenticationResult) {
            return createResponse(401, { error: 'Authentication failed' });
        }
        return createResponse(200, {
            message: 'Sign in successful',
            tokens: {
                accessToken: authResult.AuthenticationResult.AccessToken,
                refreshToken: authResult.AuthenticationResult.RefreshToken,
                idToken: authResult.AuthenticationResult.IdToken
            },
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName
            }
        });
    }
    catch (error) {
        console.error('SignIn error:', error);
        return createResponse(401, { error: 'Authentication failed', details: error.message });
    }
};
// Refresh token function
const refreshToken = async (event) => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { refreshToken } = JSON.parse(event.body);
        if (!refreshToken) {
            return createResponse(400, { error: 'Refresh token is required' });
        }
        if (!USER_POOL_ID || !USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'Cognito configuration missing' });
        }
        const refreshParams = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'REFRESH_TOKEN_AUTH',
            AuthParameters: {
                REFRESH_TOKEN: refreshToken
            }
        };
        const refreshCommand = new client_cognito_identity_provider_1.AdminInitiateAuthCommand(refreshParams);
        const authResult = await cognitoClient.send(refreshCommand);
        if (!authResult.AuthenticationResult) {
            return createResponse(401, { error: 'Token refresh failed' });
        }
        return createResponse(200, {
            message: 'Token refreshed successfully',
            tokens: {
                accessToken: authResult.AuthenticationResult.AccessToken,
                idToken: authResult.AuthenticationResult.IdToken
            }
        });
    }
    catch (error) {
        console.error('RefreshToken error:', error);
        return createResponse(401, { error: 'Token refresh failed', details: error.message });
    }
};
// Validate token function
const validateToken = async (event) => {
    try {
        // Get token from Authorization header
        const authHeader = event.headers?.Authorization || event.headers?.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return createResponse(401, { error: 'Authorization header with Bearer token required' });
        }
        const accessToken = authHeader.substring(7);
        try {
            // Validate token with Cognito
            const getUserParams = {
                AccessToken: accessToken
            };
            const getUserCommand = new client_cognito_identity_provider_1.GetUserCommand(getUserParams);
            const cognitoUser = await cognitoClient.send(getUserCommand);
            // Get the user ID from Cognito (this is the 'sub' claim in the JWT)
            const userId = cognitoUser.Username; // This is actually the user ID in Cognito
            if (!userId) {
                return createResponse(401, { error: 'Invalid token - no user ID' });
            }
            if (!USERS_TABLE) {
                return createResponse(500, { error: 'Users table configuration missing' });
            }
            // Get user details from DynamoDB using user ID
            const getCommand = new lib_dynamodb_1.GetCommand({
                TableName: USERS_TABLE,
                Key: {
                    id: userId
                }
            });
            const userResult = await dynamodb.send(getCommand);
            if (!userResult.Item) {
                return createResponse(404, { error: 'User not found' });
            }
            const user = userResult.Item;
            return createResponse(200, {
                message: 'Token is valid',
                valid: true,
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    firstName: user.firstName,
                    lastName: user.lastName
                }
            });
        }
        catch (cognitoError) {
            console.error('Token validation error:', cognitoError);
            return createResponse(401, {
                error: 'Invalid or expired token',
                valid: false
            });
        }
    }
    catch (error) {
        console.error('Validate token error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
// Main handler
const handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    const { httpMethod, path } = event;
    // Handle CORS preflight requests
    if (httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }
    try {
        // Parse the action from the request body for POST requests
        // or from query parameters for GET requests
        let action = '';
        if (httpMethod === 'POST') {
            const body = JSON.parse(event.body || '{}');
            action = body.action || '';
            console.log('POST request body:', body);
        }
        else if (httpMethod === 'GET') {
            action = event.queryStringParameters?.action || '';
            console.log('GET query parameters:', event.queryStringParameters);
        }
        console.log(`Processing: ${httpMethod} ${action}`);
        switch (`${httpMethod} ${action}`) {
            case 'POST signup':
                console.log('Calling signUp');
                return await signUp(event);
            case 'POST signin':
                console.log('Calling signIn');
                return await signIn(event);
            case 'POST refresh':
                console.log('Calling refreshToken');
                return await refreshToken(event);
            case 'GET validate':
                console.log('Calling validateToken');
                return await validateToken(event);
            default:
                console.log(`No matching case for: ${httpMethod} ${action}`);
                return createResponse(404, { error: 'Not found', message: `Unsupported action: ${httpMethod} ${action}` });
        }
    }
    catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
exports.handler = handler;
//# sourceMappingURL=data:application/json;base64,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