{"Resources": {"UserPool6BA7E5F2": {"Type": "AWS::Cognito::UserPool", "Properties": {"AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_phone_number", "Priority": 1}, {"Name": "verified_email", "Priority": 2}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": true}, "AutoVerifiedAttributes": ["email"], "DeletionProtection": "INACTIVE", "EmailVerificationMessage": "The verification code to your new account is {####}", "EmailVerificationSubject": "Verify your new account", "Policies": {"PasswordPolicy": {"MinimumLength": 8, "RequireLowercase": true, "RequireNumbers": true, "RequireSymbols": false, "RequireUppercase": true}}, "SmsVerificationMessage": "The verification code to your new account is {####}", "UserPoolName": "gameflex-users-development", "UsernameAttributes": ["email"], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailMessage": "The verification code to your new account is {####}", "EmailSubject": "Verify your new account", "SmsMessage": "The verification code to your new account is {####}"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/UserPool/Resource"}}, "UserPoolClient2F5918F7": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"AccessTokenValidity": 60, "AllowedOAuthFlows": ["implicit", "code"], "AllowedOAuthFlowsUserPoolClient": true, "AllowedOAuthScopes": ["profile", "phone", "email", "openid", "aws.cognito.signin.user.admin"], "CallbackURLs": ["https://example.com"], "ClientName": "gameflex-client-development", "ExplicitAuthFlows": ["ALLOW_USER_PASSWORD_AUTH", "ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"], "GenerateSecret": false, "IdTokenValidity": 60, "RefreshTokenValidity": 43200, "SupportedIdentityProviders": ["COGNITO"], "TokenValidityUnits": {"AccessToken": "minutes", "IdToken": "minutes", "RefreshToken": "minutes"}, "UserPoolId": {"Ref": "UserPool6BA7E5F2"}}, "Metadata": {"aws:cdk:path": "gameflex-development/UserPoolClient/Resource"}}, "PostsTableC82B36F0": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "id", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "DeletionProtectionEnabled": false, "KeySchema": [{"AttributeName": "id", "KeyType": "HASH"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": false}, "TableName": "gameflex-development-Posts"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/PostsTable/Resource"}}, "MediaTableCFC93525": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "id", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "DeletionProtectionEnabled": false, "KeySchema": [{"AttributeName": "id", "KeyType": "HASH"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": false}, "TableName": "gameflex-development-Media"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/MediaTable/Resource"}}, "UserProfilesTableF49D814C": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "userId", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "DeletionProtectionEnabled": false, "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": false}, "TableName": "gameflex-development-UserProfiles"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/UserProfilesTable/Resource"}}, "CommentsTableBBDBF0A8": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "id", "AttributeType": "S"}, {"AttributeName": "postId", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "DeletionProtectionEnabled": false, "GlobalSecondaryIndexes": [{"IndexName": "postId-index", "KeySchema": [{"AttributeName": "postId", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}], "KeySchema": [{"AttributeName": "id", "KeyType": "HASH"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": false}, "TableName": "gameflex-development-Comments"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/CommentsTable/Resource"}}, "LikesTable9511B0A4": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "postId", "AttributeType": "S"}, {"AttributeName": "userId", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "DeletionProtectionEnabled": false, "GlobalSecondaryIndexes": [{"IndexName": "userId-index", "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}], "KeySchema": [{"AttributeName": "postId", "KeyType": "HASH"}, {"AttributeName": "userId", "KeyType": "RANGE"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": false}, "TableName": "gameflex-development-Likes"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/LikesTable/Resource"}}, "FollowsTable7B81FE10": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "followerId", "AttributeType": "S"}, {"AttributeName": "followingId", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "DeletionProtectionEnabled": false, "KeySchema": [{"AttributeName": "followerId", "KeyType": "HASH"}, {"AttributeName": "followingId", "KeyType": "RANGE"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": false}, "TableName": "gameflex-development-Follows"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/FollowsTable/Resource"}}, "ChannelsTable6C883730": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "id", "AttributeType": "S"}, {"AttributeName": "ownerId", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "DeletionProtectionEnabled": false, "GlobalSecondaryIndexes": [{"IndexName": "ownerId-index", "KeySchema": [{"AttributeName": "ownerId", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}], "KeySchema": [{"AttributeName": "id", "KeyType": "HASH"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": false}, "TableName": "gameflex-development-Channels"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/ChannelsTable/Resource"}}, "ChannelMembersTable93E36D7C": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "channelId", "AttributeType": "S"}, {"AttributeName": "userId", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "DeletionProtectionEnabled": false, "GlobalSecondaryIndexes": [{"IndexName": "userId-index", "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}], "KeySchema": [{"AttributeName": "channelId", "KeyType": "HASH"}, {"AttributeName": "userId", "KeyType": "RANGE"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": false}, "TableName": "gameflex-development-ChannelMembers"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/ChannelMembersTable/Resource"}}, "ReflexesTableD80A0AD3": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "id", "AttributeType": "S"}, {"AttributeName": "postId", "AttributeType": "S"}, {"AttributeName": "userId", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "DeletionProtectionEnabled": false, "GlobalSecondaryIndexes": [{"IndexName": "postId-index", "KeySchema": [{"AttributeName": "postId", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}, {"IndexName": "userId-index", "KeySchema": [{"AttributeName": "userId", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}], "KeySchema": [{"AttributeName": "id", "KeyType": "HASH"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": false}, "TableName": "gameflex-development-Reflexes"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/ReflexesTable/Resource"}}, "UsersTable9725E9C8": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "id", "AttributeType": "S"}, {"AttributeName": "email", "AttributeType": "S"}, {"AttributeName": "username", "AttributeType": "S"}, {"AttributeName": "cognitoUserId", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "DeletionProtectionEnabled": false, "GlobalSecondaryIndexes": [{"IndexName": "EmailIndex", "KeySchema": [{"AttributeName": "email", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}, {"IndexName": "UsernameIndex", "KeySchema": [{"AttributeName": "username", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}, {"IndexName": "CognitoUserIdIndex", "KeySchema": [{"AttributeName": "cognitoUserId", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}}], "KeySchema": [{"AttributeName": "id", "KeyType": "HASH"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": false}, "TableName": "gameflex-development-Users"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "gameflex-development/UsersTable/Resource"}}, "AuthorizerFunctionServiceRole5B2A061B": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "gameflex-development/AuthorizerFunction/ServiceRole/Resource"}}, "AuthorizerFunctionServiceRoleDefaultPolicy2C75A4EA": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": "cognito-idp:Get<PERSON>ser", "Effect": "Allow", "Resource": {"Fn::GetAtt": ["UserPool6BA7E5F2", "<PERSON><PERSON>"]}}], "Version": "2012-10-17"}, "PolicyName": "AuthorizerFunctionServiceRoleDefaultPolicy2C75A4EA", "Roles": [{"Ref": "AuthorizerFunctionServiceRole5B2A061B"}]}, "Metadata": {"aws:cdk:path": "gameflex-development/AuthorizerFunction/ServiceRole/DefaultPolicy/Resource"}}, "AuthorizerFunctionB4DBAA43": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "S3Key": "dc160347e535143f47254e1e24af8f1807dc8d0933b88e37a426c013a5055e1d.zip"}, "Environment": {"Variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "FunctionName": "gameflex-authorizer-development", "Handler": "index.handler", "MemorySize": 256, "Role": {"Fn::GetAtt": ["AuthorizerFunctionServiceRole5B2A061B", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Timeout": 30}, "DependsOn": ["AuthorizerFunctionServiceRoleDefaultPolicy2C75A4EA", "AuthorizerFunctionServiceRole5B2A061B"], "Metadata": {"aws:cdk:path": "gameflex-development/AuthorizerFunction/Resource", "aws:asset:path": "asset.dc160347e535143f47254e1e24af8f1807dc8d0933b88e37a426c013a5055e1d", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "AuthorizerFunctionLogGroupB47801AB": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "AuthorizerFunctionB4DBAA43"}]]}, "RetentionInDays": 731}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "gameflex-development/AuthorizerFunction/LogGroup/Resource"}}, "AuthorizerFunctiongameflexdevelopmentDefaultAuthorizerCA84F39EPermissionsCB2412EE": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["AuthorizerFunctionB4DBAA43", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/authorizers/", {"Ref": "DefaultAuthorizerCA0170E0"}]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/AuthorizerFunction/gameflexdevelopmentDefaultAuthorizerCA84F39E:Permissions"}}, "AuthFunctionServiceRole87A7A68C": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "gameflex-development/AuthFunction/ServiceRole/Resource"}}, "AuthFunctionServiceRoleDefaultPolicy4198AD5B": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}]}, {"Action": "cognito-idp:*", "Effect": "Allow", "Resource": {"Fn::GetAtt": ["UserPool6BA7E5F2", "<PERSON><PERSON>"]}}], "Version": "2012-10-17"}, "PolicyName": "AuthFunctionServiceRoleDefaultPolicy4198AD5B", "Roles": [{"Ref": "AuthFunctionServiceRole87A7A68C"}]}, "Metadata": {"aws:cdk:path": "gameflex-development/AuthFunction/ServiceRole/DefaultPolicy/Resource"}}, "AuthFunctionA1CD5E0F": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "S3Key": "ae58682233a278e182bdf174d49db13bd82e7f600c0d6ba017bcf1fe7e15c67c.zip"}, "Environment": {"Variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "FunctionName": "gameflex-auth-development", "Handler": "index.handler", "MemorySize": 256, "Role": {"Fn::GetAtt": ["AuthFunctionServiceRole87A7A68C", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Timeout": 30}, "DependsOn": ["AuthFunctionServiceRoleDefaultPolicy4198AD5B", "AuthFunctionServiceRole87A7A68C"], "Metadata": {"aws:cdk:path": "gameflex-development/AuthFunction/Resource", "aws:asset:path": "asset.ae58682233a278e182bdf174d49db13bd82e7f600c0d6ba017bcf1fe7e15c67c", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "AuthFunctionLogGroupF2D1C9DD": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "AuthFunctionA1CD5E0F"}]]}, "RetentionInDays": 731}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "gameflex-development/AuthFunction/LogGroup/Resource"}}, "PostsFunctionServiceRoleF2DB8406": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "gameflex-development/PostsFunction/ServiceRole/Resource"}}, "PostsFunctionServiceRoleDefaultPolicyACA3B729": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["CommentsTableBBDBF0A8", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["LikesTable9511B0A4", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["MediaTableCFC93525", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["PostsTableC82B36F0", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["ReflexesTableD80A0AD3", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["CommentsTableBBDBF0A8", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["LikesTable9511B0A4", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ReflexesTableD80A0AD3", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Ref": "AWS::NoValue"}]}], "Version": "2012-10-17"}, "PolicyName": "PostsFunctionServiceRoleDefaultPolicyACA3B729", "Roles": [{"Ref": "PostsFunctionServiceRoleF2DB8406"}]}, "Metadata": {"aws:cdk:path": "gameflex-development/PostsFunction/ServiceRole/DefaultPolicy/Resource"}}, "PostsFunction3DC4AEDB": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "S3Key": "b868217bdf455fb562dd8a8ee6cd293e524b9fa4f8282824678df88bec1b6375.zip"}, "Environment": {"Variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "FunctionName": "gameflex-posts-development", "Handler": "index.handler", "MemorySize": 256, "Role": {"Fn::GetAtt": ["PostsFunctionServiceRoleF2DB8406", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Timeout": 30}, "DependsOn": ["PostsFunctionServiceRoleDefaultPolicyACA3B729", "PostsFunctionServiceRoleF2DB8406"], "Metadata": {"aws:cdk:path": "gameflex-development/PostsFunction/Resource", "aws:asset:path": "asset.b868217bdf455fb562dd8a8ee6cd293e524b9fa4f8282824678df88bec1b6375", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "PostsFunctionLogGroupC6FCA2A2": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "PostsFunction3DC4AEDB"}]]}, "RetentionInDays": 731}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "gameflex-development/PostsFunction/LogGroup/Resource"}}, "MediaFunctionServiceRole8E5A8C78": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "gameflex-development/MediaFunction/ServiceRole/Resource"}}, "MediaFunctionServiceRoleDefaultPolicy8C41214C": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["MediaTableCFC93525", "<PERSON><PERSON>"]}, {"Ref": "AWS::NoValue"}]}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": ["arn:aws:secretsmanager:us-west-2:405316604661:secret:gameflex-app-config-development-??????", "arn:aws:secretsmanager:us-west-2:405316604661:secret:gameflex-r2-config-development-??????"]}], "Version": "2012-10-17"}, "PolicyName": "MediaFunctionServiceRoleDefaultPolicy8C41214C", "Roles": [{"Ref": "MediaFunctionServiceRole8E5A8C78"}]}, "Metadata": {"aws:cdk:path": "gameflex-development/MediaFunction/ServiceRole/DefaultPolicy/Resource"}}, "MediaFunctionD33228E9": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "S3Key": "85ac8ea8f2e87310bb51e3b76b3c5e1818dab94587199dcccdf5bac79696dfc6.zip"}, "Environment": {"Variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "FunctionName": "gameflex-media-development", "Handler": "index.handler", "MemorySize": 256, "Role": {"Fn::GetAtt": ["MediaFunctionServiceRole8E5A8C78", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Timeout": 30}, "DependsOn": ["MediaFunctionServiceRoleDefaultPolicy8C41214C", "MediaFunctionServiceRole8E5A8C78"], "Metadata": {"aws:cdk:path": "gameflex-development/MediaFunction/Resource", "aws:asset:path": "asset.85ac8ea8f2e87310bb51e3b76b3c5e1818dab94587199dcccdf5bac79696dfc6", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "MediaFunctionLogGroup944AF0E8": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "MediaFunctionD33228E9"}]]}, "RetentionInDays": 731}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "gameflex-development/MediaFunction/LogGroup/Resource"}}, "UsersFunctionServiceRoleAA0FFEE7": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "gameflex-development/UsersFunction/ServiceRole/Resource"}}, "UsersFunctionServiceRoleDefaultPolicyDAA4211F": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["FollowsTable7B81FE10", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["LikesTable9511B0A4", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["PostsTableC82B36F0", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UserProfilesTableF49D814C", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["LikesTable9511B0A4", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Ref": "AWS::NoValue"}]}], "Version": "2012-10-17"}, "PolicyName": "UsersFunctionServiceRoleDefaultPolicyDAA4211F", "Roles": [{"Ref": "UsersFunctionServiceRoleAA0FFEE7"}]}, "Metadata": {"aws:cdk:path": "gameflex-development/UsersFunction/ServiceRole/DefaultPolicy/Resource"}}, "UsersFunction1976AE51": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "S3Key": "06b7d8ab0d4621a2b976de40da2d2d29f2fc5dcd2e5322c4b0eb5694c06c6845.zip"}, "Environment": {"Variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "FunctionName": "gameflex-users-development", "Handler": "index.handler", "MemorySize": 256, "Role": {"Fn::GetAtt": ["UsersFunctionServiceRoleAA0FFEE7", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Timeout": 30}, "DependsOn": ["UsersFunctionServiceRoleDefaultPolicyDAA4211F", "UsersFunctionServiceRoleAA0FFEE7"], "Metadata": {"aws:cdk:path": "gameflex-development/UsersFunction/Resource", "aws:asset:path": "asset.06b7d8ab0d4621a2b976de40da2d2d29f2fc5dcd2e5322c4b0eb5694c06c6845", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "UsersFunctionLogGroup08AD9B83": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "UsersFunction1976AE51"}]]}, "RetentionInDays": 731}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "gameflex-development/UsersFunction/LogGroup/Resource"}}, "HealthFunctionServiceRole04552894": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "gameflex-development/HealthFunction/ServiceRole/Resource"}}, "HealthFunctionServiceRoleDefaultPolicy57228674": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:ConditionCheckItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:Query", "dynamodb:<PERSON><PERSON>"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}]}], "Version": "2012-10-17"}, "PolicyName": "HealthFunctionServiceRoleDefaultPolicy57228674", "Roles": [{"Ref": "HealthFunctionServiceRole04552894"}]}, "Metadata": {"aws:cdk:path": "gameflex-development/HealthFunction/ServiceRole/DefaultPolicy/Resource"}}, "HealthFunction19D7724A": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "S3Key": "a436ae863fe1b0734db0e07398b70d1b8615769d3a580d0eb63569b74907d9f3.zip"}, "Environment": {"Variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "FunctionName": "gameflex-health-development", "Handler": "index.handler", "MemorySize": 256, "Role": {"Fn::GetAtt": ["HealthFunctionServiceRole04552894", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Timeout": 30}, "DependsOn": ["HealthFunctionServiceRoleDefaultPolicy57228674", "HealthFunctionServiceRole04552894"], "Metadata": {"aws:cdk:path": "gameflex-development/HealthFunction/Resource", "aws:asset:path": "asset.a436ae863fe1b0734db0e07398b70d1b8615769d3a580d0eb63569b74907d9f3", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "HealthFunctionLogGroup8350B222": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "HealthFunction19D7724A"}]]}, "RetentionInDays": 731}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "gameflex-development/HealthFunction/LogGroup/Resource"}}, "ReflexesFunctionServiceRole768786C8": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "gameflex-development/ReflexesFunction/ServiceRole/Resource"}}, "ReflexesFunctionServiceRoleDefaultPolicyEBCECFAA": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["MediaTableCFC93525", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["PostsTableC82B36F0", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["ReflexesTableD80A0AD3", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ReflexesTableD80A0AD3", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Ref": "AWS::NoValue"}]}], "Version": "2012-10-17"}, "PolicyName": "ReflexesFunctionServiceRoleDefaultPolicyEBCECFAA", "Roles": [{"Ref": "ReflexesFunctionServiceRole768786C8"}]}, "Metadata": {"aws:cdk:path": "gameflex-development/ReflexesFunction/ServiceRole/DefaultPolicy/Resource"}}, "ReflexesFunction5F91D3B2": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "S3Key": "f1e4164a456d2b28d440eb37f99aa8016eb94f92c9458380b112ff73b87df2cc.zip"}, "Environment": {"Variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "FunctionName": "gameflex-reflexes-development", "Handler": "index.handler", "MemorySize": 256, "Role": {"Fn::GetAtt": ["ReflexesFunctionServiceRole768786C8", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Timeout": 30}, "DependsOn": ["ReflexesFunctionServiceRoleDefaultPolicyEBCECFAA", "ReflexesFunctionServiceRole768786C8"], "Metadata": {"aws:cdk:path": "gameflex-development/ReflexesFunction/Resource", "aws:asset:path": "asset.f1e4164a456d2b28d440eb37f99aa8016eb94f92c9458380b112ff73b87df2cc", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "ReflexesFunctionLogGroupF5ABF65B": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "ReflexesFunction5F91D3B2"}]]}, "RetentionInDays": 731}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "gameflex-development/ReflexesFunction/LogGroup/Resource"}}, "ChannelsFunctionServiceRole0A782FB9": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "gameflex-development/ChannelsFunction/ServiceRole/Resource"}}, "ChannelsFunctionServiceRoleDefaultPolicy6E3FD81C": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ChannelMembersTable93E36D7C", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["ChannelsTable6C883730", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["MediaTableCFC93525", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["PostsTableC82B36F0", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ChannelMembersTable93E36D7C", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ChannelsTable6C883730", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["UsersTable9725E9C8", "<PERSON><PERSON>"]}, "/index/*"]]}, {"Ref": "AWS::NoValue"}]}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": ["arn:aws:secretsmanager:us-west-2:405316604661:secret:gameflex-app-config-development-??????", "arn:aws:secretsmanager:us-west-2:405316604661:secret:gameflex-r2-config-development-??????"]}], "Version": "2012-10-17"}, "PolicyName": "ChannelsFunctionServiceRoleDefaultPolicy6E3FD81C", "Roles": [{"Ref": "ChannelsFunctionServiceRole0A782FB9"}]}, "Metadata": {"aws:cdk:path": "gameflex-development/ChannelsFunction/ServiceRole/DefaultPolicy/Resource"}}, "ChannelsFunction9B818B1B": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-405316604661-us-west-2", "S3Key": "3755b8241ac0ed1969259e60e80337e004fe247c518848c42e22ee0527cb9e9b.zip"}, "Environment": {"Variables": {"ENVIRONMENT": "development", "PROJECT_NAME": "gameflex", "USER_POOL_ID": {"Ref": "UserPool6BA7E5F2"}, "USER_POOL_CLIENT_ID": {"Ref": "UserPoolClient2F5918F7"}, "POSTS_TABLE": {"Ref": "PostsTableC82B36F0"}, "MEDIA_TABLE": {"Ref": "MediaTableCFC93525"}, "USER_PROFILES_TABLE": {"Ref": "UserProfilesTableF49D814C"}, "COMMENTS_TABLE": {"Ref": "CommentsTableBBDBF0A8"}, "LIKES_TABLE": {"Ref": "LikesTable9511B0A4"}, "FOLLOWS_TABLE": {"Ref": "FollowsTable7B81FE10"}, "CHANNELS_TABLE": {"Ref": "ChannelsTable6C883730"}, "CHANNEL_MEMBERS_TABLE": {"Ref": "ChannelMembersTable93E36D7C"}, "REFLEXES_TABLE": {"Ref": "ReflexesTableD80A0AD3"}, "USERS_TABLE": {"Ref": "UsersTable9725E9C8"}, "R2_SECRET_NAME": "gameflex-r2-config-development", "APP_CONFIG_SECRET_NAME": "gameflex-app-config-development"}}, "FunctionName": "gameflex-channels-development", "Handler": "index.handler", "MemorySize": 256, "Role": {"Fn::GetAtt": ["ChannelsFunctionServiceRole0A782FB9", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Timeout": 30}, "DependsOn": ["ChannelsFunctionServiceRoleDefaultPolicy6E3FD81C", "ChannelsFunctionServiceRole0A782FB9"], "Metadata": {"aws:cdk:path": "gameflex-development/ChannelsFunction/Resource", "aws:asset:path": "asset.3755b8241ac0ed1969259e60e80337e004fe247c518848c42e22ee0527cb9e9b", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "ChannelsFunctionLogGroupE884AE8C": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "ChannelsFunction9B818B1B"}]]}, "RetentionInDays": 731}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "gameflex-development/ChannelsFunction/LogGroup/Resource"}}, "GameFlexApiC965E6C8": {"Type": "AWS::ApiGateway::RestApi", "Properties": {"Description": "GameFlex API for development environment", "Name": "gameflex-api-development"}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Resource"}}, "GameFlexApiDeployment8960269Dd23fb76cc0ff6720e32e2ac231ca298a": {"Type": "AWS::ApiGateway::Deployment", "Properties": {"Description": "GameFlex API for development environment", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "DependsOn": ["DefaultAuthorizerCA0170E0", "GameFlexApiauthGET38F7822D", "GameFlexApiauthOPTIONSA390FDA7", "GameFlexApiauthPOSTB8EDAEDC", "GameFlexApiauth34DB0BF1", "GameFlexApichannelsidDELETEB3C13444", "GameFlexApichannelsidGETCBD3255E", "GameFlexApichannelsidOPTIONS29E6B8F8", "GameFlexApichannelsidPUT5A9E3CDD", "GameFlexApichannelsidA7085A68", "GameFlexApichannelsGET659DD05A", "GameFlexApichannelsOPTIONS6D8DAFD4", "GameFlexApichannelsPOSTCF16E736", "GameFlexApichannels7A12BEA5", "GameFlexApihealthGET79EDAB02", "GameFlexApihealthOPTIONS019C8FE9", "GameFlexApihealth412F2DFA", "GameFlexApimediaidDELETEF9AAD476", "GameFlexApimediaidGET53E65C4D", "GameFlexApimediaidOPTIONS15C420E0", "GameFlexApimediaidPUTD672B82A", "GameFlexApimediaidB68B38E9", "GameFlexApimediaGETF88B1162", "GameFlexApimediaOPTIONS888063DA", "GameFlexApimediaPOST23821932", "GameFlexApimedia4CFF2055", "GameFlexApimediauploadOPTIONS3D8C100F", "GameFlexApimediauploadPOST48EB5DC0", "GameFlexApimediaupload246052B2", "GameFlexApiOPTIONS68CD382C", "GameFlexApipostsidcommentsGETF1DF9BDB", "GameFlexApipostsidcommentsOPTIONS863B9C13", "GameFlexApipostsidcommentsPOST45548CB9", "GameFlexApipostsidcomments3BE1F05E", "GameFlexApipostsidDELETE3A55CAA5", "GameFlexApipostsidGETDBDEB4C6", "GameFlexApipostsidlikeDELETE1046505C", "GameFlexApipostsidlikeOPTIONS4C5C766C", "GameFlexApipostsidlikePOSTF39DD595", "GameFlexApipostsidlikeCDEA7366", "GameFlexApipostsidmediaOPTIONS73D78B81", "GameFlexApipostsidmediaPUT1A3D52BF", "GameFlexApipostsidmediaA8DE1E9D", "GameFlexApipostsidOPTIONSD440FEBE", "GameFlexApipostsidpublishOPTIONSD6B22911", "GameFlexApipostsidpublishPUTD94AEA0F", "GameFlexApipostsidpublish67AF9D0C", "GameFlexApipostsidPUT90659B0E", "GameFlexApipostsidEA3F073A", "GameFlexApipostsdraftOPTIONSCC49E433", "GameFlexApipostsdraftPOST3EC59156", "GameFlexApipostsdraftAAEA2551", "GameFlexApipostsGET0F7C7972", "GameFlexApipostsOPTIONSF0778EA0", "GameFlexApipostsPOST6CAE87D1", "GameFlexApiposts137B57F5", "GameFlexApireflexesidDELETEFDB13D0A", "GameFlexApireflexesidGETE144C995", "GameFlexApireflexesidOPTIONS9DD475C6", "GameFlexApireflexesidPUT3264D76B", "GameFlexApireflexesidFB646CA8", "GameFlexApireflexesGET98D253A6", "GameFlexApireflexesOPTIONS651473FA", "GameFlexApireflexesPOST49B0BCC9", "GameFlexApireflexesF9B892EC", "GameFlexApiusersidGET5EDFC202", "GameFlexApiusersidOPTIONSFAA6CD24", "GameFlexApiusersidPUT6BC0DEBC", "GameFlexApiusersidDF782D5C", "GameFlexApiusersGETD39706DD", "GameFlexApiusersOPTIONS91C48C12", "GameFlexApiusersPOST5E8DF6A8", "GameFlexApiusersprofileGETC63B83E9", "GameFlexApiusersprofilelikedpostsGETC07CC6B9", "GameFlexApiusersprofilelikedpostsOPTIONS91344DD8", "GameFlexApiusersprofilelikedposts922C1EB7", "GameFlexApiusersprofileOPTIONS3FBFBB96", "GameFlexApiusersprofilePUTB1C58802", "GameFlexApiusersprofileE72A3ECA", "GameFlexApiusers539BBD8B"], "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Deployment/Resource", "aws:cdk:do-not-refactor": true}}, "GameFlexApiDeploymentStagev1A494DD80": {"Type": "AWS::ApiGateway::Stage", "Properties": {"DeploymentId": {"Ref": "GameFlexApiDeployment8960269Dd23fb76cc0ff6720e32e2ac231ca298a"}, "MethodSettings": [{"DataTraceEnabled": false, "HttpMethod": "*", "ResourcePath": "/*", "ThrottlingBurstLimit": 2000, "ThrottlingRateLimit": 1000}], "RestApiId": {"Ref": "GameFlexApiC965E6C8"}, "StageName": "v1"}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/DeploymentStage.v1/Resource"}}, "GameFlexApiOPTIONS68CD382C": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/OPTIONS/Resource"}}, "GameFlexApihealth412F2DFA": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "PathPart": "health", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/health/Resource"}}, "GameFlexApihealthOPTIONS019C8FE9": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApihealth412F2DFA"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/health/OPTIONS/Resource"}}, "GameFlexApihealthGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GEThealth272E2BBE": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["HealthFunction19D7724A", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/health"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/health/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..health"}}, "GameFlexApihealthGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GEThealth0C4B10BB": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["HealthFunction19D7724A", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/health"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/health/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..health"}}, "GameFlexApihealthGET79EDAB02": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["HealthFunction19D7724A", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApihealth412F2DFA"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/health/GET/Resource"}}, "GameFlexApiauth34DB0BF1": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "PathPart": "auth", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/auth/Resource"}}, "GameFlexApiauthOPTIONSA390FDA7": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApiauth34DB0BF1"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/auth/OPTIONS/Resource"}}, "GameFlexApiauthPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTauth1F61597A": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/auth"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/auth/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..auth"}}, "GameFlexApiauthPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTauth4AFBD867": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/auth"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/auth/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..auth"}}, "GameFlexApiauthPOSTB8EDAEDC": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiauth34DB0BF1"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/auth/POST/Resource"}}, "GameFlexApiauthGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETauth8265FB7D": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/auth"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/auth/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..auth"}}, "GameFlexApiauthGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETauthB8C104F6": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/auth"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/auth/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..auth"}}, "GameFlexApiauthGET38F7822D": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["AuthFunctionA1CD5E0F", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiauth34DB0BF1"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/auth/GET/Resource"}}, "GameFlexApiposts137B57F5": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "PathPart": "posts", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/Resource"}}, "GameFlexApipostsOPTIONSF0778EA0": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApiposts137B57F5"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/OPTIONS/Resource"}}, "GameFlexApipostsGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETposts3D9E48D5": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/posts"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts"}}, "GameFlexApipostsGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETpostsB09E6BBA": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/posts"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts"}}, "GameFlexApipostsGET0F7C7972": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiposts137B57F5"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/GET/Resource"}}, "GameFlexApipostsPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTposts616FF206": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/posts"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts"}}, "GameFlexApipostsPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTposts25767A0A": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/posts"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts"}}, "GameFlexApipostsPOST6CAE87D1": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiposts137B57F5"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/POST/Resource"}}, "GameFlexApipostsdraftAAEA2551": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApiposts137B57F5"}, "PathPart": "draft", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/draft/Resource"}}, "GameFlexApipostsdraftOPTIONSCC49E433": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApipostsdraftAAEA2551"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/draft/OPTIONS/Resource"}}, "GameFlexApipostsdraftPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTpostsdraft141EE64F": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/posts/draft"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/draft/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.draft"}}, "GameFlexApipostsdraftPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTpostsdraftF65F1D73": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/posts/draft"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/draft/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.draft"}}, "GameFlexApipostsdraftPOST3EC59156": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApipostsdraftAAEA2551"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/draft/POST/Resource"}}, "GameFlexApipostsidEA3F073A": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApiposts137B57F5"}, "PathPart": "{id}", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/Resource"}}, "GameFlexApipostsidOPTIONSD440FEBE": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApipostsidEA3F073A"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/OPTIONS/Resource"}}, "GameFlexApipostsidGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETpostsid1228102E": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/posts/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}"}}, "GameFlexApipostsidGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETpostsidF96746EB": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/posts/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}"}}, "GameFlexApipostsidGETDBDEB4C6": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApipostsidEA3F073A"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/GET/Resource"}}, "GameFlexApipostsidPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTpostsidA215B381": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/posts/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}"}}, "GameFlexApipostsidPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTpostsid810758A2": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/posts/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}"}}, "GameFlexApipostsidPUT90659B0E": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "PUT", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApipostsidEA3F073A"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/PUT/Resource"}}, "GameFlexApipostsidDELETEApiPermissiongameflexdevelopmentGameFlexApi1C9066F4DELETEpostsidACA73A1C": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/DELETE/posts/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}"}}, "GameFlexApipostsidDELETEApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4DELETEpostsid512347D2": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/DELETE/posts/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}"}}, "GameFlexApipostsidDELETE3A55CAA5": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "DELETE", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApipostsidEA3F073A"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/DELETE/Resource"}}, "GameFlexApipostsidmediaA8DE1E9D": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApipostsidEA3F073A"}, "PathPart": "media", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/Resource"}}, "GameFlexApipostsidmediaOPTIONS73D78B81": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApipostsidmediaA8DE1E9D"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/OPTIONS/Resource"}}, "GameFlexApipostsidmediaPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTpostsidmediaD261D149": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/posts/*/media"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.media"}}, "GameFlexApipostsidmediaPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTpostsidmediaED4A6CBB": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/posts/*/media"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.media"}}, "GameFlexApipostsidmediaPUT1A3D52BF": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "PUT", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApipostsidmediaA8DE1E9D"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/media/PUT/Resource"}}, "GameFlexApipostsidpublish67AF9D0C": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApipostsidEA3F073A"}, "PathPart": "publish", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/Resource"}}, "GameFlexApipostsidpublishOPTIONSD6B22911": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApipostsidpublish67AF9D0C"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/OPTIONS/Resource"}}, "GameFlexApipostsidpublishPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTpostsidpublishF74F2BCF": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/posts/*/publish"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.publish"}}, "GameFlexApipostsidpublishPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTpostsidpublish13867156": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/posts/*/publish"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..posts.{id}.publish"}}, "GameFlexApipostsidpublishPUTD94AEA0F": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "PUT", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApipostsidpublish67AF9D0C"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/publish/PUT/Resource"}}, "GameFlexApipostsidlikeCDEA7366": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApipostsidEA3F073A"}, "PathPart": "like", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/Resource"}}, "GameFlexApipostsidlikeOPTIONS4C5C766C": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApipostsidlikeCDEA7366"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/OPTIONS/Resource"}}, "GameFlexApipostsidlikePOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTpostsidlike5BFFC18C": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/posts/*/like"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.like"}}, "GameFlexApipostsidlikePOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTpostsidlike17A6FFA2": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/posts/*/like"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.like"}}, "GameFlexApipostsidlikePOSTF39DD595": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApipostsidlikeCDEA7366"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/POST/Resource"}}, "GameFlexApipostsidlikeDELETEApiPermissiongameflexdevelopmentGameFlexApi1C9066F4DELETEpostsidlike87453760": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/DELETE/posts/*/like"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}.like"}}, "GameFlexApipostsidlikeDELETEApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4DELETEpostsidlikeD1BA6E14": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/DELETE/posts/*/like"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..posts.{id}.like"}}, "GameFlexApipostsidlikeDELETE1046505C": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "DELETE", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApipostsidlikeCDEA7366"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/like/DELETE/Resource"}}, "GameFlexApipostsidcomments3BE1F05E": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApipostsidEA3F073A"}, "PathPart": "comments", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/Resource"}}, "GameFlexApipostsidcommentsOPTIONS863B9C13": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApipostsidcomments3BE1F05E"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/OPTIONS/Resource"}}, "GameFlexApipostsidcommentsGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETpostsidcommentsB568A75F": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/posts/*/comments"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}.comments"}}, "GameFlexApipostsidcommentsGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETpostsidcomments8820FCE4": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/posts/*/comments"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..posts.{id}.comments"}}, "GameFlexApipostsidcommentsGETF1DF9BDB": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApipostsidcomments3BE1F05E"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/GET/Resource"}}, "GameFlexApipostsidcommentsPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTpostsidcomments5D5E5838": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/posts/*/comments"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.comments"}}, "GameFlexApipostsidcommentsPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTpostsidcommentsC2738A2B": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/posts/*/comments"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..posts.{id}.comments"}}, "GameFlexApipostsidcommentsPOST45548CB9": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["PostsFunction3DC4AEDB", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApipostsidcomments3BE1F05E"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/posts/{id}/comments/POST/Resource"}}, "GameFlexApiusers539BBD8B": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "PathPart": "users", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/Resource"}}, "GameFlexApiusersOPTIONS91C48C12": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApiusers539BBD8B"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/OPTIONS/Resource"}}, "GameFlexApiusersGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETusersCA21EEFD": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/users"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users"}}, "GameFlexApiusersGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETusers4D263189": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/users"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users"}}, "GameFlexApiusersGETD39706DD": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiusers539BBD8B"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/GET/Resource"}}, "GameFlexApiusersPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTusersC1E14F4E": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/users"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..users"}}, "GameFlexApiusersPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTusers52B047F5": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/users"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..users"}}, "GameFlexApiusersPOST5E8DF6A8": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiusers539BBD8B"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/POST/Resource"}}, "GameFlexApiusersidDF782D5C": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApiusers539BBD8B"}, "PathPart": "{id}", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/{id}/Resource"}}, "GameFlexApiusersidOPTIONSFAA6CD24": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApiusersidDF782D5C"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/{id}/OPTIONS/Resource"}}, "GameFlexApiusersidGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETusersidEC8EE65D": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/users/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.{id}"}}, "GameFlexApiusersidGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETusersidD49C646A": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/users/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.{id}"}}, "GameFlexApiusersidGET5EDFC202": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiusersidDF782D5C"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/{id}/GET/Resource"}}, "GameFlexApiusersidPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTusersidD9AD0EA0": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/users/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.{id}"}}, "GameFlexApiusersidPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTusersid4CD0E122": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/users/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.{id}"}}, "GameFlexApiusersidPUT6BC0DEBC": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "PUT", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiusersidDF782D5C"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/{id}/PUT/Resource"}}, "GameFlexApiusersprofileE72A3ECA": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApiusers539BBD8B"}, "PathPart": "profile", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/Resource"}}, "GameFlexApiusersprofileOPTIONS3FBFBB96": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApiusersprofileE72A3ECA"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/OPTIONS/Resource"}}, "GameFlexApiusersprofileGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETusersprofileBD9A6FF8": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/users/profile"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile"}}, "GameFlexApiusersprofileGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETusersprofile18F88C76": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/users/profile"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile"}}, "GameFlexApiusersprofileGETC63B83E9": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiusersprofileE72A3ECA"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/GET/Resource"}}, "GameFlexApiusersprofilePUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTusersprofileC7CD10E2": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/users/profile"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.profile"}}, "GameFlexApiusersprofilePUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTusersprofileF01F543C": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/users/profile"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..users.profile"}}, "GameFlexApiusersprofilePUTB1C58802": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "PUT", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiusersprofileE72A3ECA"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/PUT/Resource"}}, "GameFlexApiusersprofilelikedposts922C1EB7": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApiusersprofileE72A3ECA"}, "PathPart": "liked-posts", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/Resource"}}, "GameFlexApiusersprofilelikedpostsOPTIONS91344DD8": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApiusersprofilelikedposts922C1EB7"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/OPTIONS/Resource"}}, "GameFlexApiusersprofilelikedpostsGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETusersprofilelikedpostsC6301664": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/users/profile/liked-posts"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile.liked-posts"}}, "GameFlexApiusersprofilelikedpostsGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETusersprofilelikedposts985C2BCA": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/users/profile/liked-posts"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..users.profile.liked-posts"}}, "GameFlexApiusersprofilelikedpostsGETC07CC6B9": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["UsersFunction1976AE51", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApiusersprofilelikedposts922C1EB7"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/users/profile/liked-posts/GET/Resource"}}, "GameFlexApimedia4CFF2055": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "PathPart": "media", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/Resource"}}, "GameFlexApimediaOPTIONS888063DA": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApimedia4CFF2055"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/OPTIONS/Resource"}}, "GameFlexApimediaGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETmedia851AB196": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/media"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..media"}}, "GameFlexApimediaGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETmedia9C36643A": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/media"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..media"}}, "GameFlexApimediaGETF88B1162": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApimedia4CFF2055"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/GET/Resource"}}, "GameFlexApimediaPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTmediaFA50121E": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/media"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..media"}}, "GameFlexApimediaPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTmediaE6B58FC3": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/media"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..media"}}, "GameFlexApimediaPOST23821932": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApimedia4CFF2055"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/POST/Resource"}}, "GameFlexApimediaupload246052B2": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApimedia4CFF2055"}, "PathPart": "upload", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/upload/Resource"}}, "GameFlexApimediauploadOPTIONS3D8C100F": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApimediaupload246052B2"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/upload/OPTIONS/Resource"}}, "GameFlexApimediauploadPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTmediauploadFE07429B": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/media/upload"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/upload/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..media.upload"}}, "GameFlexApimediauploadPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTmediaupload2A33C7CB": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/media/upload"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/upload/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..media.upload"}}, "GameFlexApimediauploadPOST48EB5DC0": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApimediaupload246052B2"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/upload/POST/Resource"}}, "GameFlexApimediaidB68B38E9": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApimedia4CFF2055"}, "PathPart": "{id}", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/Resource"}}, "GameFlexApimediaidOPTIONS15C420E0": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApimediaidB68B38E9"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/OPTIONS/Resource"}}, "GameFlexApimediaidGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETmediaid338CA734": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/media/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..media.{id}"}}, "GameFlexApimediaidGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETmediaid0AFE803B": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/media/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..media.{id}"}}, "GameFlexApimediaidGET53E65C4D": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApimediaidB68B38E9"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/GET/Resource"}}, "GameFlexApimediaidPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTmediaid6309CDA4": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/media/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..media.{id}"}}, "GameFlexApimediaidPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTmediaid276B28A2": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/media/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..media.{id}"}}, "GameFlexApimediaidPUTD672B82A": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "PUT", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApimediaidB68B38E9"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/PUT/Resource"}}, "GameFlexApimediaidDELETEApiPermissiongameflexdevelopmentGameFlexApi1C9066F4DELETEmediaidADA8C4BB": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/DELETE/media/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..media.{id}"}}, "GameFlexApimediaidDELETEApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4DELETEmediaid54761C46": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/DELETE/media/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..media.{id}"}}, "GameFlexApimediaidDELETEF9AAD476": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "DELETE", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["MediaFunctionD33228E9", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApimediaidB68B38E9"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/media/{id}/DELETE/Resource"}}, "GameFlexApireflexesF9B892EC": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "PathPart": "reflexes", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/Resource"}}, "GameFlexApireflexesOPTIONS651473FA": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApireflexesF9B892EC"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/OPTIONS/Resource"}}, "GameFlexApireflexesGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETreflexesD3B84664": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/reflexes"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes"}}, "GameFlexApireflexesGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETreflexes75399A82": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/reflexes"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes"}}, "GameFlexApireflexesGET98D253A6": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApireflexesF9B892EC"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/GET/Resource"}}, "GameFlexApireflexesPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTreflexes6C7E14B2": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/reflexes"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..reflexes"}}, "GameFlexApireflexesPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTreflexesE9A2DF18": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/reflexes"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..reflexes"}}, "GameFlexApireflexesPOST49B0BCC9": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApireflexesF9B892EC"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/POST/Resource"}}, "GameFlexApireflexesidFB646CA8": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApireflexesF9B892EC"}, "PathPart": "{id}", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/Resource"}}, "GameFlexApireflexesidOPTIONS9DD475C6": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApireflexesidFB646CA8"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/OPTIONS/Resource"}}, "GameFlexApireflexesidGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETreflexesidC72268A4": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/reflexes/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes.{id}"}}, "GameFlexApireflexesidGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETreflexesid19262906": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/reflexes/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..reflexes.{id}"}}, "GameFlexApireflexesidGETE144C995": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApireflexesidFB646CA8"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/GET/Resource"}}, "GameFlexApireflexesidPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTreflexesid4DE788CC": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/reflexes/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..reflexes.{id}"}}, "GameFlexApireflexesidPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTreflexesid67A173B7": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/reflexes/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..reflexes.{id}"}}, "GameFlexApireflexesidPUT3264D76B": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "PUT", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApireflexesidFB646CA8"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/PUT/Resource"}}, "GameFlexApireflexesidDELETEApiPermissiongameflexdevelopmentGameFlexApi1C9066F4DELETEreflexesidF8D33183": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/DELETE/reflexes/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..reflexes.{id}"}}, "GameFlexApireflexesidDELETEApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4DELETEreflexesid7351C9EC": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/DELETE/reflexes/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..reflexes.{id}"}}, "GameFlexApireflexesidDELETEFDB13D0A": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "DELETE", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ReflexesFunction5F91D3B2", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApireflexesidFB646CA8"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/reflexes/{id}/DELETE/Resource"}}, "GameFlexApichannels7A12BEA5": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["GameFlexApiC965E6C8", "RootResourceId"]}, "PathPart": "channels", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/Resource"}}, "GameFlexApichannelsOPTIONS6D8DAFD4": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApichannels7A12BEA5"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/OPTIONS/Resource"}}, "GameFlexApichannelsGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETchannelsC9BB72A4": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/channels"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels"}}, "GameFlexApichannelsGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETchannels48E71BB4": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/channels"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels"}}, "GameFlexApichannelsGET659DD05A": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApichannels7A12BEA5"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/GET/Resource"}}, "GameFlexApichannelsPOSTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4POSTchannelsDC7248FA": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/POST/channels"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/POST/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.POST..channels"}}, "GameFlexApichannelsPOSTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4POSTchannelsB7C82A82": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/POST/channels"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/POST/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.POST..channels"}}, "GameFlexApichannelsPOSTCF16E736": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApichannels7A12BEA5"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/POST/Resource"}}, "GameFlexApichannelsidA7085A68": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Ref": "GameFlexApichannels7A12BEA5"}, "PathPart": "{id}", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/Resource"}}, "GameFlexApichannelsidOPTIONS29E6B8F8": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "GameFlexApichannelsidA7085A68"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/OPTIONS/Resource"}}, "GameFlexApichannelsidGETApiPermissiongameflexdevelopmentGameFlexApi1C9066F4GETchannelsid41711DF8": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/GET/channels/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/GET/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels.{id}"}}, "GameFlexApichannelsidGETApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4GETchannelsidA9D411FD": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/GET/channels/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/GET/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.GET..channels.{id}"}}, "GameFlexApichannelsidGETCBD3255E": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "GET", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApichannelsidA7085A68"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/GET/Resource"}}, "GameFlexApichannelsidPUTApiPermissiongameflexdevelopmentGameFlexApi1C9066F4PUTchannelsidC0518577": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/PUT/channels/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/PUT/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.PUT..channels.{id}"}}, "GameFlexApichannelsidPUTApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4PUTchannelsid0EFA3962": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/PUT/channels/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/PUT/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.PUT..channels.{id}"}}, "GameFlexApichannelsidPUT5A9E3CDD": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "PUT", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApichannelsidA7085A68"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/PUT/Resource"}}, "GameFlexApichannelsidDELETEApiPermissiongameflexdevelopmentGameFlexApi1C9066F4DELETEchannelsidB3E67663": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/DELETE/channels/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/DELETE/ApiPermission.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..channels.{id}"}}, "GameFlexApichannelsidDELETEApiPermissionTestgameflexdevelopmentGameFlexApi1C9066F4DELETEchannelsidBD961F2E": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-west-2:405316604661:", {"Ref": "GameFlexApiC965E6C8"}, "/test-invoke-stage/DELETE/channels/*"]]}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/DELETE/ApiPermission.Test.gameflexdevelopmentGameFlexApi1C9066F4.DELETE..channels.{id}"}}, "GameFlexApichannelsidDELETEB3C13444": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "CUSTOM", "AuthorizerId": {"Ref": "DefaultAuthorizerCA0170E0"}, "HttpMethod": "DELETE", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["ChannelsFunction9B818B1B", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "GameFlexApichannelsidA7085A68"}, "RestApiId": {"Ref": "GameFlexApiC965E6C8"}}, "Metadata": {"aws:cdk:path": "gameflex-development/GameFlexApi/Default/channels/{id}/DELETE/Resource"}}, "DefaultAuthorizerCA0170E0": {"Type": "AWS::ApiGateway::Authorizer", "Properties": {"AuthorizerResultTtlInSeconds": 300, "AuthorizerUri": {"Fn::Join": ["", ["arn:", {"Fn::Select": [1, {"Fn::Split": [":", {"Fn::GetAtt": ["AuthorizerFunctionB4DBAA43", "<PERSON><PERSON>"]}]}]}, ":apigateway:", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["AuthorizerFunctionB4DBAA43", "<PERSON><PERSON>"]}]}]}, ":lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["AuthorizerFunctionB4DBAA43", "<PERSON><PERSON>"]}, "/invocations"]]}, "IdentitySource": "method.request.header.Authorization", "Name": "DefaultAuthorizer", "RestApiId": {"Ref": "GameFlexApiC965E6C8"}, "Type": "TOKEN"}, "Metadata": {"aws:cdk:path": "gameflex-development/DefaultAuthorizer/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/+1cW2/bNhT+LdXjoGhtBmxA3pw07Ywmi2c7BYbACGjxWGZDkRpJxVMN/feR1MVy3aZIls6XnAfD1PdRhzzfOZJI6nIcHb/+NXr9iiz0UUzvjjibRsuRIfFdOAQtcxXDTfBTED7DbxLaRm6XsUwEMzJaXmtQAyn5zTLI6+IfJIXgxNcnuZEfQbFZEZwsA0gJ48GJUTmUYaBZIvqixxnRoDfpjGi9kIoOJGex3z1l4gJEYua1cQV/50zBdZaBiq2RatcWv5CLr+JvWcKM/gIcFelUcovOCNeueQocDJNioKSB2JVqzu2SynvCm47ZqtooWQTlJDybiUaQsCmccQbCdPSpu5+v8R3REhCgiIERxApM26zVcv6Oy4XXilCrhm+hlqnxJ/8GNlJZK62CmQI9H8s7EB8JZ5SZoglYHIPWX2MY3UTXPa4cKUNaCJJKalNwTKYcrOdTxjkTyaWkzsdB76/bwfnwdnj+5/X5aBw8KHYmmTB9MWYpDCGW96CK7wYiDIxruCNpRpRhzu4H8KkkVpQpMlccBWUZYk9fdk8Jpe+5nBJuDz0pKFFFX1D4JzixOcxc6dFGJ/sjgD0jS2VQov8s0X74gsHEfN9ijqCue6XrjzDrB49+iFiGnKRTSqLlu1z4qFizs7rYsaxyYVi7NSeCclD1VlyF3Ldi68jc1FupjZ0qRuxzQ4O4Z0qK1A5Wq1Fs5d95F26mSxMk95LE5EESkwdJTB4k94jE5EESkwdJTB4k94jE5EHyaaRfA2oWfVx5ACplWtutMmQkjZZD6W8hEq3zFOipX1HKFBMxywjvxbHMRZM+VR23Q69e2vMpE6REkASqO8nM3Wu29rpY0VN13aa3YzlommjW/G6WNWsMied9wZmAlmsdfZA8ABcetnvwDm6LfeHCvnD3X7h0B+ACxncHD42nsn7M4uQMK9iPZhonx9IxG75vwpSODDHgBkS6afbZDG3J/o+Et9T3vZBsbxLwALV/FOxPHdVZowz1L7f21AxGRz33Z7ej0zy+A3NKNNxMQi4THS0vZPJeyTyzJ2deF9tptmtm10DvYtPpMiQZS6wIC1LYqRxo08uY9URVpc56AQUdK5a11yeHzEjOzZlUeqBgxlkyN1e+QvUELOdycaVYwtx2daFz0CWYuaRr0O9AKCjdXPcoZFwWHVPa2Gtepy9mrqQx7jmSoe36BUuZ2WBOc6XNiirrq0LlV/jWN+Eib53ddM2K0iyTgCFMrKrrVll/AbI6spjwPv3iwoQ44ogjjjjiiCOOOOLbxP3gdzWKDUduRO2Hvg1Uj3efY6jtG/MttG83+hmTmwKG1fC/mmJ4qrkDaUxWcTXAhIFEkc6oXHYmF7mtqdhnd8uyu9Vv9m4gv/+4eqrZTy7s0P4DFMPqrUK69vLeZn13x9N1ybqR2ZbrNTirkcn12eomqarpAVFWOdNOZSZlHZDLNSuPNRKiWP+XWPXcDuXeF7kxYFs8mfh1lZ02iOFE9V+q+rtvEPMD1Uf1UX08lWE4d8sghhPDieHcVYMYTgwnhhPVPwD1MZwHFU5U/6AOJswPzA8M566qv/sGMT8wnBjOnTboH1aq9l99i33pvpG4eigqI2ZuxVp94gBZZJ/ANq+g+CQL/efae20+Vo/H2QoryKa7f/PIPUvHROLIq9xkuSlDYQ/n6JP++f74OHrzW/Tm1SfN2FH9UY9oWP3/Cz8aMPt3YAAA"}, "Metadata": {"aws:cdk:path": "gameflex-development/CDKMetadata/Default"}}}, "Outputs": {"GameFlexApiEndpoint1F3A0AB8": {"Value": {"Fn::Join": ["", ["https://", {"Ref": "GameFlexApiC965E6C8"}, ".execute-api.us-west-2.", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/"]]}}, "ApiGatewayUrl": {"Description": "API Gateway URL", "Value": {"Fn::Join": ["", ["https://", {"Ref": "GameFlexApiC965E6C8"}, ".execute-api.us-west-2.", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "GameFlexApiDeploymentStagev1A494DD80"}, "/"]]}, "Export": {"Name": "gameflex-development-ApiGatewayUrl"}}, "UserPoolId": {"Description": "Cognito User Pool ID", "Value": {"Ref": "UserPool6BA7E5F2"}, "Export": {"Name": "gameflex-development-UserPoolId"}}, "UserPoolClientId": {"Description": "Cognito User Pool Client ID", "Value": {"Ref": "UserPoolClient2F5918F7"}, "Export": {"Name": "gameflex-development-UserPoolClientId"}}, "R2SecretArn": {"Description": "R2 Configuration Secret ARN", "Value": "arn:aws:secretsmanager:us-west-2:405316604661:secret:gameflex-r2-config-development", "Export": {"Name": "gameflex-development-R2SecretArn"}}, "AppConfigSecretArn": {"Description": "App Configuration Secret ARN", "Value": "arn:aws:secretsmanager:us-west-2:405316604661:secret:gameflex-app-config-development", "Export": {"Name": "gameflex-development-AppConfigSecretArn"}}, "PostsTableName": {"Description": "posts DynamoDB Table Name", "Value": {"Ref": "PostsTableC82B36F0"}, "Export": {"Name": "gameflex-development-PostsTableName"}}, "MediaTableName": {"Description": "media DynamoDB Table Name", "Value": {"Ref": "MediaTableCFC93525"}, "Export": {"Name": "gameflex-development-MediaTableName"}}, "UserProfilesTableName": {"Description": "userProfiles DynamoDB Table Name", "Value": {"Ref": "UserProfilesTableF49D814C"}, "Export": {"Name": "gameflex-development-UserProfilesTableName"}}, "CommentsTableName": {"Description": "comments DynamoDB Table Name", "Value": {"Ref": "CommentsTableBBDBF0A8"}, "Export": {"Name": "gameflex-development-CommentsTableName"}}, "LikesTableName": {"Description": "likes DynamoDB Table Name", "Value": {"Ref": "LikesTable9511B0A4"}, "Export": {"Name": "gameflex-development-LikesTableName"}}, "FollowsTableName": {"Description": "follows DynamoDB Table Name", "Value": {"Ref": "FollowsTable7B81FE10"}, "Export": {"Name": "gameflex-development-FollowsTableName"}}, "ChannelsTableName": {"Description": "channels DynamoDB Table Name", "Value": {"Ref": "ChannelsTable6C883730"}, "Export": {"Name": "gameflex-development-ChannelsTableName"}}, "ChannelMembersTableName": {"Description": "channelMembers DynamoDB Table Name", "Value": {"Ref": "ChannelMembersTable93E36D7C"}, "Export": {"Name": "gameflex-development-ChannelMembersTableName"}}, "ReflexesTableName": {"Description": "reflexes DynamoDB Table Name", "Value": {"Ref": "ReflexesTableD80A0AD3"}, "Export": {"Name": "gameflex-development-ReflexesTableName"}}, "UsersTableName": {"Description": "users DynamoDB Table Name", "Value": {"Ref": "UsersTable9725E9C8"}, "Export": {"Name": "gameflex-development-UsersTableName"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}