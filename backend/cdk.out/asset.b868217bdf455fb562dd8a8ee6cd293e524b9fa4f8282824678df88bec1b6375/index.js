"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const client_s3_1 = require("@aws-sdk/client-s3");
const uuid_1 = require("uuid");
// Configure AWS SDK v3 clients
const dynamoClient = new client_dynamodb_1.DynamoDBClient({
    region: process.env.AWS_REGION || 'us-east-1'
});
const dynamodb = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamoClient);
const s3 = new client_s3_1.S3Client({
    region: process.env.AWS_REGION || 'us-east-1'
});
const POSTS_TABLE = process.env.POSTS_TABLE;
const MEDIA_TABLE = process.env.MEDIA_TABLE;
const COMMENTS_TABLE = process.env.COMMENTS_TABLE;
const LIKES_TABLE = process.env.LIKES_TABLE;
const USERS_TABLE = process.env.USERS_TABLE;
const MEDIA_BUCKET = process.env.MEDIA_BUCKET;
// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});
// Get all posts
const getPosts = async (event) => {
    try {
        // Get current user ID from authorizer context
        const currentUserId = getUserIdFromContext(event);
        const scanCommand = new lib_dynamodb_1.ScanCommand({
            TableName: POSTS_TABLE
        });
        const result = await dynamodb.send(scanCommand);
        // Filter out inactive posts and posts that are not published
        const activePosts = (result.Items || []).filter((post) => post.active === true && post.status === 'published');
        // Sort posts by createdAt descending
        const posts = activePosts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        // Fetch media data and like status for posts
        const postsWithMediaAndLikes = await Promise.all(posts.map(async (post) => {
            // Fetch media data if post has mediaId or media_id
            const mediaId = post.mediaId || post.media_id;
            if (mediaId) {
                try {
                    const getMediaCommand = new lib_dynamodb_1.GetCommand({
                        TableName: MEDIA_TABLE,
                        Key: { id: mediaId }
                    });
                    const mediaResult = await dynamodb.send(getMediaCommand);
                    if (mediaResult.Item) {
                        post.media = mediaResult.Item;
                    }
                }
                catch (error) {
                    console.error(`Failed to fetch media for post ${post.id}:`, error);
                    // Continue without media data if fetch fails
                }
            }
            // Check if current user has liked this post
            let isLikedByCurrentUser = false;
            if (currentUserId) {
                try {
                    const getLikeCommand = new lib_dynamodb_1.GetCommand({
                        TableName: LIKES_TABLE,
                        Key: {
                            postId: post.id,
                            userId: currentUserId
                        }
                    });
                    const likeResult = await dynamodb.send(getLikeCommand);
                    isLikedByCurrentUser = !!likeResult.Item;
                }
                catch (error) {
                    console.error(`Failed to check like status for post ${post.id}:`, error);
                    // Continue with false if check fails
                }
            }
            // Add the like status to the post
            post.isLikedByCurrentUser = isLikedByCurrentUser;
            return post;
        }));
        return createResponse(200, {
            posts: postsWithMediaAndLikes,
            count: postsWithMediaAndLikes.length
        });
    }
    catch (error) {
        console.error('GetPosts error:', error);
        return createResponse(500, { error: 'Failed to get posts', details: error.message });
    }
};
// Get single post
const getPost = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        // Get current user ID from authorizer context
        const currentUserId = getUserIdFromContext(event);
        const getPostCommand = new lib_dynamodb_1.GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const result = await dynamodb.send(getPostCommand);
        if (!result.Item) {
            return createResponse(404, { error: 'Post not found' });
        }
        const post = result.Item;
        // Fetch media data if post has media_id
        if (post.media_id) {
            try {
                const getMediaCommand = new lib_dynamodb_1.GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: post.media_id }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);
                if (mediaResult.Item) {
                    post.media = mediaResult.Item;
                }
            }
            catch (error) {
                console.error(`Failed to fetch media for post ${post.id}:`, error);
                // Continue without media data if fetch fails
            }
        }
        // Check if current user has liked this post
        let isLikedByCurrentUser = false;
        if (currentUserId) {
            try {
                const getLikeCommand = new lib_dynamodb_1.GetCommand({
                    TableName: LIKES_TABLE,
                    Key: {
                        postId: post.id,
                        userId: currentUserId
                    }
                });
                const likeResult = await dynamodb.send(getLikeCommand);
                isLikedByCurrentUser = !!likeResult.Item;
            }
            catch (error) {
                console.error(`Failed to check like status for post ${post.id}:`, error);
                // Continue with false if check fails
            }
        }
        // Add the like status to the post
        post.isLikedByCurrentUser = isLikedByCurrentUser;
        return createResponse(200, { post });
    }
    catch (error) {
        console.error('GetPost error:', error);
        return createResponse(500, { error: 'Failed to get post', details: error.message });
    }
};
// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event) => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return event.requestContext.authorizer.userId;
    }
    return null;
};
// Create draft post (first step of multi-step creation)
const createDraftPost = async (event) => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { title, content } = JSON.parse(event.body);
        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);
        if (!content) {
            return createResponse(400, { error: 'Content is required' });
        }
        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        const postId = (0, uuid_1.v4)();
        const post = {
            id: postId,
            title: title || null,
            content,
            mediaId: null, // Will be set later if media is uploaded
            authorId: authorId,
            userId: authorId, // Keep for backwards compatibility
            likes: 0,
            comments: 0,
            reflexes: 0,
            status: 'draft', // Status: draft, uploading_media, published
            active: false, // Not active until published
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        const putCommand = new lib_dynamodb_1.PutCommand({
            TableName: POSTS_TABLE,
            Item: post
        });
        await dynamodb.send(putCommand);
        return createResponse(201, {
            message: 'Draft post created successfully',
            post
        });
    }
    catch (error) {
        console.error('CreateDraftPost error:', error);
        return createResponse(500, { error: 'Failed to create draft post', details: error.message });
    }
};
// Create post (legacy endpoint - now creates and publishes immediately)
const createPost = async (event) => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { title, content, media_id, mediaId } = JSON.parse(event.body);
        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);
        if (!content) {
            return createResponse(400, { error: 'Content is required' });
        }
        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Support both media_id and mediaId field names
        const finalMediaId = media_id || mediaId;
        // If media_id is provided, verify it exists
        if (finalMediaId) {
            try {
                const getMediaCommand = new lib_dynamodb_1.GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: finalMediaId }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);
                if (!mediaResult.Item) {
                    return createResponse(400, { error: 'Invalid media_id: media not found' });
                }
            }
            catch (error) {
                console.error('Error verifying media:', error);
                return createResponse(400, { error: 'Failed to verify media_id' });
            }
        }
        const postId = (0, uuid_1.v4)();
        const post = {
            id: postId,
            title: title || null,
            content,
            mediaId: finalMediaId || null,
            authorId: authorId,
            userId: authorId, // Keep for backwards compatibility
            likes: 0,
            comments: 0,
            reflexes: 0,
            status: 'published', // Status: draft, uploading_media, published
            active: true, // Final switch to show in feeds
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        const putCommand = new lib_dynamodb_1.PutCommand({
            TableName: POSTS_TABLE,
            Item: post
        });
        await dynamodb.send(putCommand);
        // Fetch media data if media_id was provided
        if (finalMediaId) {
            try {
                const getMediaCommand = new lib_dynamodb_1.GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: finalMediaId }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);
                if (mediaResult.Item) {
                    post.media = mediaResult.Item;
                }
            }
            catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }
        return createResponse(201, {
            message: 'Post created successfully',
            post
        });
    }
    catch (error) {
        console.error('CreatePost error:', error);
        return createResponse(500, { error: 'Failed to create post', details: error.message });
    }
};
// Update post
const updatePost = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { title, content, media_id } = JSON.parse(event.body);
        // If media_id is provided, verify it exists
        if (media_id) {
            try {
                const getMediaCommand = new lib_dynamodb_1.GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: media_id }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);
                if (!mediaResult.Item) {
                    return createResponse(400, { error: 'Invalid media_id: media not found' });
                }
            }
            catch (error) {
                console.error('Error verifying media:', error);
                return createResponse(400, { error: 'Failed to verify media_id' });
            }
        }
        const updateExpression = [];
        const expressionAttributeValues = {};
        const expressionAttributeNames = {};
        if (title !== undefined) {
            updateExpression.push('#title = :title');
            expressionAttributeNames['#title'] = 'title';
            expressionAttributeValues[':title'] = title;
        }
        if (content !== undefined) {
            updateExpression.push('#content = :content');
            expressionAttributeNames['#content'] = 'content';
            expressionAttributeValues[':content'] = content;
        }
        if (media_id !== undefined) {
            updateExpression.push('media_id = :media_id');
            expressionAttributeValues[':media_id'] = media_id;
        }
        updateExpression.push('updated_at = :updated_at');
        expressionAttributeValues[':updated_at'] = new Date().toISOString();
        const updateCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: `SET ${updateExpression.join(', ')}`,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);
        const updatedPost = result.Attributes;
        // Fetch media data if post has media_id
        if (updatedPost.media_id) {
            try {
                const getMediaCommand = new lib_dynamodb_1.GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: updatedPost.media_id }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);
                if (mediaResult.Item) {
                    updatedPost.media = mediaResult.Item;
                }
            }
            catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }
        return createResponse(200, {
            message: 'Post updated successfully',
            post: updatedPost
        });
    }
    catch (error) {
        console.error('UpdatePost error:', error);
        return createResponse(500, { error: 'Failed to update post', details: error.message });
    }
};
// Attach media to draft post
const attachMediaToPost = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { media_id } = JSON.parse(event.body);
        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);
        if (!media_id) {
            return createResponse(400, { error: 'media_id is required' });
        }
        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Verify media exists and is uploaded
        const getMediaCommand = new lib_dynamodb_1.GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id: media_id }
        });
        const mediaResult = await dynamodb.send(getMediaCommand);
        if (!mediaResult.Item) {
            return createResponse(400, { error: 'Invalid media_id: media not found' });
        }
        if (mediaResult.Item.status !== 'uploaded') {
            return createResponse(400, { error: 'Media upload not completed' });
        }
        // Get the post and verify ownership
        const getPostCommand = new lib_dynamodb_1.GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(getPostCommand);
        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }
        if (postResult.Item.authorId !== authorId) {
            return createResponse(403, { error: 'Not authorized to modify this post' });
        }
        // Update post with media and change status to uploading_media
        const updateCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET media_id = :media_id, #status = :status, updated_at = :updated_at',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':media_id': media_id,
                ':status': 'uploading_media',
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);
        return createResponse(200, {
            message: 'Media attached to post successfully',
            post: result.Attributes
        });
    }
    catch (error) {
        console.error('AttachMediaToPost error:', error);
        return createResponse(500, { error: 'Failed to attach media to post', details: error.message });
    }
};
// Publish post (final step)
const publishPost = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);
        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Get the post and verify ownership
        const getPostCommand = new lib_dynamodb_1.GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(getPostCommand);
        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }
        if (postResult.Item.authorId !== authorId) {
            return createResponse(403, { error: 'Not authorized to modify this post' });
        }
        // If post has media, verify it's uploaded
        if (postResult.Item.media_id) {
            const getMediaCommand = new lib_dynamodb_1.GetCommand({
                TableName: MEDIA_TABLE,
                Key: { id: postResult.Item.media_id }
            });
            const mediaResult = await dynamodb.send(getMediaCommand);
            if (!mediaResult.Item || mediaResult.Item.status !== 'uploaded') {
                return createResponse(400, { error: 'Media upload not completed' });
            }
        }
        // Update post to published and active
        const updateCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET #status = :status, active = :active, updated_at = :updated_at',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': 'published',
                ':active': true,
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);
        // Fetch media data if post has media_id
        if (result.Attributes.media_id) {
            try {
                const getMediaCommand = new lib_dynamodb_1.GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: result.Attributes.media_id }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);
                if (mediaResult.Item) {
                    result.Attributes.media = mediaResult.Item;
                }
            }
            catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }
        return createResponse(200, {
            message: 'Post published successfully',
            post: result.Attributes
        });
    }
    catch (error) {
        console.error('PublishPost error:', error);
        return createResponse(500, { error: 'Failed to publish post', details: error.message });
    }
};
// Delete post
const deletePost = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        const deleteCommand = new lib_dynamodb_1.DeleteCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        await dynamodb.send(deleteCommand);
        return createResponse(200, { message: 'Post deleted successfully' });
    }
    catch (error) {
        console.error('DeletePost error:', error);
        return createResponse(500, { error: 'Failed to delete post', details: error.message });
    }
};
// Like post
const likePost = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Check if already liked
        const getLikeCommand = new lib_dynamodb_1.GetCommand({
            TableName: LIKES_TABLE,
            Key: { postId: id, userId: userId }
        });
        const existingLike = await dynamodb.send(getLikeCommand);
        if (existingLike.Item) {
            return createResponse(400, { error: 'Post already liked' });
        }
        // Add like
        const putLikeCommand = new lib_dynamodb_1.PutCommand({
            TableName: LIKES_TABLE,
            Item: {
                postId: id,
                userId: userId,
                createdAt: new Date().toISOString()
            }
        });
        await dynamodb.send(putLikeCommand);
        // Update post likes count
        const updatePostCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updatePostCommand);
        return createResponse(200, { message: 'Post liked successfully' });
    }
    catch (error) {
        console.error('LikePost error:', error);
        return createResponse(500, { error: 'Failed to like post', details: error.message });
    }
};
// Unlike post
const unlikePost = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Remove like
        const deleteLikeCommand = new lib_dynamodb_1.DeleteCommand({
            TableName: LIKES_TABLE,
            Key: { postId: id, userId: userId }
        });
        await dynamodb.send(deleteLikeCommand);
        // Update post likes count
        const updatePostCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updatePostCommand);
        return createResponse(200, { message: 'Post unliked successfully' });
    }
    catch (error) {
        console.error('UnlikePost error:', error);
        return createResponse(500, { error: 'Failed to unlike post', details: error.message });
    }
};
// Get comments for a post
const getComments = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        // Query comments for the post
        const queryCommand = new lib_dynamodb_1.QueryCommand({
            TableName: COMMENTS_TABLE,
            IndexName: 'postId-index',
            KeyConditionExpression: 'postId = :postId',
            ExpressionAttributeValues: {
                ':postId': id
            }
        });
        const result = await dynamodb.send(queryCommand);
        // Sort comments by createdAt ascending (oldest first)
        const comments = (result.Items || []).sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        // Fetch user data for each comment
        const commentsWithUserData = await Promise.all(comments.map(async (comment) => {
            try {
                const getUserCommand = new lib_dynamodb_1.GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: comment.userId }
                });
                const userResult = await dynamodb.send(getUserCommand);
                if (userResult.Item) {
                    comment.username = userResult.Item.username;
                    comment.displayName = userResult.Item.displayName;
                    comment.avatarUrl = userResult.Item.avatarUrl;
                }
            }
            catch (error) {
                console.error(`Failed to fetch user data for comment ${comment.id}:`, error);
                // Continue without user data if fetch fails
            }
            return comment;
        }));
        return createResponse(200, {
            comments: commentsWithUserData,
            count: commentsWithUserData.length
        });
    }
    catch (error) {
        console.error('GetComments error:', error);
        return createResponse(500, { error: 'Failed to get comments', details: error.message });
    }
};
// Create a comment
const createComment = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { content } = JSON.parse(event.body);
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!content || content.trim() === '') {
            return createResponse(400, { error: 'Content is required' });
        }
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Verify the post exists
        const getPostCommand = new lib_dynamodb_1.GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(getPostCommand);
        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }
        const commentId = (0, uuid_1.v4)();
        const comment = {
            id: commentId,
            postId: id,
            userId: userId,
            content: content.trim(),
            likeCount: 0,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        // Create the comment
        const putCommentCommand = new lib_dynamodb_1.PutCommand({
            TableName: COMMENTS_TABLE,
            Item: comment
        });
        await dynamodb.send(putCommentCommand);
        // Update post comments count
        const updatePostCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD comments :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updatePostCommand);
        // Fetch user data for the response
        try {
            const getUserCommand = new lib_dynamodb_1.GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userId }
            });
            const userResult = await dynamodb.send(getUserCommand);
            if (userResult.Item) {
                comment.username = userResult.Item.username;
                comment.displayName = userResult.Item.displayName;
                comment.avatarUrl = userResult.Item.avatarUrl;
            }
        }
        catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }
        return createResponse(201, {
            message: 'Comment created successfully',
            comment
        });
    }
    catch (error) {
        console.error('CreateComment error:', error);
        return createResponse(500, { error: 'Failed to create comment', details: error.message });
    }
};
// Update a comment
const updateComment = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Comment ID is required' });
        }
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { content } = JSON.parse(event.body);
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!content || content.trim() === '') {
            return createResponse(400, { error: 'Content is required' });
        }
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Get the comment to verify ownership
        const getCommentCommand = new lib_dynamodb_1.GetCommand({
            TableName: COMMENTS_TABLE,
            Key: { id }
        });
        const commentResult = await dynamodb.send(getCommentCommand);
        if (!commentResult.Item) {
            return createResponse(404, { error: 'Comment not found' });
        }
        // Verify the user owns the comment
        if (commentResult.Item.userId !== userId) {
            return createResponse(403, { error: 'You can only update your own comments' });
        }
        // Update the comment
        const updateCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: COMMENTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET content = :content, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':content': content.trim(),
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);
        const updatedComment = result.Attributes;
        // Fetch user data for the response
        try {
            const getUserCommand = new lib_dynamodb_1.GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userId }
            });
            const userResult = await dynamodb.send(getUserCommand);
            if (userResult.Item) {
                updatedComment.username = userResult.Item.username;
                updatedComment.displayName = userResult.Item.displayName;
                updatedComment.avatarUrl = userResult.Item.avatarUrl;
            }
        }
        catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }
        return createResponse(200, {
            message: 'Comment updated successfully',
            comment: updatedComment
        });
    }
    catch (error) {
        console.error('UpdateComment error:', error);
        return createResponse(500, { error: 'Failed to update comment', details: error.message });
    }
};
// Delete a comment
const deleteComment = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Comment ID is required' });
        }
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }
        // Get the comment to verify ownership and get post_id
        const getCommentCommand = new lib_dynamodb_1.GetCommand({
            TableName: COMMENTS_TABLE,
            Key: { id }
        });
        const commentResult = await dynamodb.send(getCommentCommand);
        if (!commentResult.Item) {
            return createResponse(404, { error: 'Comment not found' });
        }
        // Verify the user owns the comment
        if (commentResult.Item.userId !== userId) {
            return createResponse(403, { error: 'You can only delete your own comments' });
        }
        const postId = commentResult.Item.postId;
        // Delete the comment
        const deleteCommentCommand = new lib_dynamodb_1.DeleteCommand({
            TableName: COMMENTS_TABLE,
            Key: { id }
        });
        await dynamodb.send(deleteCommentCommand);
        // Update post comments count
        const updatePostCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId },
            UpdateExpression: 'ADD comments :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updatePostCommand);
        return createResponse(200, { message: 'Comment deleted successfully' });
    }
    catch (error) {
        console.error('DeleteComment error:', error);
        return createResponse(500, { error: 'Failed to delete comment', details: error.message });
    }
};
// Main handler
const handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    const { httpMethod, path, pathParameters } = event;
    try {
        // Posts routes
        if (httpMethod === 'GET' && path === '/posts') {
            return await getPosts(event);
        }
        else if (httpMethod === 'POST' && path === '/posts/draft') {
            return await createDraftPost(event);
        }
        else if (httpMethod === 'POST' && path === '/posts') {
            return await createPost(event);
        }
        else if (httpMethod === 'GET' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like') && !path.includes('/media') && !path.includes('/publish')) {
            return await getPost(event);
        }
        else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && path.includes('/media')) {
            return await attachMediaToPost(event);
        }
        else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && path.includes('/publish')) {
            return await publishPost(event);
        }
        else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like') && !path.includes('/media') && !path.includes('/publish')) {
            return await updatePost(event);
        }
        else if (httpMethod === 'DELETE' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like')) {
            return await deletePost(event);
        }
        // Like routes
        else if (httpMethod === 'POST' && path.includes('/like')) {
            return await likePost(event);
        }
        else if (httpMethod === 'DELETE' && path.includes('/like')) {
            return await unlikePost(event);
        }
        // Comments routes
        else if (httpMethod === 'GET' && path.includes('/comments') && pathParameters && pathParameters.id) {
            return await getComments(event);
        }
        else if (httpMethod === 'POST' && path.includes('/comments') && pathParameters && pathParameters.id) {
            return await createComment(event);
        }
        else if (httpMethod === 'PUT' && path.startsWith('/comments/') && pathParameters && pathParameters.id) {
            return await updateComment(event);
        }
        else if (httpMethod === 'DELETE' && path.startsWith('/comments/') && pathParameters && pathParameters.id) {
            return await deleteComment(event);
        }
        else {
            return createResponse(404, { error: 'Not found' });
        }
    }
    catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
exports.handler = handler;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyJpbmRleC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSw4REFBMEQ7QUFDMUQsd0RBQWdKO0FBQ2hKLGtEQUE4QztBQUM5QywrQkFBb0M7QUFHcEMsK0JBQStCO0FBQy9CLE1BQU0sWUFBWSxHQUFHLElBQUksZ0NBQWMsQ0FBQztJQUNwQyxNQUFNLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxVQUFVLElBQUksV0FBVztDQUNoRCxDQUFDLENBQUM7QUFFSCxNQUFNLFFBQVEsR0FBRyxxQ0FBc0IsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7QUFDM0QsTUFBTSxFQUFFLEdBQUcsSUFBSSxvQkFBUSxDQUFDO0lBQ3BCLE1BQU0sRUFBRSxPQUFPLENBQUMsR0FBRyxDQUFDLFVBQVUsSUFBSSxXQUFXO0NBQ2hELENBQUMsQ0FBQztBQUVILE1BQU0sV0FBVyxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDO0FBQzVDLE1BQU0sV0FBVyxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDO0FBQzVDLE1BQU0sY0FBYyxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsY0FBYyxDQUFDO0FBQ2xELE1BQU0sV0FBVyxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDO0FBQzVDLE1BQU0sV0FBVyxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDO0FBQzVDLE1BQU0sWUFBWSxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsWUFBWSxDQUFDO0FBdUQ5QyxxQ0FBcUM7QUFDckMsTUFBTSxjQUFjLEdBQUcsQ0FBQyxVQUFrQixFQUFFLElBQVMsRUFBeUIsRUFBRSxDQUFDLENBQUM7SUFDOUUsVUFBVTtJQUNWLE9BQU8sRUFBRTtRQUNMLGNBQWMsRUFBRSxrQkFBa0I7UUFDbEMsNkJBQTZCLEVBQUUsR0FBRztRQUNsQyw4QkFBOEIsRUFBRSxzRUFBc0U7UUFDdEcsOEJBQThCLEVBQUUsNkJBQTZCO0tBQ2hFO0lBQ0QsSUFBSSxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDO0NBQzdCLENBQUMsQ0FBQztBQUVILGdCQUFnQjtBQUNoQixNQUFNLFFBQVEsR0FBRyxLQUFLLEVBQUUsS0FBMkIsRUFBa0MsRUFBRTtJQUNuRixJQUFJLENBQUM7UUFDRCw4Q0FBOEM7UUFDOUMsTUFBTSxhQUFhLEdBQUcsb0JBQW9CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFbEQsTUFBTSxXQUFXLEdBQUcsSUFBSSwwQkFBVyxDQUFDO1lBQ2hDLFNBQVMsRUFBRSxXQUFXO1NBQ3pCLENBQUMsQ0FBQztRQUNILE1BQU0sTUFBTSxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUVoRCw2REFBNkQ7UUFDN0QsTUFBTSxXQUFXLEdBQUcsQ0FBQyxNQUFNLENBQUMsS0FBSyxJQUFJLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQVMsRUFBRSxFQUFFLENBQzFELElBQUksQ0FBQyxNQUFNLEtBQUssSUFBSSxJQUFJLElBQUksQ0FBQyxNQUFNLEtBQUssV0FBVyxDQUN0RCxDQUFDO1FBRUYscUNBQXFDO1FBQ3JDLE1BQU0sS0FBSyxHQUFHLFdBQVcsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFNLEVBQUUsQ0FBTSxFQUFFLEVBQUUsQ0FBQyxJQUFJLElBQUksQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsT0FBTyxFQUFFLEdBQUcsSUFBSSxJQUFJLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7UUFFdEgsNkNBQTZDO1FBQzdDLE1BQU0sc0JBQXNCLEdBQUcsTUFBTSxPQUFPLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsS0FBSyxFQUFFLElBQVMsRUFBRSxFQUFFO1lBQzNFLG1EQUFtRDtZQUNuRCxNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsT0FBTyxJQUFJLElBQUksQ0FBQyxRQUFRLENBQUM7WUFDOUMsSUFBSSxPQUFPLEVBQUUsQ0FBQztnQkFDVixJQUFJLENBQUM7b0JBQ0QsTUFBTSxlQUFlLEdBQUcsSUFBSSx5QkFBVSxDQUFDO3dCQUNuQyxTQUFTLEVBQUUsV0FBVzt3QkFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFFLE9BQU8sRUFBRTtxQkFDdkIsQ0FBQyxDQUFDO29CQUNILE1BQU0sV0FBVyxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztvQkFFekQsSUFBSSxXQUFXLENBQUMsSUFBSSxFQUFFLENBQUM7d0JBQ25CLElBQUksQ0FBQyxLQUFLLEdBQUcsV0FBVyxDQUFDLElBQUksQ0FBQztvQkFDbEMsQ0FBQztnQkFDTCxDQUFDO2dCQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7b0JBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxrQ0FBa0MsSUFBSSxDQUFDLEVBQUUsR0FBRyxFQUFFLEtBQUssQ0FBQyxDQUFDO29CQUNuRSw2Q0FBNkM7Z0JBQ2pELENBQUM7WUFDTCxDQUFDO1lBRUQsNENBQTRDO1lBQzVDLElBQUksb0JBQW9CLEdBQUcsS0FBSyxDQUFDO1lBQ2pDLElBQUksYUFBYSxFQUFFLENBQUM7Z0JBQ2hCLElBQUksQ0FBQztvQkFDRCxNQUFNLGNBQWMsR0FBRyxJQUFJLHlCQUFVLENBQUM7d0JBQ2xDLFNBQVMsRUFBRSxXQUFXO3dCQUN0QixHQUFHLEVBQUU7NEJBQ0QsTUFBTSxFQUFFLElBQUksQ0FBQyxFQUFFOzRCQUNmLE1BQU0sRUFBRSxhQUFhO3lCQUN4QjtxQkFDSixDQUFDLENBQUM7b0JBQ0gsTUFBTSxVQUFVLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO29CQUV2RCxvQkFBb0IsR0FBRyxDQUFDLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQztnQkFDN0MsQ0FBQztnQkFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO29CQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsd0NBQXdDLElBQUksQ0FBQyxFQUFFLEdBQUcsRUFBRSxLQUFLLENBQUMsQ0FBQztvQkFDekUscUNBQXFDO2dCQUN6QyxDQUFDO1lBQ0wsQ0FBQztZQUVELGtDQUFrQztZQUNsQyxJQUFJLENBQUMsb0JBQW9CLEdBQUcsb0JBQW9CLENBQUM7WUFFakQsT0FBTyxJQUFJLENBQUM7UUFDaEIsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUVKLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRTtZQUN2QixLQUFLLEVBQUUsc0JBQXNCO1lBQzdCLEtBQUssRUFBRSxzQkFBc0IsQ0FBQyxNQUFNO1NBQ3ZDLENBQUMsQ0FBQztJQUVQLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUN4QyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUscUJBQXFCLEVBQUUsT0FBTyxFQUFHLEtBQWUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQ3BHLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRixrQkFBa0I7QUFDbEIsTUFBTSxPQUFPLEdBQUcsS0FBSyxFQUFFLEtBQTJCLEVBQWtDLEVBQUU7SUFDbEYsSUFBSSxDQUFDO1FBQ0QsTUFBTSxFQUFFLEVBQUUsRUFBRSxHQUFHLEtBQUssQ0FBQyxjQUFjLElBQUksRUFBRSxDQUFDO1FBRTFDLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQztZQUNOLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSxxQkFBcUIsRUFBRSxDQUFDLENBQUM7UUFDakUsQ0FBQztRQUVELDhDQUE4QztRQUM5QyxNQUFNLGFBQWEsR0FBRyxvQkFBb0IsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUVsRCxNQUFNLGNBQWMsR0FBRyxJQUFJLHlCQUFVLENBQUM7WUFDbEMsU0FBUyxFQUFFLFdBQVc7WUFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFFO1NBQ2QsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxNQUFNLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1FBRW5ELElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDZixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsZ0JBQWdCLEVBQUUsQ0FBQyxDQUFDO1FBQzVELENBQUM7UUFFRCxNQUFNLElBQUksR0FBRyxNQUFNLENBQUMsSUFBVyxDQUFDO1FBRWhDLHdDQUF3QztRQUN4QyxJQUFJLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQztZQUNoQixJQUFJLENBQUM7Z0JBQ0QsTUFBTSxlQUFlLEdBQUcsSUFBSSx5QkFBVSxDQUFDO29CQUNuQyxTQUFTLEVBQUUsV0FBVztvQkFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFFLElBQUksQ0FBQyxRQUFRLEVBQUU7aUJBQzdCLENBQUMsQ0FBQztnQkFDSCxNQUFNLFdBQVcsR0FBRyxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUM7Z0JBRXpELElBQUksV0FBVyxDQUFDLElBQUksRUFBRSxDQUFDO29CQUNuQixJQUFJLENBQUMsS0FBSyxHQUFHLFdBQVcsQ0FBQyxJQUFJLENBQUM7Z0JBQ2xDLENBQUM7WUFDTCxDQUFDO1lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztnQkFDYixPQUFPLENBQUMsS0FBSyxDQUFDLGtDQUFrQyxJQUFJLENBQUMsRUFBRSxHQUFHLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0JBQ25FLDZDQUE2QztZQUNqRCxDQUFDO1FBQ0wsQ0FBQztRQUVELDRDQUE0QztRQUM1QyxJQUFJLG9CQUFvQixHQUFHLEtBQUssQ0FBQztRQUNqQyxJQUFJLGFBQWEsRUFBRSxDQUFDO1lBQ2hCLElBQUksQ0FBQztnQkFDRCxNQUFNLGNBQWMsR0FBRyxJQUFJLHlCQUFVLENBQUM7b0JBQ2xDLFNBQVMsRUFBRSxXQUFXO29CQUN0QixHQUFHLEVBQUU7d0JBQ0QsTUFBTSxFQUFFLElBQUksQ0FBQyxFQUFFO3dCQUNmLE1BQU0sRUFBRSxhQUFhO3FCQUN4QjtpQkFDSixDQUFDLENBQUM7Z0JBQ0gsTUFBTSxVQUFVLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO2dCQUV2RCxvQkFBb0IsR0FBRyxDQUFDLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQztZQUM3QyxDQUFDO1lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztnQkFDYixPQUFPLENBQUMsS0FBSyxDQUFDLHdDQUF3QyxJQUFJLENBQUMsRUFBRSxHQUFHLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0JBQ3pFLHFDQUFxQztZQUN6QyxDQUFDO1FBQ0wsQ0FBQztRQUVELGtDQUFrQztRQUNsQyxJQUFJLENBQUMsb0JBQW9CLEdBQUcsb0JBQW9CLENBQUM7UUFFakQsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQztJQUV6QyxDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDdkMsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLG9CQUFvQixFQUFFLE9BQU8sRUFBRyxLQUFlLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztJQUNuRyxDQUFDO0FBQ0wsQ0FBQyxDQUFDO0FBRUYseURBQXlEO0FBQ3pELE1BQU0sb0JBQW9CLEdBQUcsQ0FBQyxLQUEyQixFQUFpQixFQUFFO0lBQ3hFLHNFQUFzRTtJQUN0RSxJQUFJLEtBQUssQ0FBQyxjQUFjLElBQUksS0FBSyxDQUFDLGNBQWMsQ0FBQyxVQUFVLEVBQUUsQ0FBQztRQUMxRCxPQUFRLEtBQUssQ0FBQyxjQUFjLENBQUMsVUFBa0IsQ0FBQyxNQUFNLENBQUM7SUFDM0QsQ0FBQztJQUNELE9BQU8sSUFBSSxDQUFDO0FBQ2hCLENBQUMsQ0FBQztBQUVGLHdEQUF3RDtBQUN4RCxNQUFNLGVBQWUsR0FBRyxLQUFLLEVBQUUsS0FBMkIsRUFBa0MsRUFBRTtJQUMxRixJQUFJLENBQUM7UUFDRCxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ2QsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLDBCQUEwQixFQUFFLENBQUMsQ0FBQztRQUN0RSxDQUFDO1FBRUQsTUFBTSxFQUFFLEtBQUssRUFBRSxPQUFPLEVBQUUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUVsRCxzQ0FBc0M7UUFDdEMsTUFBTSxRQUFRLEdBQUcsb0JBQW9CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFN0MsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDO1lBQ1gsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztRQUNqRSxDQUFDO1FBRUQsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ1osT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBRUQsTUFBTSxNQUFNLEdBQUcsSUFBQSxTQUFNLEdBQUUsQ0FBQztRQUN4QixNQUFNLElBQUksR0FBUTtZQUNkLEVBQUUsRUFBRSxNQUFNO1lBQ1YsS0FBSyxFQUFFLEtBQUssSUFBSSxJQUFJO1lBQ3BCLE9BQU87WUFDUCxPQUFPLEVBQUUsSUFBSSxFQUFFLHlDQUF5QztZQUN4RCxRQUFRLEVBQUUsUUFBUTtZQUNsQixNQUFNLEVBQUUsUUFBUSxFQUFFLG1DQUFtQztZQUNyRCxLQUFLLEVBQUUsQ0FBQztZQUNSLFFBQVEsRUFBRSxDQUFDO1lBQ1gsUUFBUSxFQUFFLENBQUM7WUFDWCxNQUFNLEVBQUUsT0FBTyxFQUFFLDRDQUE0QztZQUM3RCxNQUFNLEVBQUUsS0FBSyxFQUFFLDZCQUE2QjtZQUM1QyxTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7WUFDbkMsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO1NBQ3RDLENBQUM7UUFFRixNQUFNLFVBQVUsR0FBRyxJQUFJLHlCQUFVLENBQUM7WUFDOUIsU0FBUyxFQUFFLFdBQVc7WUFDdEIsSUFBSSxFQUFFLElBQUk7U0FDYixDQUFDLENBQUM7UUFDSCxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7UUFFaEMsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFO1lBQ3ZCLE9BQU8sRUFBRSxpQ0FBaUM7WUFDMUMsSUFBSTtTQUNQLENBQUMsQ0FBQztJQUVQLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyx3QkFBd0IsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUMvQyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsNkJBQTZCLEVBQUUsT0FBTyxFQUFHLEtBQWUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQzVHLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRix3RUFBd0U7QUFDeEUsTUFBTSxVQUFVLEdBQUcsS0FBSyxFQUFFLEtBQTJCLEVBQWtDLEVBQUU7SUFDckYsSUFBSSxDQUFDO1FBQ0QsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNkLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSwwQkFBMEIsRUFBRSxDQUFDLENBQUM7UUFDdEUsQ0FBQztRQUVELE1BQU0sRUFBRSxLQUFLLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxPQUFPLEVBQUUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUVyRSxzQ0FBc0M7UUFDdEMsTUFBTSxRQUFRLEdBQUcsb0JBQW9CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFN0MsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDO1lBQ1gsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztRQUNqRSxDQUFDO1FBRUQsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ1osT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBRUQsZ0RBQWdEO1FBQ2hELE1BQU0sWUFBWSxHQUFHLFFBQVEsSUFBSSxPQUFPLENBQUM7UUFFekMsNENBQTRDO1FBQzVDLElBQUksWUFBWSxFQUFFLENBQUM7WUFDZixJQUFJLENBQUM7Z0JBQ0QsTUFBTSxlQUFlLEdBQUcsSUFBSSx5QkFBVSxDQUFDO29CQUNuQyxTQUFTLEVBQUUsV0FBVztvQkFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFFLFlBQVksRUFBRTtpQkFDNUIsQ0FBQyxDQUFDO2dCQUNILE1BQU0sV0FBVyxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztnQkFFekQsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLEVBQUUsQ0FBQztvQkFDcEIsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLG1DQUFtQyxFQUFFLENBQUMsQ0FBQztnQkFDL0UsQ0FBQztZQUNMLENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsd0JBQXdCLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0JBQy9DLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSwyQkFBMkIsRUFBRSxDQUFDLENBQUM7WUFDdkUsQ0FBQztRQUNMLENBQUM7UUFFRCxNQUFNLE1BQU0sR0FBRyxJQUFBLFNBQU0sR0FBRSxDQUFDO1FBQ3hCLE1BQU0sSUFBSSxHQUFRO1lBQ2QsRUFBRSxFQUFFLE1BQU07WUFDVixLQUFLLEVBQUUsS0FBSyxJQUFJLElBQUk7WUFDcEIsT0FBTztZQUNQLE9BQU8sRUFBRSxZQUFZLElBQUksSUFBSTtZQUM3QixRQUFRLEVBQUUsUUFBUTtZQUNsQixNQUFNLEVBQUUsUUFBUSxFQUFFLG1DQUFtQztZQUNyRCxLQUFLLEVBQUUsQ0FBQztZQUNSLFFBQVEsRUFBRSxDQUFDO1lBQ1gsUUFBUSxFQUFFLENBQUM7WUFDWCxNQUFNLEVBQUUsV0FBVyxFQUFFLDRDQUE0QztZQUNqRSxNQUFNLEVBQUUsSUFBSSxFQUFFLGdDQUFnQztZQUM5QyxTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7WUFDbkMsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO1NBQ3RDLENBQUM7UUFFRixNQUFNLFVBQVUsR0FBRyxJQUFJLHlCQUFVLENBQUM7WUFDOUIsU0FBUyxFQUFFLFdBQVc7WUFDdEIsSUFBSSxFQUFFLElBQUk7U0FDYixDQUFDLENBQUM7UUFDSCxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7UUFFaEMsNENBQTRDO1FBQzVDLElBQUksWUFBWSxFQUFFLENBQUM7WUFDZixJQUFJLENBQUM7Z0JBQ0QsTUFBTSxlQUFlLEdBQUcsSUFBSSx5QkFBVSxDQUFDO29CQUNuQyxTQUFTLEVBQUUsV0FBVztvQkFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFFLFlBQVksRUFBRTtpQkFDNUIsQ0FBQyxDQUFDO2dCQUNILE1BQU0sV0FBVyxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztnQkFFekQsSUFBSSxXQUFXLENBQUMsSUFBSSxFQUFFLENBQUM7b0JBQ25CLElBQUksQ0FBQyxLQUFLLEdBQUcsV0FBVyxDQUFDLElBQUksQ0FBQztnQkFDbEMsQ0FBQztZQUNMLENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsb0NBQW9DLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0JBQzNELDBDQUEwQztZQUM5QyxDQUFDO1FBQ0wsQ0FBQztRQUVELE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRTtZQUN2QixPQUFPLEVBQUUsMkJBQTJCO1lBQ3BDLElBQUk7U0FDUCxDQUFDLENBQUM7SUFFUCxDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsbUJBQW1CLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDMUMsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHVCQUF1QixFQUFFLE9BQU8sRUFBRyxLQUFlLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztJQUN0RyxDQUFDO0FBQ0wsQ0FBQyxDQUFDO0FBRUYsY0FBYztBQUNkLE1BQU0sVUFBVSxHQUFHLEtBQUssRUFBRSxLQUEyQixFQUFrQyxFQUFFO0lBQ3JGLElBQUksQ0FBQztRQUNELE1BQU0sRUFBRSxFQUFFLEVBQUUsR0FBRyxLQUFLLENBQUMsY0FBYyxJQUFJLEVBQUUsQ0FBQztRQUUxQyxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUM7WUFDTixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUscUJBQXFCLEVBQUUsQ0FBQyxDQUFDO1FBQ2pFLENBQUM7UUFFRCxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ2QsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLDBCQUEwQixFQUFFLENBQUMsQ0FBQztRQUN0RSxDQUFDO1FBRUQsTUFBTSxFQUFFLEtBQUssRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUM7UUFFNUQsNENBQTRDO1FBQzVDLElBQUksUUFBUSxFQUFFLENBQUM7WUFDWCxJQUFJLENBQUM7Z0JBQ0QsTUFBTSxlQUFlLEdBQUcsSUFBSSx5QkFBVSxDQUFDO29CQUNuQyxTQUFTLEVBQUUsV0FBVztvQkFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFFLFFBQVEsRUFBRTtpQkFDeEIsQ0FBQyxDQUFDO2dCQUNILE1BQU0sV0FBVyxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztnQkFFekQsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLEVBQUUsQ0FBQztvQkFDcEIsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLG1DQUFtQyxFQUFFLENBQUMsQ0FBQztnQkFDL0UsQ0FBQztZQUNMLENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsd0JBQXdCLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0JBQy9DLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSwyQkFBMkIsRUFBRSxDQUFDLENBQUM7WUFDdkUsQ0FBQztRQUNMLENBQUM7UUFFRCxNQUFNLGdCQUFnQixHQUFhLEVBQUUsQ0FBQztRQUN0QyxNQUFNLHlCQUF5QixHQUF3QixFQUFFLENBQUM7UUFDMUQsTUFBTSx3QkFBd0IsR0FBMkIsRUFBRSxDQUFDO1FBRTVELElBQUksS0FBSyxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQ3RCLGdCQUFnQixDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1lBQ3pDLHdCQUF3QixDQUFDLFFBQVEsQ0FBQyxHQUFHLE9BQU8sQ0FBQztZQUM3Qyx5QkFBeUIsQ0FBQyxRQUFRLENBQUMsR0FBRyxLQUFLLENBQUM7UUFDaEQsQ0FBQztRQUVELElBQUksT0FBTyxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQ3hCLGdCQUFnQixDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1lBQzdDLHdCQUF3QixDQUFDLFVBQVUsQ0FBQyxHQUFHLFNBQVMsQ0FBQztZQUNqRCx5QkFBeUIsQ0FBQyxVQUFVLENBQUMsR0FBRyxPQUFPLENBQUM7UUFDcEQsQ0FBQztRQUVELElBQUksUUFBUSxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQ3pCLGdCQUFnQixDQUFDLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO1lBQzlDLHlCQUF5QixDQUFDLFdBQVcsQ0FBQyxHQUFHLFFBQVEsQ0FBQztRQUN0RCxDQUFDO1FBRUQsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLDBCQUEwQixDQUFDLENBQUM7UUFDbEQseUJBQXlCLENBQUMsYUFBYSxDQUFDLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUVwRSxNQUFNLGFBQWEsR0FBRyxJQUFJLDRCQUFhLENBQUM7WUFDcEMsU0FBUyxFQUFFLFdBQVc7WUFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFFO1lBQ1gsZ0JBQWdCLEVBQUUsT0FBTyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDdEQsd0JBQXdCLEVBQUUsd0JBQXdCO1lBQ2xELHlCQUF5QixFQUFFLHlCQUF5QjtZQUNwRCxZQUFZLEVBQUUsU0FBUztTQUMxQixDQUFDLENBQUM7UUFDSCxNQUFNLE1BQU0sR0FBRyxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUM7UUFFbEQsTUFBTSxXQUFXLEdBQUcsTUFBTSxDQUFDLFVBQWlCLENBQUM7UUFFN0Msd0NBQXdDO1FBQ3hDLElBQUksV0FBVyxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ3ZCLElBQUksQ0FBQztnQkFDRCxNQUFNLGVBQWUsR0FBRyxJQUFJLHlCQUFVLENBQUM7b0JBQ25DLFNBQVMsRUFBRSxXQUFXO29CQUN0QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUUsV0FBVyxDQUFDLFFBQVEsRUFBRTtpQkFDcEMsQ0FBQyxDQUFDO2dCQUNILE1BQU0sV0FBVyxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztnQkFFekQsSUFBSSxXQUFXLENBQUMsSUFBSSxFQUFFLENBQUM7b0JBQ25CLFdBQVcsQ0FBQyxLQUFLLEdBQUcsV0FBVyxDQUFDLElBQUksQ0FBQztnQkFDekMsQ0FBQztZQUNMLENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsb0NBQW9DLEVBQUUsS0FBSyxDQUFDLENBQUM7Z0JBQzNELDBDQUEwQztZQUM5QyxDQUFDO1FBQ0wsQ0FBQztRQUVELE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRTtZQUN2QixPQUFPLEVBQUUsMkJBQTJCO1lBQ3BDLElBQUksRUFBRSxXQUFXO1NBQ3BCLENBQUMsQ0FBQztJQUVQLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxtQkFBbUIsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUMxQyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsdUJBQXVCLEVBQUUsT0FBTyxFQUFHLEtBQWUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQ3RHLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRiw2QkFBNkI7QUFDN0IsTUFBTSxpQkFBaUIsR0FBRyxLQUFLLEVBQUUsS0FBMkIsRUFBa0MsRUFBRTtJQUM1RixJQUFJLENBQUM7UUFDRCxNQUFNLEVBQUUsRUFBRSxFQUFFLEdBQUcsS0FBSyxDQUFDLGNBQWMsSUFBSSxFQUFFLENBQUM7UUFFMUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ04sT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztRQUNqRSxDQUFDO1FBRUQsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNkLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSwwQkFBMEIsRUFBRSxDQUFDLENBQUM7UUFDdEUsQ0FBQztRQUVELE1BQU0sRUFBRSxRQUFRLEVBQUUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUU1QyxzQ0FBc0M7UUFDdEMsTUFBTSxRQUFRLEdBQUcsb0JBQW9CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFN0MsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ1osT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHNCQUFzQixFQUFFLENBQUMsQ0FBQztRQUNsRSxDQUFDO1FBRUQsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ1osT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBRUQsc0NBQXNDO1FBQ3RDLE1BQU0sZUFBZSxHQUFHLElBQUkseUJBQVUsQ0FBQztZQUNuQyxTQUFTLEVBQUUsV0FBVztZQUN0QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUUsUUFBUSxFQUFFO1NBQ3hCLENBQUMsQ0FBQztRQUNILE1BQU0sV0FBVyxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztRQUV6RCxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ3BCLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSxtQ0FBbUMsRUFBRSxDQUFDLENBQUM7UUFDL0UsQ0FBQztRQUVELElBQUksV0FBVyxDQUFDLElBQUksQ0FBQyxNQUFNLEtBQUssVUFBVSxFQUFFLENBQUM7WUFDekMsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLDRCQUE0QixFQUFFLENBQUMsQ0FBQztRQUN4RSxDQUFDO1FBRUQsb0NBQW9DO1FBQ3BDLE1BQU0sY0FBYyxHQUFHLElBQUkseUJBQVUsQ0FBQztZQUNsQyxTQUFTLEVBQUUsV0FBVztZQUN0QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUU7U0FDZCxDQUFDLENBQUM7UUFDSCxNQUFNLFVBQVUsR0FBRyxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7UUFFdkQsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNuQixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsZ0JBQWdCLEVBQUUsQ0FBQyxDQUFDO1FBQzVELENBQUM7UUFFRCxJQUFLLFVBQVUsQ0FBQyxJQUFZLENBQUMsUUFBUSxLQUFLLFFBQVEsRUFBRSxDQUFDO1lBQ2pELE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSxvQ0FBb0MsRUFBRSxDQUFDLENBQUM7UUFDaEYsQ0FBQztRQUVELDhEQUE4RDtRQUM5RCxNQUFNLGFBQWEsR0FBRyxJQUFJLDRCQUFhLENBQUM7WUFDcEMsU0FBUyxFQUFFLFdBQVc7WUFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFFO1lBQ1gsZ0JBQWdCLEVBQUUsdUVBQXVFO1lBQ3pGLHdCQUF3QixFQUFFO2dCQUN0QixTQUFTLEVBQUUsUUFBUTthQUN0QjtZQUNELHlCQUF5QixFQUFFO2dCQUN2QixXQUFXLEVBQUUsUUFBUTtnQkFDckIsU0FBUyxFQUFFLGlCQUFpQjtnQkFDNUIsYUFBYSxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO2FBQzFDO1lBQ0QsWUFBWSxFQUFFLFNBQVM7U0FDMUIsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxNQUFNLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1FBRWxELE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRTtZQUN2QixPQUFPLEVBQUUscUNBQXFDO1lBQzlDLElBQUksRUFBRSxNQUFNLENBQUMsVUFBVTtTQUMxQixDQUFDLENBQUM7SUFFUCxDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsMEJBQTBCLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDakQsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLGdDQUFnQyxFQUFFLE9BQU8sRUFBRyxLQUFlLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztJQUMvRyxDQUFDO0FBQ0wsQ0FBQyxDQUFDO0FBRUYsNEJBQTRCO0FBQzVCLE1BQU0sV0FBVyxHQUFHLEtBQUssRUFBRSxLQUEyQixFQUFrQyxFQUFFO0lBQ3RGLElBQUksQ0FBQztRQUNELE1BQU0sRUFBRSxFQUFFLEVBQUUsR0FBRyxLQUFLLENBQUMsY0FBYyxJQUFJLEVBQUUsQ0FBQztRQUUxQyxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUM7WUFDTixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUscUJBQXFCLEVBQUUsQ0FBQyxDQUFDO1FBQ2pFLENBQUM7UUFFRCxzQ0FBc0M7UUFDdEMsTUFBTSxRQUFRLEdBQUcsb0JBQW9CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFN0MsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ1osT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBRUQsb0NBQW9DO1FBQ3BDLE1BQU0sY0FBYyxHQUFHLElBQUkseUJBQVUsQ0FBQztZQUNsQyxTQUFTLEVBQUUsV0FBVztZQUN0QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUU7U0FDZCxDQUFDLENBQUM7UUFDSCxNQUFNLFVBQVUsR0FBRyxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7UUFFdkQsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNuQixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsZ0JBQWdCLEVBQUUsQ0FBQyxDQUFDO1FBQzVELENBQUM7UUFFRCxJQUFLLFVBQVUsQ0FBQyxJQUFZLENBQUMsUUFBUSxLQUFLLFFBQVEsRUFBRSxDQUFDO1lBQ2pELE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSxvQ0FBb0MsRUFBRSxDQUFDLENBQUM7UUFDaEYsQ0FBQztRQUVELDBDQUEwQztRQUMxQyxJQUFLLFVBQVUsQ0FBQyxJQUFZLENBQUMsUUFBUSxFQUFFLENBQUM7WUFDcEMsTUFBTSxlQUFlLEdBQUcsSUFBSSx5QkFBVSxDQUFDO2dCQUNuQyxTQUFTLEVBQUUsV0FBVztnQkFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFHLFVBQVUsQ0FBQyxJQUFZLENBQUMsUUFBUSxFQUFFO2FBQ2pELENBQUMsQ0FBQztZQUNILE1BQU0sV0FBVyxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztZQUV6RCxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksSUFBSSxXQUFXLENBQUMsSUFBSSxDQUFDLE1BQU0sS0FBSyxVQUFVLEVBQUUsQ0FBQztnQkFDOUQsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLDRCQUE0QixFQUFFLENBQUMsQ0FBQztZQUN4RSxDQUFDO1FBQ0wsQ0FBQztRQUVELHNDQUFzQztRQUN0QyxNQUFNLGFBQWEsR0FBRyxJQUFJLDRCQUFhLENBQUM7WUFDcEMsU0FBUyxFQUFFLFdBQVc7WUFDdEIsR0FBRyxFQUFFLEVBQUUsRUFBRSxFQUFFO1lBQ1gsZ0JBQWdCLEVBQUUsbUVBQW1FO1lBQ3JGLHdCQUF3QixFQUFFO2dCQUN0QixTQUFTLEVBQUUsUUFBUTthQUN0QjtZQUNELHlCQUF5QixFQUFFO2dCQUN2QixTQUFTLEVBQUUsV0FBVztnQkFDdEIsU0FBUyxFQUFFLElBQUk7Z0JBQ2YsYUFBYSxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO2FBQzFDO1lBQ0QsWUFBWSxFQUFFLFNBQVM7U0FDMUIsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxNQUFNLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDO1FBRWxELHdDQUF3QztRQUN4QyxJQUFLLE1BQU0sQ0FBQyxVQUFrQixDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ3RDLElBQUksQ0FBQztnQkFDRCxNQUFNLGVBQWUsR0FBRyxJQUFJLHlCQUFVLENBQUM7b0JBQ25DLFNBQVMsRUFBRSxXQUFXO29CQUN0QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUcsTUFBTSxDQUFDLFVBQWtCLENBQUMsUUFBUSxFQUFFO2lCQUNuRCxDQUFDLENBQUM7Z0JBQ0gsTUFBTSxXQUFXLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDO2dCQUV6RCxJQUFJLFdBQVcsQ0FBQyxJQUFJLEVBQUUsQ0FBQztvQkFDbEIsTUFBTSxDQUFDLFVBQWtCLENBQUMsS0FBSyxHQUFHLFdBQVcsQ0FBQyxJQUFJLENBQUM7Z0JBQ3hELENBQUM7WUFDTCxDQUFDO1lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztnQkFDYixPQUFPLENBQUMsS0FBSyxDQUFDLG9DQUFvQyxFQUFFLEtBQUssQ0FBQyxDQUFDO2dCQUMzRCwwQ0FBMEM7WUFDOUMsQ0FBQztRQUNMLENBQUM7UUFFRCxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUU7WUFDdkIsT0FBTyxFQUFFLDZCQUE2QjtZQUN0QyxJQUFJLEVBQUUsTUFBTSxDQUFDLFVBQVU7U0FDMUIsQ0FBQyxDQUFDO0lBRVAsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDYixPQUFPLENBQUMsS0FBSyxDQUFDLG9CQUFvQixFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQzNDLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSx3QkFBd0IsRUFBRSxPQUFPLEVBQUcsS0FBZSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDdkcsQ0FBQztBQUNMLENBQUMsQ0FBQztBQUVGLGNBQWM7QUFDZCxNQUFNLFVBQVUsR0FBRyxLQUFLLEVBQUUsS0FBMkIsRUFBa0MsRUFBRTtJQUNyRixJQUFJLENBQUM7UUFDRCxNQUFNLEVBQUUsRUFBRSxFQUFFLEdBQUcsS0FBSyxDQUFDLGNBQWMsSUFBSSxFQUFFLENBQUM7UUFFMUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ04sT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztRQUNqRSxDQUFDO1FBRUQsTUFBTSxhQUFhLEdBQUcsSUFBSSw0QkFBYSxDQUFDO1lBQ3BDLFNBQVMsRUFBRSxXQUFXO1lBQ3RCLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRTtTQUNkLENBQUMsQ0FBQztRQUNILE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztRQUVuQyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxPQUFPLEVBQUUsMkJBQTJCLEVBQUUsQ0FBQyxDQUFDO0lBRXpFLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxtQkFBbUIsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUMxQyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsdUJBQXVCLEVBQUUsT0FBTyxFQUFHLEtBQWUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQ3RHLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRixZQUFZO0FBQ1osTUFBTSxRQUFRLEdBQUcsS0FBSyxFQUFFLEtBQTJCLEVBQWtDLEVBQUU7SUFDbkYsSUFBSSxDQUFDO1FBQ0QsTUFBTSxFQUFFLEVBQUUsRUFBRSxHQUFHLEtBQUssQ0FBQyxjQUFjLElBQUksRUFBRSxDQUFDO1FBRTFDLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQztZQUNOLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSxxQkFBcUIsRUFBRSxDQUFDLENBQUM7UUFDakUsQ0FBQztRQUVELHNDQUFzQztRQUN0QyxNQUFNLE1BQU0sR0FBRyxvQkFBb0IsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUUzQyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUM7WUFDVixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsd0JBQXdCLEVBQUUsQ0FBQyxDQUFDO1FBQ3BFLENBQUM7UUFFRCx5QkFBeUI7UUFDekIsTUFBTSxjQUFjLEdBQUcsSUFBSSx5QkFBVSxDQUFDO1lBQ2xDLFNBQVMsRUFBRSxXQUFXO1lBQ3RCLEdBQUcsRUFBRSxFQUFFLE1BQU0sRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLE1BQU0sRUFBRTtTQUN0QyxDQUFDLENBQUM7UUFDSCxNQUFNLFlBQVksR0FBRyxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7UUFFekQsSUFBSSxZQUFZLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDcEIsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLG9CQUFvQixFQUFFLENBQUMsQ0FBQztRQUNoRSxDQUFDO1FBRUQsV0FBVztRQUNYLE1BQU0sY0FBYyxHQUFHLElBQUkseUJBQVUsQ0FBQztZQUNsQyxTQUFTLEVBQUUsV0FBVztZQUN0QixJQUFJLEVBQUU7Z0JBQ0YsTUFBTSxFQUFFLEVBQUU7Z0JBQ1YsTUFBTSxFQUFFLE1BQU07Z0JBQ2QsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO2FBQ3RDO1NBQ0osQ0FBQyxDQUFDO1FBQ0gsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1FBRXBDLDBCQUEwQjtRQUMxQixNQUFNLGlCQUFpQixHQUFHLElBQUksNEJBQWEsQ0FBQztZQUN4QyxTQUFTLEVBQUUsV0FBVztZQUN0QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUU7WUFDWCxnQkFBZ0IsRUFBRSxnQkFBZ0I7WUFDbEMseUJBQXlCLEVBQUUsRUFBRSxNQUFNLEVBQUUsQ0FBQyxFQUFFO1NBQzNDLENBQUMsQ0FBQztRQUNILE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1FBRXZDLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLE9BQU8sRUFBRSx5QkFBeUIsRUFBRSxDQUFDLENBQUM7SUFFdkUsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDYixPQUFPLENBQUMsS0FBSyxDQUFDLGlCQUFpQixFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQ3hDLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSxxQkFBcUIsRUFBRSxPQUFPLEVBQUcsS0FBZSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDcEcsQ0FBQztBQUNMLENBQUMsQ0FBQztBQUVGLGNBQWM7QUFDZCxNQUFNLFVBQVUsR0FBRyxLQUFLLEVBQUUsS0FBMkIsRUFBa0MsRUFBRTtJQUNyRixJQUFJLENBQUM7UUFDRCxNQUFNLEVBQUUsRUFBRSxFQUFFLEdBQUcsS0FBSyxDQUFDLGNBQWMsSUFBSSxFQUFFLENBQUM7UUFFMUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ04sT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztRQUNqRSxDQUFDO1FBRUQsc0NBQXNDO1FBQ3RDLE1BQU0sTUFBTSxHQUFHLG9CQUFvQixDQUFDLEtBQUssQ0FBQyxDQUFDO1FBRTNDLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQztZQUNWLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSx3QkFBd0IsRUFBRSxDQUFDLENBQUM7UUFDcEUsQ0FBQztRQUVELGNBQWM7UUFDZCxNQUFNLGlCQUFpQixHQUFHLElBQUksNEJBQWEsQ0FBQztZQUN4QyxTQUFTLEVBQUUsV0FBVztZQUN0QixHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUU7U0FDdEMsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUM7UUFFdkMsMEJBQTBCO1FBQzFCLE1BQU0saUJBQWlCLEdBQUcsSUFBSSw0QkFBYSxDQUFDO1lBQ3hDLFNBQVMsRUFBRSxXQUFXO1lBQ3RCLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRTtZQUNYLGdCQUFnQixFQUFFLGdCQUFnQjtZQUNsQyx5QkFBeUIsRUFBRSxFQUFFLE1BQU0sRUFBRSxDQUFDLENBQUMsRUFBRTtTQUM1QyxDQUFDLENBQUM7UUFDSCxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQztRQUV2QyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxPQUFPLEVBQUUsMkJBQTJCLEVBQUUsQ0FBQyxDQUFDO0lBRXpFLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxtQkFBbUIsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUMxQyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsdUJBQXVCLEVBQUUsT0FBTyxFQUFHLEtBQWUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQ3RHLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRiwwQkFBMEI7QUFDMUIsTUFBTSxXQUFXLEdBQUcsS0FBSyxFQUFFLEtBQTJCLEVBQWtDLEVBQUU7SUFDdEYsSUFBSSxDQUFDO1FBQ0QsTUFBTSxFQUFFLEVBQUUsRUFBRSxHQUFHLEtBQUssQ0FBQyxjQUFjLElBQUksRUFBRSxDQUFDO1FBRTFDLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQztZQUNOLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSxxQkFBcUIsRUFBRSxDQUFDLENBQUM7UUFDakUsQ0FBQztRQUVELDhCQUE4QjtRQUM5QixNQUFNLFlBQVksR0FBRyxJQUFJLDJCQUFZLENBQUM7WUFDbEMsU0FBUyxFQUFFLGNBQWM7WUFDekIsU0FBUyxFQUFFLGNBQWM7WUFDekIsc0JBQXNCLEVBQUUsa0JBQWtCO1lBQzFDLHlCQUF5QixFQUFFO2dCQUN2QixTQUFTLEVBQUUsRUFBRTthQUNoQjtTQUNKLENBQUMsQ0FBQztRQUNILE1BQU0sTUFBTSxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUVqRCxzREFBc0Q7UUFDdEQsTUFBTSxRQUFRLEdBQUcsQ0FBQyxNQUFNLENBQUMsS0FBSyxJQUFJLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQU0sRUFBRSxDQUFNLEVBQUUsRUFBRSxDQUFDLElBQUksSUFBSSxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxPQUFPLEVBQUUsR0FBRyxJQUFJLElBQUksQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztRQUVsSSxtQ0FBbUM7UUFDbkMsTUFBTSxvQkFBb0IsR0FBRyxNQUFNLE9BQU8sQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxLQUFLLEVBQUUsT0FBWSxFQUFFLEVBQUU7WUFDL0UsSUFBSSxDQUFDO2dCQUNELE1BQU0sY0FBYyxHQUFHLElBQUkseUJBQVUsQ0FBQztvQkFDbEMsU0FBUyxFQUFFLFdBQVc7b0JBQ3RCLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRSxPQUFPLENBQUMsTUFBTSxFQUFFO2lCQUM5QixDQUFDLENBQUM7Z0JBQ0gsTUFBTSxVQUFVLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO2dCQUV2RCxJQUFJLFVBQVUsQ0FBQyxJQUFJLEVBQUUsQ0FBQztvQkFDbEIsT0FBTyxDQUFDLFFBQVEsR0FBRyxVQUFVLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQztvQkFDNUMsT0FBTyxDQUFDLFdBQVcsR0FBRyxVQUFVLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQztvQkFDbEQsT0FBTyxDQUFDLFNBQVMsR0FBRyxVQUFVLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQztnQkFDbEQsQ0FBQztZQUNMLENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMseUNBQXlDLE9BQU8sQ0FBQyxFQUFFLEdBQUcsRUFBRSxLQUFLLENBQUMsQ0FBQztnQkFDN0UsNENBQTRDO1lBQ2hELENBQUM7WUFDRCxPQUFPLE9BQU8sQ0FBQztRQUNuQixDQUFDLENBQUMsQ0FBQyxDQUFDO1FBRUosT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFO1lBQ3ZCLFFBQVEsRUFBRSxvQkFBb0I7WUFDOUIsS0FBSyxFQUFFLG9CQUFvQixDQUFDLE1BQU07U0FDckMsQ0FBQyxDQUFDO0lBRVAsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDYixPQUFPLENBQUMsS0FBSyxDQUFDLG9CQUFvQixFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQzNDLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSx3QkFBd0IsRUFBRSxPQUFPLEVBQUcsS0FBZSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDdkcsQ0FBQztBQUNMLENBQUMsQ0FBQztBQUVGLG1CQUFtQjtBQUNuQixNQUFNLGFBQWEsR0FBRyxLQUFLLEVBQUUsS0FBMkIsRUFBa0MsRUFBRTtJQUN4RixJQUFJLENBQUM7UUFDRCxNQUFNLEVBQUUsRUFBRSxFQUFFLEdBQUcsS0FBSyxDQUFDLGNBQWMsSUFBSSxFQUFFLENBQUM7UUFFMUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ04sT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztRQUNqRSxDQUFDO1FBRUQsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNkLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSwwQkFBMEIsRUFBRSxDQUFDLENBQUM7UUFDdEUsQ0FBQztRQUVELE1BQU0sRUFBRSxPQUFPLEVBQUUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUUzQyxzQ0FBc0M7UUFDdEMsTUFBTSxNQUFNLEdBQUcsb0JBQW9CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFM0MsSUFBSSxDQUFDLE9BQU8sSUFBSSxPQUFPLENBQUMsSUFBSSxFQUFFLEtBQUssRUFBRSxFQUFFLENBQUM7WUFDcEMsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztRQUNqRSxDQUFDO1FBRUQsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQ1YsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBRUQseUJBQXlCO1FBQ3pCLE1BQU0sY0FBYyxHQUFHLElBQUkseUJBQVUsQ0FBQztZQUNsQyxTQUFTLEVBQUUsV0FBVztZQUN0QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUU7U0FDZCxDQUFDLENBQUM7UUFDSCxNQUFNLFVBQVUsR0FBRyxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7UUFFdkQsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNuQixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsZ0JBQWdCLEVBQUUsQ0FBQyxDQUFDO1FBQzVELENBQUM7UUFFRCxNQUFNLFNBQVMsR0FBRyxJQUFBLFNBQU0sR0FBRSxDQUFDO1FBQzNCLE1BQU0sT0FBTyxHQUFRO1lBQ2pCLEVBQUUsRUFBRSxTQUFTO1lBQ2IsTUFBTSxFQUFFLEVBQUU7WUFDVixNQUFNLEVBQUUsTUFBTTtZQUNkLE9BQU8sRUFBRSxPQUFPLENBQUMsSUFBSSxFQUFFO1lBQ3ZCLFNBQVMsRUFBRSxDQUFDO1lBQ1osUUFBUSxFQUFFLElBQUk7WUFDZCxTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7WUFDbkMsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO1NBQ3RDLENBQUM7UUFFRixxQkFBcUI7UUFDckIsTUFBTSxpQkFBaUIsR0FBRyxJQUFJLHlCQUFVLENBQUM7WUFDckMsU0FBUyxFQUFFLGNBQWM7WUFDekIsSUFBSSxFQUFFLE9BQU87U0FDaEIsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUM7UUFFdkMsNkJBQTZCO1FBQzdCLE1BQU0saUJBQWlCLEdBQUcsSUFBSSw0QkFBYSxDQUFDO1lBQ3hDLFNBQVMsRUFBRSxXQUFXO1lBQ3RCLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRTtZQUNYLGdCQUFnQixFQUFFLG1CQUFtQjtZQUNyQyx5QkFBeUIsRUFBRSxFQUFFLE1BQU0sRUFBRSxDQUFDLEVBQUU7U0FDM0MsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUM7UUFFdkMsbUNBQW1DO1FBQ25DLElBQUksQ0FBQztZQUNELE1BQU0sY0FBYyxHQUFHLElBQUkseUJBQVUsQ0FBQztnQkFDbEMsU0FBUyxFQUFFLFdBQVc7Z0JBQ3RCLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRSxNQUFNLEVBQUU7YUFDdEIsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxVQUFVLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBRXZELElBQUksVUFBVSxDQUFDLElBQUksRUFBRSxDQUFDO2dCQUNsQixPQUFPLENBQUMsUUFBUSxHQUFHLFVBQVUsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDO2dCQUM1QyxPQUFPLENBQUMsV0FBVyxHQUFHLFVBQVUsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDO2dCQUNsRCxPQUFPLENBQUMsU0FBUyxHQUFHLFVBQVUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDO1lBQ2xELENBQUM7UUFDTCxDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsd0NBQXdDLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDL0QseUNBQXlDO1FBQzdDLENBQUM7UUFFRCxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUU7WUFDdkIsT0FBTyxFQUFFLDhCQUE4QjtZQUN2QyxPQUFPO1NBQ1YsQ0FBQyxDQUFDO0lBRVAsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDYixPQUFPLENBQUMsS0FBSyxDQUFDLHNCQUFzQixFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQzdDLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSwwQkFBMEIsRUFBRSxPQUFPLEVBQUcsS0FBZSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDekcsQ0FBQztBQUNMLENBQUMsQ0FBQztBQUVGLG1CQUFtQjtBQUNuQixNQUFNLGFBQWEsR0FBRyxLQUFLLEVBQUUsS0FBMkIsRUFBa0MsRUFBRTtJQUN4RixJQUFJLENBQUM7UUFDRCxNQUFNLEVBQUUsRUFBRSxFQUFFLEdBQUcsS0FBSyxDQUFDLGNBQWMsSUFBSSxFQUFFLENBQUM7UUFFMUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ04sT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBRUQsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNkLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSwwQkFBMEIsRUFBRSxDQUFDLENBQUM7UUFDdEUsQ0FBQztRQUVELE1BQU0sRUFBRSxPQUFPLEVBQUUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUUzQyxzQ0FBc0M7UUFDdEMsTUFBTSxNQUFNLEdBQUcsb0JBQW9CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFM0MsSUFBSSxDQUFDLE9BQU8sSUFBSSxPQUFPLENBQUMsSUFBSSxFQUFFLEtBQUssRUFBRSxFQUFFLENBQUM7WUFDcEMsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztRQUNqRSxDQUFDO1FBRUQsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQ1YsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBRUQsc0NBQXNDO1FBQ3RDLE1BQU0saUJBQWlCLEdBQUcsSUFBSSx5QkFBVSxDQUFDO1lBQ3JDLFNBQVMsRUFBRSxjQUFjO1lBQ3pCLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRTtTQUNkLENBQUMsQ0FBQztRQUNILE1BQU0sYUFBYSxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1FBRTdELElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDdEIsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLG1CQUFtQixFQUFFLENBQUMsQ0FBQztRQUMvRCxDQUFDO1FBRUQsbUNBQW1DO1FBQ25DLElBQUssYUFBYSxDQUFDLElBQVksQ0FBQyxNQUFNLEtBQUssTUFBTSxFQUFFLENBQUM7WUFDaEQsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHVDQUF1QyxFQUFFLENBQUMsQ0FBQztRQUNuRixDQUFDO1FBRUQscUJBQXFCO1FBQ3JCLE1BQU0sYUFBYSxHQUFHLElBQUksNEJBQWEsQ0FBQztZQUNwQyxTQUFTLEVBQUUsY0FBYztZQUN6QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUU7WUFDWCxnQkFBZ0IsRUFBRSxnREFBZ0Q7WUFDbEUseUJBQXlCLEVBQUU7Z0JBQ3ZCLFVBQVUsRUFBRSxPQUFPLENBQUMsSUFBSSxFQUFFO2dCQUMxQixZQUFZLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7YUFDekM7WUFDRCxZQUFZLEVBQUUsU0FBUztTQUMxQixDQUFDLENBQUM7UUFDSCxNQUFNLE1BQU0sR0FBRyxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUM7UUFFbEQsTUFBTSxjQUFjLEdBQUcsTUFBTSxDQUFDLFVBQWlCLENBQUM7UUFFaEQsbUNBQW1DO1FBQ25DLElBQUksQ0FBQztZQUNELE1BQU0sY0FBYyxHQUFHLElBQUkseUJBQVUsQ0FBQztnQkFDbEMsU0FBUyxFQUFFLFdBQVc7Z0JBQ3RCLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRSxNQUFNLEVBQUU7YUFDdEIsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxVQUFVLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBRXZELElBQUksVUFBVSxDQUFDLElBQUksRUFBRSxDQUFDO2dCQUNsQixjQUFjLENBQUMsUUFBUSxHQUFHLFVBQVUsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDO2dCQUNuRCxjQUFjLENBQUMsV0FBVyxHQUFHLFVBQVUsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDO2dCQUN6RCxjQUFjLENBQUMsU0FBUyxHQUFHLFVBQVUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDO1lBQ3pELENBQUM7UUFDTCxDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsd0NBQXdDLEVBQUUsS0FBSyxDQUFDLENBQUM7WUFDL0QseUNBQXlDO1FBQzdDLENBQUM7UUFFRCxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUU7WUFDdkIsT0FBTyxFQUFFLDhCQUE4QjtZQUN2QyxPQUFPLEVBQUUsY0FBYztTQUMxQixDQUFDLENBQUM7SUFFUCxDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNiLE9BQU8sQ0FBQyxLQUFLLENBQUMsc0JBQXNCLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDN0MsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLDBCQUEwQixFQUFFLE9BQU8sRUFBRyxLQUFlLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztJQUN6RyxDQUFDO0FBQ0wsQ0FBQyxDQUFDO0FBRUYsbUJBQW1CO0FBQ25CLE1BQU0sYUFBYSxHQUFHLEtBQUssRUFBRSxLQUEyQixFQUFrQyxFQUFFO0lBQ3hGLElBQUksQ0FBQztRQUNELE1BQU0sRUFBRSxFQUFFLEVBQUUsR0FBRyxLQUFLLENBQUMsY0FBYyxJQUFJLEVBQUUsQ0FBQztRQUUxQyxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUM7WUFDTixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsd0JBQXdCLEVBQUUsQ0FBQyxDQUFDO1FBQ3BFLENBQUM7UUFFRCxzQ0FBc0M7UUFDdEMsTUFBTSxNQUFNLEdBQUcsb0JBQW9CLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFM0MsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQ1YsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQztRQUNwRSxDQUFDO1FBRUQsc0RBQXNEO1FBQ3RELE1BQU0saUJBQWlCLEdBQUcsSUFBSSx5QkFBVSxDQUFDO1lBQ3JDLFNBQVMsRUFBRSxjQUFjO1lBQ3pCLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRTtTQUNkLENBQUMsQ0FBQztRQUNILE1BQU0sYUFBYSxHQUFHLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1FBRTdELElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDdEIsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLG1CQUFtQixFQUFFLENBQUMsQ0FBQztRQUMvRCxDQUFDO1FBRUQsbUNBQW1DO1FBQ25DLElBQUssYUFBYSxDQUFDLElBQVksQ0FBQyxNQUFNLEtBQUssTUFBTSxFQUFFLENBQUM7WUFDaEQsT0FBTyxjQUFjLENBQUMsR0FBRyxFQUFFLEVBQUUsS0FBSyxFQUFFLHVDQUF1QyxFQUFFLENBQUMsQ0FBQztRQUNuRixDQUFDO1FBRUQsTUFBTSxNQUFNLEdBQUksYUFBYSxDQUFDLElBQVksQ0FBQyxNQUFNLENBQUM7UUFFbEQscUJBQXFCO1FBQ3JCLE1BQU0sb0JBQW9CLEdBQUcsSUFBSSw0QkFBYSxDQUFDO1lBQzNDLFNBQVMsRUFBRSxjQUFjO1lBQ3pCLEdBQUcsRUFBRSxFQUFFLEVBQUUsRUFBRTtTQUNkLENBQUMsQ0FBQztRQUNILE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1FBRTFDLDZCQUE2QjtRQUM3QixNQUFNLGlCQUFpQixHQUFHLElBQUksNEJBQWEsQ0FBQztZQUN4QyxTQUFTLEVBQUUsV0FBVztZQUN0QixHQUFHLEVBQUUsRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFO1lBQ25CLGdCQUFnQixFQUFFLG1CQUFtQjtZQUNyQyx5QkFBeUIsRUFBRSxFQUFFLE1BQU0sRUFBRSxDQUFDLENBQUMsRUFBRTtTQUM1QyxDQUFDLENBQUM7UUFDSCxNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQztRQUV2QyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxPQUFPLEVBQUUsOEJBQThCLEVBQUUsQ0FBQyxDQUFDO0lBRTVFLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2IsT0FBTyxDQUFDLEtBQUssQ0FBQyxzQkFBc0IsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUM3QyxPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsMEJBQTBCLEVBQUUsT0FBTyxFQUFHLEtBQWUsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQ3pHLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRixlQUFlO0FBQ1IsTUFBTSxPQUFPLEdBQUcsS0FBSyxFQUFFLEtBQTJCLEVBQWtDLEVBQUU7SUFDekYsT0FBTyxDQUFDLEdBQUcsQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFFdEQsTUFBTSxFQUFFLFVBQVUsRUFBRSxJQUFJLEVBQUUsY0FBYyxFQUFFLEdBQUcsS0FBSyxDQUFDO0lBRW5ELElBQUksQ0FBQztRQUNELGVBQWU7UUFDZixJQUFJLFVBQVUsS0FBSyxLQUFLLElBQUksSUFBSSxLQUFLLFFBQVEsRUFBRSxDQUFDO1lBQzVDLE9BQU8sTUFBTSxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDakMsQ0FBQzthQUFNLElBQUksVUFBVSxLQUFLLE1BQU0sSUFBSSxJQUFJLEtBQUssY0FBYyxFQUFFLENBQUM7WUFDMUQsT0FBTyxNQUFNLGVBQWUsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUN4QyxDQUFDO2FBQU0sSUFBSSxVQUFVLEtBQUssTUFBTSxJQUFJLElBQUksS0FBSyxRQUFRLEVBQUUsQ0FBQztZQUNwRCxPQUFPLE1BQU0sVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ25DLENBQUM7YUFBTSxJQUFJLFVBQVUsS0FBSyxLQUFLLElBQUksY0FBYyxJQUFJLGNBQWMsQ0FBQyxFQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUM7WUFDekwsT0FBTyxNQUFNLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNoQyxDQUFDO2FBQU0sSUFBSSxVQUFVLEtBQUssS0FBSyxJQUFJLGNBQWMsSUFBSSxjQUFjLENBQUMsRUFBRSxJQUFJLElBQUksQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQztZQUNoRyxPQUFPLE1BQU0saUJBQWlCLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDMUMsQ0FBQzthQUFNLElBQUksVUFBVSxLQUFLLEtBQUssSUFBSSxjQUFjLElBQUksY0FBYyxDQUFDLEVBQUUsSUFBSSxJQUFJLENBQUMsUUFBUSxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUM7WUFDbEcsT0FBTyxNQUFNLFdBQVcsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNwQyxDQUFDO2FBQU0sSUFBSSxVQUFVLEtBQUssS0FBSyxJQUFJLGNBQWMsSUFBSSxjQUFjLENBQUMsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDO1lBQ3pMLE9BQU8sTUFBTSxVQUFVLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDbkMsQ0FBQzthQUFNLElBQUksVUFBVSxLQUFLLFFBQVEsSUFBSSxjQUFjLElBQUksY0FBYyxDQUFDLEVBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7WUFDbEksT0FBTyxNQUFNLFVBQVUsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNuQyxDQUFDO1FBQ0QsY0FBYzthQUNULElBQUksVUFBVSxLQUFLLE1BQU0sSUFBSSxJQUFJLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7WUFDdkQsT0FBTyxNQUFNLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNqQyxDQUFDO2FBQU0sSUFBSSxVQUFVLEtBQUssUUFBUSxJQUFJLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUMzRCxPQUFPLE1BQU0sVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ25DLENBQUM7UUFDRCxrQkFBa0I7YUFDYixJQUFJLFVBQVUsS0FBSyxLQUFLLElBQUksSUFBSSxDQUFDLFFBQVEsQ0FBQyxXQUFXLENBQUMsSUFBSSxjQUFjLElBQUksY0FBYyxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ2pHLE9BQU8sTUFBTSxXQUFXLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDcEMsQ0FBQzthQUFNLElBQUksVUFBVSxLQUFLLE1BQU0sSUFBSSxJQUFJLENBQUMsUUFBUSxDQUFDLFdBQVcsQ0FBQyxJQUFJLGNBQWMsSUFBSSxjQUFjLENBQUMsRUFBRSxFQUFFLENBQUM7WUFDcEcsT0FBTyxNQUFNLGFBQWEsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUN0QyxDQUFDO2FBQU0sSUFBSSxVQUFVLEtBQUssS0FBSyxJQUFJLElBQUksQ0FBQyxVQUFVLENBQUMsWUFBWSxDQUFDLElBQUksY0FBYyxJQUFJLGNBQWMsQ0FBQyxFQUFFLEVBQUUsQ0FBQztZQUN0RyxPQUFPLE1BQU0sYUFBYSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ3RDLENBQUM7YUFBTSxJQUFJLFVBQVUsS0FBSyxRQUFRLElBQUksSUFBSSxDQUFDLFVBQVUsQ0FBQyxZQUFZLENBQUMsSUFBSSxjQUFjLElBQUksY0FBYyxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ3pHLE9BQU8sTUFBTSxhQUFhLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDdEMsQ0FBQzthQUFNLENBQUM7WUFDSixPQUFPLGNBQWMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxLQUFLLEVBQUUsV0FBVyxFQUFFLENBQUMsQ0FBQztRQUN2RCxDQUFDO0lBQ0wsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDYixPQUFPLENBQUMsS0FBSyxDQUFDLGdCQUFnQixFQUFFLEtBQUssQ0FBQyxDQUFDO1FBQ3ZDLE9BQU8sY0FBYyxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSx1QkFBdUIsRUFBRSxPQUFPLEVBQUcsS0FBZSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDdEcsQ0FBQztBQUNMLENBQUMsQ0FBQztBQTlDVyxRQUFBLE9BQU8sV0E4Q2xCIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRHluYW1vREJDbGllbnQgfSBmcm9tICdAYXdzLXNkay9jbGllbnQtZHluYW1vZGInO1xuaW1wb3J0IHsgRHluYW1vREJEb2N1bWVudENsaWVudCwgU2NhbkNvbW1hbmQsIEdldENvbW1hbmQsIFB1dENvbW1hbmQsIFVwZGF0ZUNvbW1hbmQsIERlbGV0ZUNvbW1hbmQsIFF1ZXJ5Q29tbWFuZCB9IGZyb20gJ0Bhd3Mtc2RrL2xpYi1keW5hbW9kYic7XG5pbXBvcnQgeyBTM0NsaWVudCB9IGZyb20gJ0Bhd3Mtc2RrL2NsaWVudC1zMyc7XG5pbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICd1dWlkJztcbmltcG9ydCB7IEFQSUdhdGV3YXlQcm94eUV2ZW50LCBBUElHYXRld2F5UHJveHlSZXN1bHQgfSBmcm9tICdhd3MtbGFtYmRhJztcblxuLy8gQ29uZmlndXJlIEFXUyBTREsgdjMgY2xpZW50c1xuY29uc3QgZHluYW1vQ2xpZW50ID0gbmV3IER5bmFtb0RCQ2xpZW50KHtcbiAgICByZWdpb246IHByb2Nlc3MuZW52LkFXU19SRUdJT04gfHwgJ3VzLWVhc3QtMSdcbn0pO1xuXG5jb25zdCBkeW5hbW9kYiA9IER5bmFtb0RCRG9jdW1lbnRDbGllbnQuZnJvbShkeW5hbW9DbGllbnQpO1xuY29uc3QgczMgPSBuZXcgUzNDbGllbnQoe1xuICAgIHJlZ2lvbjogcHJvY2Vzcy5lbnYuQVdTX1JFR0lPTiB8fCAndXMtZWFzdC0xJ1xufSk7XG5cbmNvbnN0IFBPU1RTX1RBQkxFID0gcHJvY2Vzcy5lbnYuUE9TVFNfVEFCTEU7XG5jb25zdCBNRURJQV9UQUJMRSA9IHByb2Nlc3MuZW52Lk1FRElBX1RBQkxFO1xuY29uc3QgQ09NTUVOVFNfVEFCTEUgPSBwcm9jZXNzLmVudi5DT01NRU5UU19UQUJMRTtcbmNvbnN0IExJS0VTX1RBQkxFID0gcHJvY2Vzcy5lbnYuTElLRVNfVEFCTEU7XG5jb25zdCBVU0VSU19UQUJMRSA9IHByb2Nlc3MuZW52LlVTRVJTX1RBQkxFO1xuY29uc3QgTUVESUFfQlVDS0VUID0gcHJvY2Vzcy5lbnYuTUVESUFfQlVDS0VUO1xuXG4vLyBUeXBlU2NyaXB0IGludGVyZmFjZXNcbmludGVyZmFjZSBQb3N0IHtcbiAgICBpZDogc3RyaW5nO1xuICAgIHRpdGxlPzogc3RyaW5nO1xuICAgIGNvbnRlbnQ6IHN0cmluZztcbiAgICBtZWRpYUlkPzogc3RyaW5nO1xuICAgIG1lZGlhX2lkPzogc3RyaW5nO1xuICAgIGF1dGhvcklkOiBzdHJpbmc7XG4gICAgdXNlcklkOiBzdHJpbmc7XG4gICAgbGlrZXM6IG51bWJlcjtcbiAgICBjb21tZW50czogbnVtYmVyO1xuICAgIHJlZmxleGVzOiBudW1iZXI7XG4gICAgc3RhdHVzOiAnZHJhZnQnIHwgJ3VwbG9hZGluZ19tZWRpYScgfCAncHVibGlzaGVkJztcbiAgICBhY3RpdmU6IGJvb2xlYW47XG4gICAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gICAgdXBkYXRlZEF0OiBzdHJpbmc7XG4gICAgbWVkaWE/OiBNZWRpYUl0ZW07XG4gICAgaXNMaWtlZEJ5Q3VycmVudFVzZXI/OiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgTWVkaWFJdGVtIHtcbiAgICBpZDogc3RyaW5nO1xuICAgIHN0YXR1czogc3RyaW5nO1xuICAgIFtrZXk6IHN0cmluZ106IGFueTtcbn1cblxuaW50ZXJmYWNlIENvbW1lbnQge1xuICAgIGlkOiBzdHJpbmc7XG4gICAgcG9zdF9pZDogc3RyaW5nO1xuICAgIHVzZXJfaWQ6IHN0cmluZztcbiAgICBjb250ZW50OiBzdHJpbmc7XG4gICAgbGlrZV9jb3VudDogbnVtYmVyO1xuICAgIGlzX2FjdGl2ZTogYm9vbGVhbjtcbiAgICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gICAgdXBkYXRlZF9hdDogc3RyaW5nO1xuICAgIHVzZXJuYW1lPzogc3RyaW5nO1xuICAgIGRpc3BsYXlOYW1lPzogc3RyaW5nO1xuICAgIGF2YXRhclVybD86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIFVzZXIge1xuICAgIGlkOiBzdHJpbmc7XG4gICAgdXNlcm5hbWU6IHN0cmluZztcbiAgICBkaXNwbGF5TmFtZTogc3RyaW5nO1xuICAgIGF2YXRhclVybD86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIExpa2Uge1xuICAgIHBvc3RJZDogc3RyaW5nO1xuICAgIHVzZXJJZDogc3RyaW5nO1xuICAgIGNyZWF0ZWRBdDogc3RyaW5nO1xufVxuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gY3JlYXRlIHJlc3BvbnNlXG5jb25zdCBjcmVhdGVSZXNwb25zZSA9IChzdGF0dXNDb2RlOiBudW1iZXIsIGJvZHk6IGFueSk6IEFQSUdhdGV3YXlQcm94eVJlc3VsdCA9PiAoe1xuICAgIHN0YXR1c0NvZGUsXG4gICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAnQWNjZXNzLUNvbnRyb2wtQWxsb3ctT3JpZ2luJzogJyonLFxuICAgICAgICAnQWNjZXNzLUNvbnRyb2wtQWxsb3ctSGVhZGVycyc6ICdDb250ZW50LVR5cGUsWC1BbXotRGF0ZSxBdXRob3JpemF0aW9uLFgtQXBpLUtleSxYLUFtei1TZWN1cml0eS1Ub2tlbicsXG4gICAgICAgICdBY2Nlc3MtQ29udHJvbC1BbGxvdy1NZXRob2RzJzogJ0dFVCxQT1NULFBVVCxERUxFVEUsT1BUSU9OUydcbiAgICB9LFxuICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGJvZHkpXG59KTtcblxuLy8gR2V0IGFsbCBwb3N0c1xuY29uc3QgZ2V0UG9zdHMgPSBhc3luYyAoZXZlbnQ6IEFQSUdhdGV3YXlQcm94eUV2ZW50KTogUHJvbWlzZTxBUElHYXRld2F5UHJveHlSZXN1bHQ+ID0+IHtcbiAgICB0cnkge1xuICAgICAgICAvLyBHZXQgY3VycmVudCB1c2VyIElEIGZyb20gYXV0aG9yaXplciBjb250ZXh0XG4gICAgICAgIGNvbnN0IGN1cnJlbnRVc2VySWQgPSBnZXRVc2VySWRGcm9tQ29udGV4dChldmVudCk7XG5cbiAgICAgICAgY29uc3Qgc2NhbkNvbW1hbmQgPSBuZXcgU2NhbkNvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBQT1NUU19UQUJMRVxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZHluYW1vZGIuc2VuZChzY2FuQ29tbWFuZCk7XG5cbiAgICAgICAgLy8gRmlsdGVyIG91dCBpbmFjdGl2ZSBwb3N0cyBhbmQgcG9zdHMgdGhhdCBhcmUgbm90IHB1Ymxpc2hlZFxuICAgICAgICBjb25zdCBhY3RpdmVQb3N0cyA9IChyZXN1bHQuSXRlbXMgfHwgW10pLmZpbHRlcigocG9zdDogYW55KSA9PlxuICAgICAgICAgICAgcG9zdC5hY3RpdmUgPT09IHRydWUgJiYgcG9zdC5zdGF0dXMgPT09ICdwdWJsaXNoZWQnXG4gICAgICAgICk7XG5cbiAgICAgICAgLy8gU29ydCBwb3N0cyBieSBjcmVhdGVkQXQgZGVzY2VuZGluZ1xuICAgICAgICBjb25zdCBwb3N0cyA9IGFjdGl2ZVBvc3RzLnNvcnQoKGE6IGFueSwgYjogYW55KSA9PiBuZXcgRGF0ZShiLmNyZWF0ZWRBdCkuZ2V0VGltZSgpIC0gbmV3IERhdGUoYS5jcmVhdGVkQXQpLmdldFRpbWUoKSk7XG5cbiAgICAgICAgLy8gRmV0Y2ggbWVkaWEgZGF0YSBhbmQgbGlrZSBzdGF0dXMgZm9yIHBvc3RzXG4gICAgICAgIGNvbnN0IHBvc3RzV2l0aE1lZGlhQW5kTGlrZXMgPSBhd2FpdCBQcm9taXNlLmFsbChwb3N0cy5tYXAoYXN5bmMgKHBvc3Q6IGFueSkgPT4ge1xuICAgICAgICAgICAgLy8gRmV0Y2ggbWVkaWEgZGF0YSBpZiBwb3N0IGhhcyBtZWRpYUlkIG9yIG1lZGlhX2lkXG4gICAgICAgICAgICBjb25zdCBtZWRpYUlkID0gcG9zdC5tZWRpYUlkIHx8IHBvc3QubWVkaWFfaWQ7XG4gICAgICAgICAgICBpZiAobWVkaWFJZCkge1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGdldE1lZGlhQ29tbWFuZCA9IG5ldyBHZXRDb21tYW5kKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIFRhYmxlTmFtZTogTUVESUFfVEFCTEUsXG4gICAgICAgICAgICAgICAgICAgICAgICBLZXk6IHsgaWQ6IG1lZGlhSWQgfVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWVkaWFSZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKGdldE1lZGlhQ29tbWFuZCk7XG5cbiAgICAgICAgICAgICAgICAgICAgaWYgKG1lZGlhUmVzdWx0Lkl0ZW0pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHBvc3QubWVkaWEgPSBtZWRpYVJlc3VsdC5JdGVtO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihgRmFpbGVkIHRvIGZldGNoIG1lZGlhIGZvciBwb3N0ICR7cG9zdC5pZH06YCwgZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICAvLyBDb250aW51ZSB3aXRob3V0IG1lZGlhIGRhdGEgaWYgZmV0Y2ggZmFpbHNcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIENoZWNrIGlmIGN1cnJlbnQgdXNlciBoYXMgbGlrZWQgdGhpcyBwb3N0XG4gICAgICAgICAgICBsZXQgaXNMaWtlZEJ5Q3VycmVudFVzZXIgPSBmYWxzZTtcbiAgICAgICAgICAgIGlmIChjdXJyZW50VXNlcklkKSB7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZ2V0TGlrZUNvbW1hbmQgPSBuZXcgR2V0Q29tbWFuZCh7XG4gICAgICAgICAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IExJS0VTX1RBQkxFLFxuICAgICAgICAgICAgICAgICAgICAgICAgS2V5OiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zdElkOiBwb3N0LmlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVzZXJJZDogY3VycmVudFVzZXJJZFxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbGlrZVJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoZ2V0TGlrZUNvbW1hbmQpO1xuXG4gICAgICAgICAgICAgICAgICAgIGlzTGlrZWRCeUN1cnJlbnRVc2VyID0gISFsaWtlUmVzdWx0Lkl0ZW07XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihgRmFpbGVkIHRvIGNoZWNrIGxpa2Ugc3RhdHVzIGZvciBwb3N0ICR7cG9zdC5pZH06YCwgZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICAvLyBDb250aW51ZSB3aXRoIGZhbHNlIGlmIGNoZWNrIGZhaWxzXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBBZGQgdGhlIGxpa2Ugc3RhdHVzIHRvIHRoZSBwb3N0XG4gICAgICAgICAgICBwb3N0LmlzTGlrZWRCeUN1cnJlbnRVc2VyID0gaXNMaWtlZEJ5Q3VycmVudFVzZXI7XG5cbiAgICAgICAgICAgIHJldHVybiBwb3N0O1xuICAgICAgICB9KSk7XG5cbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDIwMCwge1xuICAgICAgICAgICAgcG9zdHM6IHBvc3RzV2l0aE1lZGlhQW5kTGlrZXMsXG4gICAgICAgICAgICBjb3VudDogcG9zdHNXaXRoTWVkaWFBbmRMaWtlcy5sZW5ndGhcbiAgICAgICAgfSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdHZXRQb3N0cyBlcnJvcjonLCBlcnJvcik7XG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg1MDAsIHsgZXJyb3I6ICdGYWlsZWQgdG8gZ2V0IHBvc3RzJywgZGV0YWlsczogKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlIH0pO1xuICAgIH1cbn07XG5cbi8vIEdldCBzaW5nbGUgcG9zdFxuY29uc3QgZ2V0UG9zdCA9IGFzeW5jIChldmVudDogQVBJR2F0ZXdheVByb3h5RXZlbnQpOiBQcm9taXNlPEFQSUdhdGV3YXlQcm94eVJlc3VsdD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHsgaWQgfSA9IGV2ZW50LnBhdGhQYXJhbWV0ZXJzIHx8IHt9O1xuXG4gICAgICAgIGlmICghaWQpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdQb3N0IElEIGlzIHJlcXVpcmVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEdldCBjdXJyZW50IHVzZXIgSUQgZnJvbSBhdXRob3JpemVyIGNvbnRleHRcbiAgICAgICAgY29uc3QgY3VycmVudFVzZXJJZCA9IGdldFVzZXJJZEZyb21Db250ZXh0KGV2ZW50KTtcblxuICAgICAgICBjb25zdCBnZXRQb3N0Q29tbWFuZCA9IG5ldyBHZXRDb21tYW5kKHtcbiAgICAgICAgICAgIFRhYmxlTmFtZTogUE9TVFNfVEFCTEUsXG4gICAgICAgICAgICBLZXk6IHsgaWQgfVxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZHluYW1vZGIuc2VuZChnZXRQb3N0Q29tbWFuZCk7XG5cbiAgICAgICAgaWYgKCFyZXN1bHQuSXRlbSkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwNCwgeyBlcnJvcjogJ1Bvc3Qgbm90IGZvdW5kJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHBvc3QgPSByZXN1bHQuSXRlbSBhcyBhbnk7XG5cbiAgICAgICAgLy8gRmV0Y2ggbWVkaWEgZGF0YSBpZiBwb3N0IGhhcyBtZWRpYV9pZFxuICAgICAgICBpZiAocG9zdC5tZWRpYV9pZCkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBnZXRNZWRpYUNvbW1hbmQgPSBuZXcgR2V0Q29tbWFuZCh7XG4gICAgICAgICAgICAgICAgICAgIFRhYmxlTmFtZTogTUVESUFfVEFCTEUsXG4gICAgICAgICAgICAgICAgICAgIEtleTogeyBpZDogcG9zdC5tZWRpYV9pZCB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29uc3QgbWVkaWFSZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKGdldE1lZGlhQ29tbWFuZCk7XG5cbiAgICAgICAgICAgICAgICBpZiAobWVkaWFSZXN1bHQuSXRlbSkge1xuICAgICAgICAgICAgICAgICAgICBwb3N0Lm1lZGlhID0gbWVkaWFSZXN1bHQuSXRlbTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byBmZXRjaCBtZWRpYSBmb3IgcG9zdCAke3Bvc3QuaWR9OmAsIGVycm9yKTtcbiAgICAgICAgICAgICAgICAvLyBDb250aW51ZSB3aXRob3V0IG1lZGlhIGRhdGEgaWYgZmV0Y2ggZmFpbHNcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIENoZWNrIGlmIGN1cnJlbnQgdXNlciBoYXMgbGlrZWQgdGhpcyBwb3N0XG4gICAgICAgIGxldCBpc0xpa2VkQnlDdXJyZW50VXNlciA9IGZhbHNlO1xuICAgICAgICBpZiAoY3VycmVudFVzZXJJZCkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBnZXRMaWtlQ29tbWFuZCA9IG5ldyBHZXRDb21tYW5kKHtcbiAgICAgICAgICAgICAgICAgICAgVGFibGVOYW1lOiBMSUtFU19UQUJMRSxcbiAgICAgICAgICAgICAgICAgICAgS2V5OiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBwb3N0SWQ6IHBvc3QuaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICB1c2VySWQ6IGN1cnJlbnRVc2VySWRcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IGxpa2VSZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKGdldExpa2VDb21tYW5kKTtcblxuICAgICAgICAgICAgICAgIGlzTGlrZWRCeUN1cnJlbnRVc2VyID0gISFsaWtlUmVzdWx0Lkl0ZW07XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byBjaGVjayBsaWtlIHN0YXR1cyBmb3IgcG9zdCAke3Bvc3QuaWR9OmAsIGVycm9yKTtcbiAgICAgICAgICAgICAgICAvLyBDb250aW51ZSB3aXRoIGZhbHNlIGlmIGNoZWNrIGZhaWxzXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBBZGQgdGhlIGxpa2Ugc3RhdHVzIHRvIHRoZSBwb3N0XG4gICAgICAgIHBvc3QuaXNMaWtlZEJ5Q3VycmVudFVzZXIgPSBpc0xpa2VkQnlDdXJyZW50VXNlcjtcblxuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoMjAwLCB7IHBvc3QgfSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdHZXRQb3N0IGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDUwMCwgeyBlcnJvcjogJ0ZhaWxlZCB0byBnZXQgcG9zdCcsIGRldGFpbHM6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9KTtcbiAgICB9XG59O1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gZ2V0IHVzZXIgSUQgZnJvbSBhdXRob3JpemVyIGNvbnRleHRcbmNvbnN0IGdldFVzZXJJZEZyb21Db250ZXh0ID0gKGV2ZW50OiBBUElHYXRld2F5UHJveHlFdmVudCk6IHN0cmluZyB8IG51bGwgPT4ge1xuICAgIC8vIFdoZW4gdXNpbmcgTGFtYmRhIGF1dGhvcml6ZXIsIHVzZXIgaW5mbyBpcyBhdmFpbGFibGUgaW4gdGhlIGNvbnRleHRcbiAgICBpZiAoZXZlbnQucmVxdWVzdENvbnRleHQgJiYgZXZlbnQucmVxdWVzdENvbnRleHQuYXV0aG9yaXplcikge1xuICAgICAgICByZXR1cm4gKGV2ZW50LnJlcXVlc3RDb250ZXh0LmF1dGhvcml6ZXIgYXMgYW55KS51c2VySWQ7XG4gICAgfVxuICAgIHJldHVybiBudWxsO1xufTtcblxuLy8gQ3JlYXRlIGRyYWZ0IHBvc3QgKGZpcnN0IHN0ZXAgb2YgbXVsdGktc3RlcCBjcmVhdGlvbilcbmNvbnN0IGNyZWF0ZURyYWZ0UG9zdCA9IGFzeW5jIChldmVudDogQVBJR2F0ZXdheVByb3h5RXZlbnQpOiBQcm9taXNlPEFQSUdhdGV3YXlQcm94eVJlc3VsdD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAgIGlmICghZXZlbnQuYm9keSkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ1JlcXVlc3QgYm9keSBpcyByZXF1aXJlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCB7IHRpdGxlLCBjb250ZW50IH0gPSBKU09OLnBhcnNlKGV2ZW50LmJvZHkpO1xuXG4gICAgICAgIC8vIEdldCB1c2VyIElEIGZyb20gYXV0aG9yaXplciBjb250ZXh0XG4gICAgICAgIGNvbnN0IGF1dGhvcklkID0gZ2V0VXNlcklkRnJvbUNvbnRleHQoZXZlbnQpO1xuXG4gICAgICAgIGlmICghY29udGVudCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ0NvbnRlbnQgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCFhdXRob3JJZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMSwgeyBlcnJvcjogJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgcG9zdElkID0gdXVpZHY0KCk7XG4gICAgICAgIGNvbnN0IHBvc3Q6IGFueSA9IHtcbiAgICAgICAgICAgIGlkOiBwb3N0SWQsXG4gICAgICAgICAgICB0aXRsZTogdGl0bGUgfHwgbnVsbCxcbiAgICAgICAgICAgIGNvbnRlbnQsXG4gICAgICAgICAgICBtZWRpYUlkOiBudWxsLCAvLyBXaWxsIGJlIHNldCBsYXRlciBpZiBtZWRpYSBpcyB1cGxvYWRlZFxuICAgICAgICAgICAgYXV0aG9ySWQ6IGF1dGhvcklkLFxuICAgICAgICAgICAgdXNlcklkOiBhdXRob3JJZCwgLy8gS2VlcCBmb3IgYmFja3dhcmRzIGNvbXBhdGliaWxpdHlcbiAgICAgICAgICAgIGxpa2VzOiAwLFxuICAgICAgICAgICAgY29tbWVudHM6IDAsXG4gICAgICAgICAgICByZWZsZXhlczogMCxcbiAgICAgICAgICAgIHN0YXR1czogJ2RyYWZ0JywgLy8gU3RhdHVzOiBkcmFmdCwgdXBsb2FkaW5nX21lZGlhLCBwdWJsaXNoZWRcbiAgICAgICAgICAgIGFjdGl2ZTogZmFsc2UsIC8vIE5vdCBhY3RpdmUgdW50aWwgcHVibGlzaGVkXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc3QgcHV0Q29tbWFuZCA9IG5ldyBQdXRDb21tYW5kKHtcbiAgICAgICAgICAgIFRhYmxlTmFtZTogUE9TVFNfVEFCTEUsXG4gICAgICAgICAgICBJdGVtOiBwb3N0XG4gICAgICAgIH0pO1xuICAgICAgICBhd2FpdCBkeW5hbW9kYi5zZW5kKHB1dENvbW1hbmQpO1xuXG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSgyMDEsIHtcbiAgICAgICAgICAgIG1lc3NhZ2U6ICdEcmFmdCBwb3N0IGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5JyxcbiAgICAgICAgICAgIHBvc3RcbiAgICAgICAgfSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdDcmVhdGVEcmFmdFBvc3QgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNTAwLCB7IGVycm9yOiAnRmFpbGVkIHRvIGNyZWF0ZSBkcmFmdCBwb3N0JywgZGV0YWlsczogKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlIH0pO1xuICAgIH1cbn07XG5cbi8vIENyZWF0ZSBwb3N0IChsZWdhY3kgZW5kcG9pbnQgLSBub3cgY3JlYXRlcyBhbmQgcHVibGlzaGVzIGltbWVkaWF0ZWx5KVxuY29uc3QgY3JlYXRlUG9zdCA9IGFzeW5jIChldmVudDogQVBJR2F0ZXdheVByb3h5RXZlbnQpOiBQcm9taXNlPEFQSUdhdGV3YXlQcm94eVJlc3VsdD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAgIGlmICghZXZlbnQuYm9keSkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ1JlcXVlc3QgYm9keSBpcyByZXF1aXJlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCB7IHRpdGxlLCBjb250ZW50LCBtZWRpYV9pZCwgbWVkaWFJZCB9ID0gSlNPTi5wYXJzZShldmVudC5ib2R5KTtcblxuICAgICAgICAvLyBHZXQgdXNlciBJRCBmcm9tIGF1dGhvcml6ZXIgY29udGV4dFxuICAgICAgICBjb25zdCBhdXRob3JJZCA9IGdldFVzZXJJZEZyb21Db250ZXh0KGV2ZW50KTtcblxuICAgICAgICBpZiAoIWNvbnRlbnQpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdDb250ZW50IGlzIHJlcXVpcmVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICghYXV0aG9ySWQpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDEsIHsgZXJyb3I6ICdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFN1cHBvcnQgYm90aCBtZWRpYV9pZCBhbmQgbWVkaWFJZCBmaWVsZCBuYW1lc1xuICAgICAgICBjb25zdCBmaW5hbE1lZGlhSWQgPSBtZWRpYV9pZCB8fCBtZWRpYUlkO1xuXG4gICAgICAgIC8vIElmIG1lZGlhX2lkIGlzIHByb3ZpZGVkLCB2ZXJpZnkgaXQgZXhpc3RzXG4gICAgICAgIGlmIChmaW5hbE1lZGlhSWQpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZ2V0TWVkaWFDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IE1FRElBX1RBQkxFLFxuICAgICAgICAgICAgICAgICAgICBLZXk6IHsgaWQ6IGZpbmFsTWVkaWFJZCB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29uc3QgbWVkaWFSZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKGdldE1lZGlhQ29tbWFuZCk7XG5cbiAgICAgICAgICAgICAgICBpZiAoIW1lZGlhUmVzdWx0Lkl0ZW0pIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ0ludmFsaWQgbWVkaWFfaWQ6IG1lZGlhIG5vdCBmb3VuZCcgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB2ZXJpZnlpbmcgbWVkaWE6JywgZXJyb3IpO1xuICAgICAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdGYWlsZWQgdG8gdmVyaWZ5IG1lZGlhX2lkJyB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHBvc3RJZCA9IHV1aWR2NCgpO1xuICAgICAgICBjb25zdCBwb3N0OiBhbnkgPSB7XG4gICAgICAgICAgICBpZDogcG9zdElkLFxuICAgICAgICAgICAgdGl0bGU6IHRpdGxlIHx8IG51bGwsXG4gICAgICAgICAgICBjb250ZW50LFxuICAgICAgICAgICAgbWVkaWFJZDogZmluYWxNZWRpYUlkIHx8IG51bGwsXG4gICAgICAgICAgICBhdXRob3JJZDogYXV0aG9ySWQsXG4gICAgICAgICAgICB1c2VySWQ6IGF1dGhvcklkLCAvLyBLZWVwIGZvciBiYWNrd2FyZHMgY29tcGF0aWJpbGl0eVxuICAgICAgICAgICAgbGlrZXM6IDAsXG4gICAgICAgICAgICBjb21tZW50czogMCxcbiAgICAgICAgICAgIHJlZmxleGVzOiAwLFxuICAgICAgICAgICAgc3RhdHVzOiAncHVibGlzaGVkJywgLy8gU3RhdHVzOiBkcmFmdCwgdXBsb2FkaW5nX21lZGlhLCBwdWJsaXNoZWRcbiAgICAgICAgICAgIGFjdGl2ZTogdHJ1ZSwgLy8gRmluYWwgc3dpdGNoIHRvIHNob3cgaW4gZmVlZHNcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgfTtcblxuICAgICAgICBjb25zdCBwdXRDb21tYW5kID0gbmV3IFB1dENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBQT1NUU19UQUJMRSxcbiAgICAgICAgICAgIEl0ZW06IHBvc3RcbiAgICAgICAgfSk7XG4gICAgICAgIGF3YWl0IGR5bmFtb2RiLnNlbmQocHV0Q29tbWFuZCk7XG5cbiAgICAgICAgLy8gRmV0Y2ggbWVkaWEgZGF0YSBpZiBtZWRpYV9pZCB3YXMgcHJvdmlkZWRcbiAgICAgICAgaWYgKGZpbmFsTWVkaWFJZCkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBnZXRNZWRpYUNvbW1hbmQgPSBuZXcgR2V0Q29tbWFuZCh7XG4gICAgICAgICAgICAgICAgICAgIFRhYmxlTmFtZTogTUVESUFfVEFCTEUsXG4gICAgICAgICAgICAgICAgICAgIEtleTogeyBpZDogZmluYWxNZWRpYUlkIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCBtZWRpYVJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoZ2V0TWVkaWFDb21tYW5kKTtcblxuICAgICAgICAgICAgICAgIGlmIChtZWRpYVJlc3VsdC5JdGVtKSB7XG4gICAgICAgICAgICAgICAgICAgIHBvc3QubWVkaWEgPSBtZWRpYVJlc3VsdC5JdGVtO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgbWVkaWEgZm9yIHJlc3BvbnNlOicsIGVycm9yKTtcbiAgICAgICAgICAgICAgICAvLyBDb250aW51ZSB3aXRob3V0IG1lZGlhIGRhdGEgaW4gcmVzcG9uc2VcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSgyMDEsIHtcbiAgICAgICAgICAgIG1lc3NhZ2U6ICdQb3N0IGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5JyxcbiAgICAgICAgICAgIHBvc3RcbiAgICAgICAgfSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdDcmVhdGVQb3N0IGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDUwMCwgeyBlcnJvcjogJ0ZhaWxlZCB0byBjcmVhdGUgcG9zdCcsIGRldGFpbHM6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9KTtcbiAgICB9XG59O1xuXG4vLyBVcGRhdGUgcG9zdFxuY29uc3QgdXBkYXRlUG9zdCA9IGFzeW5jIChldmVudDogQVBJR2F0ZXdheVByb3h5RXZlbnQpOiBQcm9taXNlPEFQSUdhdGV3YXlQcm94eVJlc3VsdD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHsgaWQgfSA9IGV2ZW50LnBhdGhQYXJhbWV0ZXJzIHx8IHt9O1xuXG4gICAgICAgIGlmICghaWQpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdQb3N0IElEIGlzIHJlcXVpcmVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICghZXZlbnQuYm9keSkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ1JlcXVlc3QgYm9keSBpcyByZXF1aXJlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCB7IHRpdGxlLCBjb250ZW50LCBtZWRpYV9pZCB9ID0gSlNPTi5wYXJzZShldmVudC5ib2R5KTtcblxuICAgICAgICAvLyBJZiBtZWRpYV9pZCBpcyBwcm92aWRlZCwgdmVyaWZ5IGl0IGV4aXN0c1xuICAgICAgICBpZiAobWVkaWFfaWQpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZ2V0TWVkaWFDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IE1FRElBX1RBQkxFLFxuICAgICAgICAgICAgICAgICAgICBLZXk6IHsgaWQ6IG1lZGlhX2lkIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCBtZWRpYVJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoZ2V0TWVkaWFDb21tYW5kKTtcblxuICAgICAgICAgICAgICAgIGlmICghbWVkaWFSZXN1bHQuSXRlbSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAwLCB7IGVycm9yOiAnSW52YWxpZCBtZWRpYV9pZDogbWVkaWEgbm90IGZvdW5kJyB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHZlcmlmeWluZyBtZWRpYTonLCBlcnJvcik7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ0ZhaWxlZCB0byB2ZXJpZnkgbWVkaWFfaWQnIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgdXBkYXRlRXhwcmVzc2lvbjogc3RyaW5nW10gPSBbXTtcbiAgICAgICAgY29uc3QgZXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlczogUmVjb3JkPHN0cmluZywgYW55PiA9IHt9O1xuICAgICAgICBjb25zdCBleHByZXNzaW9uQXR0cmlidXRlTmFtZXM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7fTtcblxuICAgICAgICBpZiAodGl0bGUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdXBkYXRlRXhwcmVzc2lvbi5wdXNoKCcjdGl0bGUgPSA6dGl0bGUnKTtcbiAgICAgICAgICAgIGV4cHJlc3Npb25BdHRyaWJ1dGVOYW1lc1snI3RpdGxlJ10gPSAndGl0bGUnO1xuICAgICAgICAgICAgZXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlc1snOnRpdGxlJ10gPSB0aXRsZTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChjb250ZW50ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHVwZGF0ZUV4cHJlc3Npb24ucHVzaCgnI2NvbnRlbnQgPSA6Y29udGVudCcpO1xuICAgICAgICAgICAgZXhwcmVzc2lvbkF0dHJpYnV0ZU5hbWVzWycjY29udGVudCddID0gJ2NvbnRlbnQnO1xuICAgICAgICAgICAgZXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlc1snOmNvbnRlbnQnXSA9IGNvbnRlbnQ7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAobWVkaWFfaWQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdXBkYXRlRXhwcmVzc2lvbi5wdXNoKCdtZWRpYV9pZCA9IDptZWRpYV9pZCcpO1xuICAgICAgICAgICAgZXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlc1snOm1lZGlhX2lkJ10gPSBtZWRpYV9pZDtcbiAgICAgICAgfVxuXG4gICAgICAgIHVwZGF0ZUV4cHJlc3Npb24ucHVzaCgndXBkYXRlZF9hdCA9IDp1cGRhdGVkX2F0Jyk7XG4gICAgICAgIGV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXNbJzp1cGRhdGVkX2F0J10gPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCk7XG5cbiAgICAgICAgY29uc3QgdXBkYXRlQ29tbWFuZCA9IG5ldyBVcGRhdGVDb21tYW5kKHtcbiAgICAgICAgICAgIFRhYmxlTmFtZTogUE9TVFNfVEFCTEUsXG4gICAgICAgICAgICBLZXk6IHsgaWQgfSxcbiAgICAgICAgICAgIFVwZGF0ZUV4cHJlc3Npb246IGBTRVQgJHt1cGRhdGVFeHByZXNzaW9uLmpvaW4oJywgJyl9YCxcbiAgICAgICAgICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVOYW1lczogZXhwcmVzc2lvbkF0dHJpYnV0ZU5hbWVzLFxuICAgICAgICAgICAgRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlczogZXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlcyxcbiAgICAgICAgICAgIFJldHVyblZhbHVlczogJ0FMTF9ORVcnXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKHVwZGF0ZUNvbW1hbmQpO1xuXG4gICAgICAgIGNvbnN0IHVwZGF0ZWRQb3N0ID0gcmVzdWx0LkF0dHJpYnV0ZXMgYXMgYW55O1xuXG4gICAgICAgIC8vIEZldGNoIG1lZGlhIGRhdGEgaWYgcG9zdCBoYXMgbWVkaWFfaWRcbiAgICAgICAgaWYgKHVwZGF0ZWRQb3N0Lm1lZGlhX2lkKSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGdldE1lZGlhQ29tbWFuZCA9IG5ldyBHZXRDb21tYW5kKHtcbiAgICAgICAgICAgICAgICAgICAgVGFibGVOYW1lOiBNRURJQV9UQUJMRSxcbiAgICAgICAgICAgICAgICAgICAgS2V5OiB7IGlkOiB1cGRhdGVkUG9zdC5tZWRpYV9pZCB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29uc3QgbWVkaWFSZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKGdldE1lZGlhQ29tbWFuZCk7XG5cbiAgICAgICAgICAgICAgICBpZiAobWVkaWFSZXN1bHQuSXRlbSkge1xuICAgICAgICAgICAgICAgICAgICB1cGRhdGVkUG9zdC5tZWRpYSA9IG1lZGlhUmVzdWx0Lkl0ZW07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBtZWRpYSBmb3IgcmVzcG9uc2U6JywgZXJyb3IpO1xuICAgICAgICAgICAgICAgIC8vIENvbnRpbnVlIHdpdGhvdXQgbWVkaWEgZGF0YSBpbiByZXNwb25zZVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDIwMCwge1xuICAgICAgICAgICAgbWVzc2FnZTogJ1Bvc3QgdXBkYXRlZCBzdWNjZXNzZnVsbHknLFxuICAgICAgICAgICAgcG9zdDogdXBkYXRlZFBvc3RcbiAgICAgICAgfSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdVcGRhdGVQb3N0IGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDUwMCwgeyBlcnJvcjogJ0ZhaWxlZCB0byB1cGRhdGUgcG9zdCcsIGRldGFpbHM6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9KTtcbiAgICB9XG59O1xuXG4vLyBBdHRhY2ggbWVkaWEgdG8gZHJhZnQgcG9zdFxuY29uc3QgYXR0YWNoTWVkaWFUb1Bvc3QgPSBhc3luYyAoZXZlbnQ6IEFQSUdhdGV3YXlQcm94eUV2ZW50KTogUHJvbWlzZTxBUElHYXRld2F5UHJveHlSZXN1bHQ+ID0+IHtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCB7IGlkIH0gPSBldmVudC5wYXRoUGFyYW1ldGVycyB8fCB7fTtcblxuICAgICAgICBpZiAoIWlkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAwLCB7IGVycm9yOiAnUG9zdCBJRCBpcyByZXF1aXJlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIWV2ZW50LmJvZHkpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdSZXF1ZXN0IGJvZHkgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgeyBtZWRpYV9pZCB9ID0gSlNPTi5wYXJzZShldmVudC5ib2R5KTtcblxuICAgICAgICAvLyBHZXQgdXNlciBJRCBmcm9tIGF1dGhvcml6ZXIgY29udGV4dFxuICAgICAgICBjb25zdCBhdXRob3JJZCA9IGdldFVzZXJJZEZyb21Db250ZXh0KGV2ZW50KTtcblxuICAgICAgICBpZiAoIW1lZGlhX2lkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAwLCB7IGVycm9yOiAnbWVkaWFfaWQgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCFhdXRob3JJZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMSwgeyBlcnJvcjogJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gVmVyaWZ5IG1lZGlhIGV4aXN0cyBhbmQgaXMgdXBsb2FkZWRcbiAgICAgICAgY29uc3QgZ2V0TWVkaWFDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBNRURJQV9UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBpZDogbWVkaWFfaWQgfVxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgbWVkaWFSZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKGdldE1lZGlhQ29tbWFuZCk7XG5cbiAgICAgICAgaWYgKCFtZWRpYVJlc3VsdC5JdGVtKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAwLCB7IGVycm9yOiAnSW52YWxpZCBtZWRpYV9pZDogbWVkaWEgbm90IGZvdW5kJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChtZWRpYVJlc3VsdC5JdGVtLnN0YXR1cyAhPT0gJ3VwbG9hZGVkJykge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ01lZGlhIHVwbG9hZCBub3QgY29tcGxldGVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEdldCB0aGUgcG9zdCBhbmQgdmVyaWZ5IG93bmVyc2hpcFxuICAgICAgICBjb25zdCBnZXRQb3N0Q29tbWFuZCA9IG5ldyBHZXRDb21tYW5kKHtcbiAgICAgICAgICAgIFRhYmxlTmFtZTogUE9TVFNfVEFCTEUsXG4gICAgICAgICAgICBLZXk6IHsgaWQgfVxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgcG9zdFJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoZ2V0UG9zdENvbW1hbmQpO1xuXG4gICAgICAgIGlmICghcG9zdFJlc3VsdC5JdGVtKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDA0LCB7IGVycm9yOiAnUG9zdCBub3QgZm91bmQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKChwb3N0UmVzdWx0Lkl0ZW0gYXMgYW55KS5hdXRob3JJZCAhPT0gYXV0aG9ySWQpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDMsIHsgZXJyb3I6ICdOb3QgYXV0aG9yaXplZCB0byBtb2RpZnkgdGhpcyBwb3N0JyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFVwZGF0ZSBwb3N0IHdpdGggbWVkaWEgYW5kIGNoYW5nZSBzdGF0dXMgdG8gdXBsb2FkaW5nX21lZGlhXG4gICAgICAgIGNvbnN0IHVwZGF0ZUNvbW1hbmQgPSBuZXcgVXBkYXRlQ29tbWFuZCh7XG4gICAgICAgICAgICBUYWJsZU5hbWU6IFBPU1RTX1RBQkxFLFxuICAgICAgICAgICAgS2V5OiB7IGlkIH0sXG4gICAgICAgICAgICBVcGRhdGVFeHByZXNzaW9uOiAnU0VUIG1lZGlhX2lkID0gOm1lZGlhX2lkLCAjc3RhdHVzID0gOnN0YXR1cywgdXBkYXRlZF9hdCA9IDp1cGRhdGVkX2F0JyxcbiAgICAgICAgICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVOYW1lczoge1xuICAgICAgICAgICAgICAgICcjc3RhdHVzJzogJ3N0YXR1cydcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzOiB7XG4gICAgICAgICAgICAgICAgJzptZWRpYV9pZCc6IG1lZGlhX2lkLFxuICAgICAgICAgICAgICAgICc6c3RhdHVzJzogJ3VwbG9hZGluZ19tZWRpYScsXG4gICAgICAgICAgICAgICAgJzp1cGRhdGVkX2F0JzogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgUmV0dXJuVmFsdWVzOiAnQUxMX05FVydcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQodXBkYXRlQ29tbWFuZCk7XG5cbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDIwMCwge1xuICAgICAgICAgICAgbWVzc2FnZTogJ01lZGlhIGF0dGFjaGVkIHRvIHBvc3Qgc3VjY2Vzc2Z1bGx5JyxcbiAgICAgICAgICAgIHBvc3Q6IHJlc3VsdC5BdHRyaWJ1dGVzXG4gICAgICAgIH0pO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignQXR0YWNoTWVkaWFUb1Bvc3QgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNTAwLCB7IGVycm9yOiAnRmFpbGVkIHRvIGF0dGFjaCBtZWRpYSB0byBwb3N0JywgZGV0YWlsczogKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlIH0pO1xuICAgIH1cbn07XG5cbi8vIFB1Ymxpc2ggcG9zdCAoZmluYWwgc3RlcClcbmNvbnN0IHB1Ymxpc2hQb3N0ID0gYXN5bmMgKGV2ZW50OiBBUElHYXRld2F5UHJveHlFdmVudCk6IFByb21pc2U8QVBJR2F0ZXdheVByb3h5UmVzdWx0PiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgeyBpZCB9ID0gZXZlbnQucGF0aFBhcmFtZXRlcnMgfHwge307XG5cbiAgICAgICAgaWYgKCFpZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ1Bvc3QgSUQgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gR2V0IHVzZXIgSUQgZnJvbSBhdXRob3JpemVyIGNvbnRleHRcbiAgICAgICAgY29uc3QgYXV0aG9ySWQgPSBnZXRVc2VySWRGcm9tQ29udGV4dChldmVudCk7XG5cbiAgICAgICAgaWYgKCFhdXRob3JJZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMSwgeyBlcnJvcjogJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gR2V0IHRoZSBwb3N0IGFuZCB2ZXJpZnkgb3duZXJzaGlwXG4gICAgICAgIGNvbnN0IGdldFBvc3RDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBQT1NUU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBpZCB9XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBwb3N0UmVzdWx0ID0gYXdhaXQgZHluYW1vZGIuc2VuZChnZXRQb3N0Q29tbWFuZCk7XG5cbiAgICAgICAgaWYgKCFwb3N0UmVzdWx0Lkl0ZW0pIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDQsIHsgZXJyb3I6ICdQb3N0IG5vdCBmb3VuZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoKHBvc3RSZXN1bHQuSXRlbSBhcyBhbnkpLmF1dGhvcklkICE9PSBhdXRob3JJZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMywgeyBlcnJvcjogJ05vdCBhdXRob3JpemVkIHRvIG1vZGlmeSB0aGlzIHBvc3QnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gSWYgcG9zdCBoYXMgbWVkaWEsIHZlcmlmeSBpdCdzIHVwbG9hZGVkXG4gICAgICAgIGlmICgocG9zdFJlc3VsdC5JdGVtIGFzIGFueSkubWVkaWFfaWQpIHtcbiAgICAgICAgICAgIGNvbnN0IGdldE1lZGlhQ29tbWFuZCA9IG5ldyBHZXRDb21tYW5kKHtcbiAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IE1FRElBX1RBQkxFLFxuICAgICAgICAgICAgICAgIEtleTogeyBpZDogKHBvc3RSZXN1bHQuSXRlbSBhcyBhbnkpLm1lZGlhX2lkIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc3QgbWVkaWFSZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKGdldE1lZGlhQ29tbWFuZCk7XG5cbiAgICAgICAgICAgIGlmICghbWVkaWFSZXN1bHQuSXRlbSB8fCBtZWRpYVJlc3VsdC5JdGVtLnN0YXR1cyAhPT0gJ3VwbG9hZGVkJykge1xuICAgICAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdNZWRpYSB1cGxvYWQgbm90IGNvbXBsZXRlZCcgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBVcGRhdGUgcG9zdCB0byBwdWJsaXNoZWQgYW5kIGFjdGl2ZVxuICAgICAgICBjb25zdCB1cGRhdGVDb21tYW5kID0gbmV3IFVwZGF0ZUNvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBQT1NUU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBpZCB9LFxuICAgICAgICAgICAgVXBkYXRlRXhwcmVzc2lvbjogJ1NFVCAjc3RhdHVzID0gOnN0YXR1cywgYWN0aXZlID0gOmFjdGl2ZSwgdXBkYXRlZF9hdCA9IDp1cGRhdGVkX2F0JyxcbiAgICAgICAgICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVOYW1lczoge1xuICAgICAgICAgICAgICAgICcjc3RhdHVzJzogJ3N0YXR1cydcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzOiB7XG4gICAgICAgICAgICAgICAgJzpzdGF0dXMnOiAncHVibGlzaGVkJyxcbiAgICAgICAgICAgICAgICAnOmFjdGl2ZSc6IHRydWUsXG4gICAgICAgICAgICAgICAgJzp1cGRhdGVkX2F0JzogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgUmV0dXJuVmFsdWVzOiAnQUxMX05FVydcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQodXBkYXRlQ29tbWFuZCk7XG5cbiAgICAgICAgLy8gRmV0Y2ggbWVkaWEgZGF0YSBpZiBwb3N0IGhhcyBtZWRpYV9pZFxuICAgICAgICBpZiAoKHJlc3VsdC5BdHRyaWJ1dGVzIGFzIGFueSkubWVkaWFfaWQpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZ2V0TWVkaWFDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IE1FRElBX1RBQkxFLFxuICAgICAgICAgICAgICAgICAgICBLZXk6IHsgaWQ6IChyZXN1bHQuQXR0cmlidXRlcyBhcyBhbnkpLm1lZGlhX2lkIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCBtZWRpYVJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoZ2V0TWVkaWFDb21tYW5kKTtcblxuICAgICAgICAgICAgICAgIGlmIChtZWRpYVJlc3VsdC5JdGVtKSB7XG4gICAgICAgICAgICAgICAgICAgIChyZXN1bHQuQXR0cmlidXRlcyBhcyBhbnkpLm1lZGlhID0gbWVkaWFSZXN1bHQuSXRlbTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG1lZGlhIGZvciByZXNwb25zZTonLCBlcnJvcik7XG4gICAgICAgICAgICAgICAgLy8gQ29udGludWUgd2l0aG91dCBtZWRpYSBkYXRhIGluIHJlc3BvbnNlXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoMjAwLCB7XG4gICAgICAgICAgICBtZXNzYWdlOiAnUG9zdCBwdWJsaXNoZWQgc3VjY2Vzc2Z1bGx5JyxcbiAgICAgICAgICAgIHBvc3Q6IHJlc3VsdC5BdHRyaWJ1dGVzXG4gICAgICAgIH0pO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignUHVibGlzaFBvc3QgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNTAwLCB7IGVycm9yOiAnRmFpbGVkIHRvIHB1Ymxpc2ggcG9zdCcsIGRldGFpbHM6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9KTtcbiAgICB9XG59O1xuXG4vLyBEZWxldGUgcG9zdFxuY29uc3QgZGVsZXRlUG9zdCA9IGFzeW5jIChldmVudDogQVBJR2F0ZXdheVByb3h5RXZlbnQpOiBQcm9taXNlPEFQSUdhdGV3YXlQcm94eVJlc3VsdD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHsgaWQgfSA9IGV2ZW50LnBhdGhQYXJhbWV0ZXJzIHx8IHt9O1xuXG4gICAgICAgIGlmICghaWQpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdQb3N0IElEIGlzIHJlcXVpcmVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGRlbGV0ZUNvbW1hbmQgPSBuZXcgRGVsZXRlQ29tbWFuZCh7XG4gICAgICAgICAgICBUYWJsZU5hbWU6IFBPU1RTX1RBQkxFLFxuICAgICAgICAgICAgS2V5OiB7IGlkIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGF3YWl0IGR5bmFtb2RiLnNlbmQoZGVsZXRlQ29tbWFuZCk7XG5cbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDIwMCwgeyBtZXNzYWdlOiAnUG9zdCBkZWxldGVkIHN1Y2Nlc3NmdWxseScgfSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdEZWxldGVQb3N0IGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDUwMCwgeyBlcnJvcjogJ0ZhaWxlZCB0byBkZWxldGUgcG9zdCcsIGRldGFpbHM6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9KTtcbiAgICB9XG59O1xuXG4vLyBMaWtlIHBvc3RcbmNvbnN0IGxpa2VQb3N0ID0gYXN5bmMgKGV2ZW50OiBBUElHYXRld2F5UHJveHlFdmVudCk6IFByb21pc2U8QVBJR2F0ZXdheVByb3h5UmVzdWx0PiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgeyBpZCB9ID0gZXZlbnQucGF0aFBhcmFtZXRlcnMgfHwge307XG5cbiAgICAgICAgaWYgKCFpZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ1Bvc3QgSUQgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gR2V0IHVzZXIgSUQgZnJvbSBhdXRob3JpemVyIGNvbnRleHRcbiAgICAgICAgY29uc3QgdXNlcklkID0gZ2V0VXNlcklkRnJvbUNvbnRleHQoZXZlbnQpO1xuXG4gICAgICAgIGlmICghdXNlcklkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAxLCB7IGVycm9yOiAnVXNlciBub3QgYXV0aGVudGljYXRlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBDaGVjayBpZiBhbHJlYWR5IGxpa2VkXG4gICAgICAgIGNvbnN0IGdldExpa2VDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBMSUtFU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBwb3N0SWQ6IGlkLCB1c2VySWQ6IHVzZXJJZCB9XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBleGlzdGluZ0xpa2UgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKGdldExpa2VDb21tYW5kKTtcblxuICAgICAgICBpZiAoZXhpc3RpbmdMaWtlLkl0ZW0pIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdQb3N0IGFscmVhZHkgbGlrZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQWRkIGxpa2VcbiAgICAgICAgY29uc3QgcHV0TGlrZUNvbW1hbmQgPSBuZXcgUHV0Q29tbWFuZCh7XG4gICAgICAgICAgICBUYWJsZU5hbWU6IExJS0VTX1RBQkxFLFxuICAgICAgICAgICAgSXRlbToge1xuICAgICAgICAgICAgICAgIHBvc3RJZDogaWQsXG4gICAgICAgICAgICAgICAgdXNlcklkOiB1c2VySWQsXG4gICAgICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGF3YWl0IGR5bmFtb2RiLnNlbmQocHV0TGlrZUNvbW1hbmQpO1xuXG4gICAgICAgIC8vIFVwZGF0ZSBwb3N0IGxpa2VzIGNvdW50XG4gICAgICAgIGNvbnN0IHVwZGF0ZVBvc3RDb21tYW5kID0gbmV3IFVwZGF0ZUNvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBQT1NUU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBpZCB9LFxuICAgICAgICAgICAgVXBkYXRlRXhwcmVzc2lvbjogJ0FERCBsaWtlcyA6aW5jJyxcbiAgICAgICAgICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXM6IHsgJzppbmMnOiAxIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGF3YWl0IGR5bmFtb2RiLnNlbmQodXBkYXRlUG9zdENvbW1hbmQpO1xuXG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSgyMDAsIHsgbWVzc2FnZTogJ1Bvc3QgbGlrZWQgc3VjY2Vzc2Z1bGx5JyB9KTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0xpa2VQb3N0IGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDUwMCwgeyBlcnJvcjogJ0ZhaWxlZCB0byBsaWtlIHBvc3QnLCBkZXRhaWxzOiAoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2UgfSk7XG4gICAgfVxufTtcblxuLy8gVW5saWtlIHBvc3RcbmNvbnN0IHVubGlrZVBvc3QgPSBhc3luYyAoZXZlbnQ6IEFQSUdhdGV3YXlQcm94eUV2ZW50KTogUHJvbWlzZTxBUElHYXRld2F5UHJveHlSZXN1bHQ+ID0+IHtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCB7IGlkIH0gPSBldmVudC5wYXRoUGFyYW1ldGVycyB8fCB7fTtcblxuICAgICAgICBpZiAoIWlkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAwLCB7IGVycm9yOiAnUG9zdCBJRCBpcyByZXF1aXJlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBHZXQgdXNlciBJRCBmcm9tIGF1dGhvcml6ZXIgY29udGV4dFxuICAgICAgICBjb25zdCB1c2VySWQgPSBnZXRVc2VySWRGcm9tQ29udGV4dChldmVudCk7XG5cbiAgICAgICAgaWYgKCF1c2VySWQpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDEsIHsgZXJyb3I6ICdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFJlbW92ZSBsaWtlXG4gICAgICAgIGNvbnN0IGRlbGV0ZUxpa2VDb21tYW5kID0gbmV3IERlbGV0ZUNvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBMSUtFU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBwb3N0SWQ6IGlkLCB1c2VySWQ6IHVzZXJJZCB9XG4gICAgICAgIH0pO1xuICAgICAgICBhd2FpdCBkeW5hbW9kYi5zZW5kKGRlbGV0ZUxpa2VDb21tYW5kKTtcblxuICAgICAgICAvLyBVcGRhdGUgcG9zdCBsaWtlcyBjb3VudFxuICAgICAgICBjb25zdCB1cGRhdGVQb3N0Q29tbWFuZCA9IG5ldyBVcGRhdGVDb21tYW5kKHtcbiAgICAgICAgICAgIFRhYmxlTmFtZTogUE9TVFNfVEFCTEUsXG4gICAgICAgICAgICBLZXk6IHsgaWQgfSxcbiAgICAgICAgICAgIFVwZGF0ZUV4cHJlc3Npb246ICdBREQgbGlrZXMgOmRlYycsXG4gICAgICAgICAgICBFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzOiB7ICc6ZGVjJzogLTEgfVxuICAgICAgICB9KTtcbiAgICAgICAgYXdhaXQgZHluYW1vZGIuc2VuZCh1cGRhdGVQb3N0Q29tbWFuZCk7XG5cbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDIwMCwgeyBtZXNzYWdlOiAnUG9zdCB1bmxpa2VkIHN1Y2Nlc3NmdWxseScgfSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdVbmxpa2VQb3N0IGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDUwMCwgeyBlcnJvcjogJ0ZhaWxlZCB0byB1bmxpa2UgcG9zdCcsIGRldGFpbHM6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9KTtcbiAgICB9XG59O1xuXG4vLyBHZXQgY29tbWVudHMgZm9yIGEgcG9zdFxuY29uc3QgZ2V0Q29tbWVudHMgPSBhc3luYyAoZXZlbnQ6IEFQSUdhdGV3YXlQcm94eUV2ZW50KTogUHJvbWlzZTxBUElHYXRld2F5UHJveHlSZXN1bHQ+ID0+IHtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCB7IGlkIH0gPSBldmVudC5wYXRoUGFyYW1ldGVycyB8fCB7fTtcblxuICAgICAgICBpZiAoIWlkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAwLCB7IGVycm9yOiAnUG9zdCBJRCBpcyByZXF1aXJlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBRdWVyeSBjb21tZW50cyBmb3IgdGhlIHBvc3RcbiAgICAgICAgY29uc3QgcXVlcnlDb21tYW5kID0gbmV3IFF1ZXJ5Q29tbWFuZCh7XG4gICAgICAgICAgICBUYWJsZU5hbWU6IENPTU1FTlRTX1RBQkxFLFxuICAgICAgICAgICAgSW5kZXhOYW1lOiAncG9zdElkLWluZGV4JyxcbiAgICAgICAgICAgIEtleUNvbmRpdGlvbkV4cHJlc3Npb246ICdwb3N0SWQgPSA6cG9zdElkJyxcbiAgICAgICAgICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXM6IHtcbiAgICAgICAgICAgICAgICAnOnBvc3RJZCc6IGlkXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKHF1ZXJ5Q29tbWFuZCk7XG5cbiAgICAgICAgLy8gU29ydCBjb21tZW50cyBieSBjcmVhdGVkQXQgYXNjZW5kaW5nIChvbGRlc3QgZmlyc3QpXG4gICAgICAgIGNvbnN0IGNvbW1lbnRzID0gKHJlc3VsdC5JdGVtcyB8fCBbXSkuc29ydCgoYTogYW55LCBiOiBhbnkpID0+IG5ldyBEYXRlKGEuY3JlYXRlZEF0KS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShiLmNyZWF0ZWRBdCkuZ2V0VGltZSgpKTtcblxuICAgICAgICAvLyBGZXRjaCB1c2VyIGRhdGEgZm9yIGVhY2ggY29tbWVudFxuICAgICAgICBjb25zdCBjb21tZW50c1dpdGhVc2VyRGF0YSA9IGF3YWl0IFByb21pc2UuYWxsKGNvbW1lbnRzLm1hcChhc3luYyAoY29tbWVudDogYW55KSA9PiB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGdldFVzZXJDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IFVTRVJTX1RBQkxFLFxuICAgICAgICAgICAgICAgICAgICBLZXk6IHsgaWQ6IGNvbW1lbnQudXNlcklkIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCB1c2VyUmVzdWx0ID0gYXdhaXQgZHluYW1vZGIuc2VuZChnZXRVc2VyQ29tbWFuZCk7XG5cbiAgICAgICAgICAgICAgICBpZiAodXNlclJlc3VsdC5JdGVtKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbW1lbnQudXNlcm5hbWUgPSB1c2VyUmVzdWx0Lkl0ZW0udXNlcm5hbWU7XG4gICAgICAgICAgICAgICAgICAgIGNvbW1lbnQuZGlzcGxheU5hbWUgPSB1c2VyUmVzdWx0Lkl0ZW0uZGlzcGxheU5hbWU7XG4gICAgICAgICAgICAgICAgICAgIGNvbW1lbnQuYXZhdGFyVXJsID0gdXNlclJlc3VsdC5JdGVtLmF2YXRhclVybDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byBmZXRjaCB1c2VyIGRhdGEgZm9yIGNvbW1lbnQgJHtjb21tZW50LmlkfTpgLCBlcnJvcik7XG4gICAgICAgICAgICAgICAgLy8gQ29udGludWUgd2l0aG91dCB1c2VyIGRhdGEgaWYgZmV0Y2ggZmFpbHNcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBjb21tZW50O1xuICAgICAgICB9KSk7XG5cbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDIwMCwge1xuICAgICAgICAgICAgY29tbWVudHM6IGNvbW1lbnRzV2l0aFVzZXJEYXRhLFxuICAgICAgICAgICAgY291bnQ6IGNvbW1lbnRzV2l0aFVzZXJEYXRhLmxlbmd0aFxuICAgICAgICB9KTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0dldENvbW1lbnRzIGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDUwMCwgeyBlcnJvcjogJ0ZhaWxlZCB0byBnZXQgY29tbWVudHMnLCBkZXRhaWxzOiAoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2UgfSk7XG4gICAgfVxufTtcblxuLy8gQ3JlYXRlIGEgY29tbWVudFxuY29uc3QgY3JlYXRlQ29tbWVudCA9IGFzeW5jIChldmVudDogQVBJR2F0ZXdheVByb3h5RXZlbnQpOiBQcm9taXNlPEFQSUdhdGV3YXlQcm94eVJlc3VsdD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHsgaWQgfSA9IGV2ZW50LnBhdGhQYXJhbWV0ZXJzIHx8IHt9O1xuXG4gICAgICAgIGlmICghaWQpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdQb3N0IElEIGlzIHJlcXVpcmVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICghZXZlbnQuYm9keSkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ1JlcXVlc3QgYm9keSBpcyByZXF1aXJlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCB7IGNvbnRlbnQgfSA9IEpTT04ucGFyc2UoZXZlbnQuYm9keSk7XG5cbiAgICAgICAgLy8gR2V0IHVzZXIgSUQgZnJvbSBhdXRob3JpemVyIGNvbnRleHRcbiAgICAgICAgY29uc3QgdXNlcklkID0gZ2V0VXNlcklkRnJvbUNvbnRleHQoZXZlbnQpO1xuXG4gICAgICAgIGlmICghY29udGVudCB8fCBjb250ZW50LnRyaW0oKSA9PT0gJycpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdDb250ZW50IGlzIHJlcXVpcmVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICghdXNlcklkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAxLCB7IGVycm9yOiAnVXNlciBub3QgYXV0aGVudGljYXRlZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBWZXJpZnkgdGhlIHBvc3QgZXhpc3RzXG4gICAgICAgIGNvbnN0IGdldFBvc3RDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBQT1NUU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBpZCB9XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBwb3N0UmVzdWx0ID0gYXdhaXQgZHluYW1vZGIuc2VuZChnZXRQb3N0Q29tbWFuZCk7XG5cbiAgICAgICAgaWYgKCFwb3N0UmVzdWx0Lkl0ZW0pIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDQsIHsgZXJyb3I6ICdQb3N0IG5vdCBmb3VuZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBjb21tZW50SWQgPSB1dWlkdjQoKTtcbiAgICAgICAgY29uc3QgY29tbWVudDogYW55ID0ge1xuICAgICAgICAgICAgaWQ6IGNvbW1lbnRJZCxcbiAgICAgICAgICAgIHBvc3RJZDogaWQsXG4gICAgICAgICAgICB1c2VySWQ6IHVzZXJJZCxcbiAgICAgICAgICAgIGNvbnRlbnQ6IGNvbnRlbnQudHJpbSgpLFxuICAgICAgICAgICAgbGlrZUNvdW50OiAwLFxuICAgICAgICAgICAgaXNBY3RpdmU6IHRydWUsXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgIH07XG5cbiAgICAgICAgLy8gQ3JlYXRlIHRoZSBjb21tZW50XG4gICAgICAgIGNvbnN0IHB1dENvbW1lbnRDb21tYW5kID0gbmV3IFB1dENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBDT01NRU5UU19UQUJMRSxcbiAgICAgICAgICAgIEl0ZW06IGNvbW1lbnRcbiAgICAgICAgfSk7XG4gICAgICAgIGF3YWl0IGR5bmFtb2RiLnNlbmQocHV0Q29tbWVudENvbW1hbmQpO1xuXG4gICAgICAgIC8vIFVwZGF0ZSBwb3N0IGNvbW1lbnRzIGNvdW50XG4gICAgICAgIGNvbnN0IHVwZGF0ZVBvc3RDb21tYW5kID0gbmV3IFVwZGF0ZUNvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBQT1NUU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBpZCB9LFxuICAgICAgICAgICAgVXBkYXRlRXhwcmVzc2lvbjogJ0FERCBjb21tZW50cyA6aW5jJyxcbiAgICAgICAgICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXM6IHsgJzppbmMnOiAxIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGF3YWl0IGR5bmFtb2RiLnNlbmQodXBkYXRlUG9zdENvbW1hbmQpO1xuXG4gICAgICAgIC8vIEZldGNoIHVzZXIgZGF0YSBmb3IgdGhlIHJlc3BvbnNlXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCBnZXRVc2VyQ29tbWFuZCA9IG5ldyBHZXRDb21tYW5kKHtcbiAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IFVTRVJTX1RBQkxFLFxuICAgICAgICAgICAgICAgIEtleTogeyBpZDogdXNlcklkIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc3QgdXNlclJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoZ2V0VXNlckNvbW1hbmQpO1xuXG4gICAgICAgICAgICBpZiAodXNlclJlc3VsdC5JdGVtKSB7XG4gICAgICAgICAgICAgICAgY29tbWVudC51c2VybmFtZSA9IHVzZXJSZXN1bHQuSXRlbS51c2VybmFtZTtcbiAgICAgICAgICAgICAgICBjb21tZW50LmRpc3BsYXlOYW1lID0gdXNlclJlc3VsdC5JdGVtLmRpc3BsYXlOYW1lO1xuICAgICAgICAgICAgICAgIGNvbW1lbnQuYXZhdGFyVXJsID0gdXNlclJlc3VsdC5JdGVtLmF2YXRhclVybDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHVzZXIgZGF0YSBmb3IgcmVzcG9uc2U6JywgZXJyb3IpO1xuICAgICAgICAgICAgLy8gQ29udGludWUgd2l0aG91dCB1c2VyIGRhdGEgaW4gcmVzcG9uc2VcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSgyMDEsIHtcbiAgICAgICAgICAgIG1lc3NhZ2U6ICdDb21tZW50IGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5JyxcbiAgICAgICAgICAgIGNvbW1lbnRcbiAgICAgICAgfSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdDcmVhdGVDb21tZW50IGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDUwMCwgeyBlcnJvcjogJ0ZhaWxlZCB0byBjcmVhdGUgY29tbWVudCcsIGRldGFpbHM6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9KTtcbiAgICB9XG59O1xuXG4vLyBVcGRhdGUgYSBjb21tZW50XG5jb25zdCB1cGRhdGVDb21tZW50ID0gYXN5bmMgKGV2ZW50OiBBUElHYXRld2F5UHJveHlFdmVudCk6IFByb21pc2U8QVBJR2F0ZXdheVByb3h5UmVzdWx0PiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgeyBpZCB9ID0gZXZlbnQucGF0aFBhcmFtZXRlcnMgfHwge307XG5cbiAgICAgICAgaWYgKCFpZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ0NvbW1lbnQgSUQgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCFldmVudC5ib2R5KSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAwLCB7IGVycm9yOiAnUmVxdWVzdCBib2R5IGlzIHJlcXVpcmVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHsgY29udGVudCB9ID0gSlNPTi5wYXJzZShldmVudC5ib2R5KTtcblxuICAgICAgICAvLyBHZXQgdXNlciBJRCBmcm9tIGF1dGhvcml6ZXIgY29udGV4dFxuICAgICAgICBjb25zdCB1c2VySWQgPSBnZXRVc2VySWRGcm9tQ29udGV4dChldmVudCk7XG5cbiAgICAgICAgaWYgKCFjb250ZW50IHx8IGNvbnRlbnQudHJpbSgpID09PSAnJykge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMCwgeyBlcnJvcjogJ0NvbnRlbnQgaXMgcmVxdWlyZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCF1c2VySWQpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDEsIHsgZXJyb3I6ICdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEdldCB0aGUgY29tbWVudCB0byB2ZXJpZnkgb3duZXJzaGlwXG4gICAgICAgIGNvbnN0IGdldENvbW1lbnRDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBDT01NRU5UU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBpZCB9XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBjb21tZW50UmVzdWx0ID0gYXdhaXQgZHluYW1vZGIuc2VuZChnZXRDb21tZW50Q29tbWFuZCk7XG5cbiAgICAgICAgaWYgKCFjb21tZW50UmVzdWx0Lkl0ZW0pIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDQsIHsgZXJyb3I6ICdDb21tZW50IG5vdCBmb3VuZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBWZXJpZnkgdGhlIHVzZXIgb3ducyB0aGUgY29tbWVudFxuICAgICAgICBpZiAoKGNvbW1lbnRSZXN1bHQuSXRlbSBhcyBhbnkpLnVzZXJJZCAhPT0gdXNlcklkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAzLCB7IGVycm9yOiAnWW91IGNhbiBvbmx5IHVwZGF0ZSB5b3VyIG93biBjb21tZW50cycgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBVcGRhdGUgdGhlIGNvbW1lbnRcbiAgICAgICAgY29uc3QgdXBkYXRlQ29tbWFuZCA9IG5ldyBVcGRhdGVDb21tYW5kKHtcbiAgICAgICAgICAgIFRhYmxlTmFtZTogQ09NTUVOVFNfVEFCTEUsXG4gICAgICAgICAgICBLZXk6IHsgaWQgfSxcbiAgICAgICAgICAgIFVwZGF0ZUV4cHJlc3Npb246ICdTRVQgY29udGVudCA9IDpjb250ZW50LCB1cGRhdGVkQXQgPSA6dXBkYXRlZEF0JyxcbiAgICAgICAgICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXM6IHtcbiAgICAgICAgICAgICAgICAnOmNvbnRlbnQnOiBjb250ZW50LnRyaW0oKSxcbiAgICAgICAgICAgICAgICAnOnVwZGF0ZWRBdCc6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIFJldHVyblZhbHVlczogJ0FMTF9ORVcnXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBkeW5hbW9kYi5zZW5kKHVwZGF0ZUNvbW1hbmQpO1xuXG4gICAgICAgIGNvbnN0IHVwZGF0ZWRDb21tZW50ID0gcmVzdWx0LkF0dHJpYnV0ZXMgYXMgYW55O1xuXG4gICAgICAgIC8vIEZldGNoIHVzZXIgZGF0YSBmb3IgdGhlIHJlc3BvbnNlXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCBnZXRVc2VyQ29tbWFuZCA9IG5ldyBHZXRDb21tYW5kKHtcbiAgICAgICAgICAgICAgICBUYWJsZU5hbWU6IFVTRVJTX1RBQkxFLFxuICAgICAgICAgICAgICAgIEtleTogeyBpZDogdXNlcklkIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc3QgdXNlclJlc3VsdCA9IGF3YWl0IGR5bmFtb2RiLnNlbmQoZ2V0VXNlckNvbW1hbmQpO1xuXG4gICAgICAgICAgICBpZiAodXNlclJlc3VsdC5JdGVtKSB7XG4gICAgICAgICAgICAgICAgdXBkYXRlZENvbW1lbnQudXNlcm5hbWUgPSB1c2VyUmVzdWx0Lkl0ZW0udXNlcm5hbWU7XG4gICAgICAgICAgICAgICAgdXBkYXRlZENvbW1lbnQuZGlzcGxheU5hbWUgPSB1c2VyUmVzdWx0Lkl0ZW0uZGlzcGxheU5hbWU7XG4gICAgICAgICAgICAgICAgdXBkYXRlZENvbW1lbnQuYXZhdGFyVXJsID0gdXNlclJlc3VsdC5JdGVtLmF2YXRhclVybDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHVzZXIgZGF0YSBmb3IgcmVzcG9uc2U6JywgZXJyb3IpO1xuICAgICAgICAgICAgLy8gQ29udGludWUgd2l0aG91dCB1c2VyIGRhdGEgaW4gcmVzcG9uc2VcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSgyMDAsIHtcbiAgICAgICAgICAgIG1lc3NhZ2U6ICdDb21tZW50IHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JyxcbiAgICAgICAgICAgIGNvbW1lbnQ6IHVwZGF0ZWRDb21tZW50XG4gICAgICAgIH0pO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignVXBkYXRlQ29tbWVudCBlcnJvcjonLCBlcnJvcik7XG4gICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg1MDAsIHsgZXJyb3I6ICdGYWlsZWQgdG8gdXBkYXRlIGNvbW1lbnQnLCBkZXRhaWxzOiAoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2UgfSk7XG4gICAgfVxufTtcblxuLy8gRGVsZXRlIGEgY29tbWVudFxuY29uc3QgZGVsZXRlQ29tbWVudCA9IGFzeW5jIChldmVudDogQVBJR2F0ZXdheVByb3h5RXZlbnQpOiBQcm9taXNlPEFQSUdhdGV3YXlQcm94eVJlc3VsdD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHsgaWQgfSA9IGV2ZW50LnBhdGhQYXJhbWV0ZXJzIHx8IHt9O1xuXG4gICAgICAgIGlmICghaWQpIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDAsIHsgZXJyb3I6ICdDb21tZW50IElEIGlzIHJlcXVpcmVkJyB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEdldCB1c2VyIElEIGZyb20gYXV0aG9yaXplciBjb250ZXh0XG4gICAgICAgIGNvbnN0IHVzZXJJZCA9IGdldFVzZXJJZEZyb21Db250ZXh0KGV2ZW50KTtcblxuICAgICAgICBpZiAoIXVzZXJJZCkge1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDQwMSwgeyBlcnJvcjogJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gR2V0IHRoZSBjb21tZW50IHRvIHZlcmlmeSBvd25lcnNoaXAgYW5kIGdldCBwb3N0X2lkXG4gICAgICAgIGNvbnN0IGdldENvbW1lbnRDb21tYW5kID0gbmV3IEdldENvbW1hbmQoe1xuICAgICAgICAgICAgVGFibGVOYW1lOiBDT01NRU5UU19UQUJMRSxcbiAgICAgICAgICAgIEtleTogeyBpZCB9XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBjb21tZW50UmVzdWx0ID0gYXdhaXQgZHluYW1vZGIuc2VuZChnZXRDb21tZW50Q29tbWFuZCk7XG5cbiAgICAgICAgaWYgKCFjb21tZW50UmVzdWx0Lkl0ZW0pIHtcbiAgICAgICAgICAgIHJldHVybiBjcmVhdGVSZXNwb25zZSg0MDQsIHsgZXJyb3I6ICdDb21tZW50IG5vdCBmb3VuZCcgfSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBWZXJpZnkgdGhlIHVzZXIgb3ducyB0aGUgY29tbWVudFxuICAgICAgICBpZiAoKGNvbW1lbnRSZXN1bHQuSXRlbSBhcyBhbnkpLnVzZXJJZCAhPT0gdXNlcklkKSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDAzLCB7IGVycm9yOiAnWW91IGNhbiBvbmx5IGRlbGV0ZSB5b3VyIG93biBjb21tZW50cycgfSk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBwb3N0SWQgPSAoY29tbWVudFJlc3VsdC5JdGVtIGFzIGFueSkucG9zdElkO1xuXG4gICAgICAgIC8vIERlbGV0ZSB0aGUgY29tbWVudFxuICAgICAgICBjb25zdCBkZWxldGVDb21tZW50Q29tbWFuZCA9IG5ldyBEZWxldGVDb21tYW5kKHtcbiAgICAgICAgICAgIFRhYmxlTmFtZTogQ09NTUVOVFNfVEFCTEUsXG4gICAgICAgICAgICBLZXk6IHsgaWQgfVxuICAgICAgICB9KTtcbiAgICAgICAgYXdhaXQgZHluYW1vZGIuc2VuZChkZWxldGVDb21tZW50Q29tbWFuZCk7XG5cbiAgICAgICAgLy8gVXBkYXRlIHBvc3QgY29tbWVudHMgY291bnRcbiAgICAgICAgY29uc3QgdXBkYXRlUG9zdENvbW1hbmQgPSBuZXcgVXBkYXRlQ29tbWFuZCh7XG4gICAgICAgICAgICBUYWJsZU5hbWU6IFBPU1RTX1RBQkxFLFxuICAgICAgICAgICAgS2V5OiB7IGlkOiBwb3N0SWQgfSxcbiAgICAgICAgICAgIFVwZGF0ZUV4cHJlc3Npb246ICdBREQgY29tbWVudHMgOmRlYycsXG4gICAgICAgICAgICBFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzOiB7ICc6ZGVjJzogLTEgfVxuICAgICAgICB9KTtcbiAgICAgICAgYXdhaXQgZHluYW1vZGIuc2VuZCh1cGRhdGVQb3N0Q29tbWFuZCk7XG5cbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDIwMCwgeyBtZXNzYWdlOiAnQ29tbWVudCBkZWxldGVkIHN1Y2Nlc3NmdWxseScgfSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdEZWxldGVDb21tZW50IGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZVJlc3BvbnNlKDUwMCwgeyBlcnJvcjogJ0ZhaWxlZCB0byBkZWxldGUgY29tbWVudCcsIGRldGFpbHM6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9KTtcbiAgICB9XG59O1xuXG4vLyBNYWluIGhhbmRsZXJcbmV4cG9ydCBjb25zdCBoYW5kbGVyID0gYXN5bmMgKGV2ZW50OiBBUElHYXRld2F5UHJveHlFdmVudCk6IFByb21pc2U8QVBJR2F0ZXdheVByb3h5UmVzdWx0PiA9PiB7XG4gICAgY29uc29sZS5sb2coJ0V2ZW50OicsIEpTT04uc3RyaW5naWZ5KGV2ZW50LCBudWxsLCAyKSk7XG5cbiAgICBjb25zdCB7IGh0dHBNZXRob2QsIHBhdGgsIHBhdGhQYXJhbWV0ZXJzIH0gPSBldmVudDtcblxuICAgIHRyeSB7XG4gICAgICAgIC8vIFBvc3RzIHJvdXRlc1xuICAgICAgICBpZiAoaHR0cE1ldGhvZCA9PT0gJ0dFVCcgJiYgcGF0aCA9PT0gJy9wb3N0cycpIHtcbiAgICAgICAgICAgIHJldHVybiBhd2FpdCBnZXRQb3N0cyhldmVudCk7XG4gICAgICAgIH0gZWxzZSBpZiAoaHR0cE1ldGhvZCA9PT0gJ1BPU1QnICYmIHBhdGggPT09ICcvcG9zdHMvZHJhZnQnKSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgY3JlYXRlRHJhZnRQb3N0KGV2ZW50KTtcbiAgICAgICAgfSBlbHNlIGlmIChodHRwTWV0aG9kID09PSAnUE9TVCcgJiYgcGF0aCA9PT0gJy9wb3N0cycpIHtcbiAgICAgICAgICAgIHJldHVybiBhd2FpdCBjcmVhdGVQb3N0KGV2ZW50KTtcbiAgICAgICAgfSBlbHNlIGlmIChodHRwTWV0aG9kID09PSAnR0VUJyAmJiBwYXRoUGFyYW1ldGVycyAmJiBwYXRoUGFyYW1ldGVycy5pZCAmJiAhcGF0aC5pbmNsdWRlcygnL2NvbW1lbnRzJykgJiYgIXBhdGguaW5jbHVkZXMoJy9saWtlJykgJiYgIXBhdGguaW5jbHVkZXMoJy9tZWRpYScpICYmICFwYXRoLmluY2x1ZGVzKCcvcHVibGlzaCcpKSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgZ2V0UG9zdChldmVudCk7XG4gICAgICAgIH0gZWxzZSBpZiAoaHR0cE1ldGhvZCA9PT0gJ1BVVCcgJiYgcGF0aFBhcmFtZXRlcnMgJiYgcGF0aFBhcmFtZXRlcnMuaWQgJiYgcGF0aC5pbmNsdWRlcygnL21lZGlhJykpIHtcbiAgICAgICAgICAgIHJldHVybiBhd2FpdCBhdHRhY2hNZWRpYVRvUG9zdChldmVudCk7XG4gICAgICAgIH0gZWxzZSBpZiAoaHR0cE1ldGhvZCA9PT0gJ1BVVCcgJiYgcGF0aFBhcmFtZXRlcnMgJiYgcGF0aFBhcmFtZXRlcnMuaWQgJiYgcGF0aC5pbmNsdWRlcygnL3B1Ymxpc2gnKSkge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IHB1Ymxpc2hQb3N0KGV2ZW50KTtcbiAgICAgICAgfSBlbHNlIGlmIChodHRwTWV0aG9kID09PSAnUFVUJyAmJiBwYXRoUGFyYW1ldGVycyAmJiBwYXRoUGFyYW1ldGVycy5pZCAmJiAhcGF0aC5pbmNsdWRlcygnL2NvbW1lbnRzJykgJiYgIXBhdGguaW5jbHVkZXMoJy9saWtlJykgJiYgIXBhdGguaW5jbHVkZXMoJy9tZWRpYScpICYmICFwYXRoLmluY2x1ZGVzKCcvcHVibGlzaCcpKSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgdXBkYXRlUG9zdChldmVudCk7XG4gICAgICAgIH0gZWxzZSBpZiAoaHR0cE1ldGhvZCA9PT0gJ0RFTEVURScgJiYgcGF0aFBhcmFtZXRlcnMgJiYgcGF0aFBhcmFtZXRlcnMuaWQgJiYgIXBhdGguaW5jbHVkZXMoJy9jb21tZW50cycpICYmICFwYXRoLmluY2x1ZGVzKCcvbGlrZScpKSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgZGVsZXRlUG9zdChldmVudCk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gTGlrZSByb3V0ZXNcbiAgICAgICAgZWxzZSBpZiAoaHR0cE1ldGhvZCA9PT0gJ1BPU1QnICYmIHBhdGguaW5jbHVkZXMoJy9saWtlJykpIHtcbiAgICAgICAgICAgIHJldHVybiBhd2FpdCBsaWtlUG9zdChldmVudCk7XG4gICAgICAgIH0gZWxzZSBpZiAoaHR0cE1ldGhvZCA9PT0gJ0RFTEVURScgJiYgcGF0aC5pbmNsdWRlcygnL2xpa2UnKSkge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IHVubGlrZVBvc3QoZXZlbnQpO1xuICAgICAgICB9XG4gICAgICAgIC8vIENvbW1lbnRzIHJvdXRlc1xuICAgICAgICBlbHNlIGlmIChodHRwTWV0aG9kID09PSAnR0VUJyAmJiBwYXRoLmluY2x1ZGVzKCcvY29tbWVudHMnKSAmJiBwYXRoUGFyYW1ldGVycyAmJiBwYXRoUGFyYW1ldGVycy5pZCkge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IGdldENvbW1lbnRzKGV2ZW50KTtcbiAgICAgICAgfSBlbHNlIGlmIChodHRwTWV0aG9kID09PSAnUE9TVCcgJiYgcGF0aC5pbmNsdWRlcygnL2NvbW1lbnRzJykgJiYgcGF0aFBhcmFtZXRlcnMgJiYgcGF0aFBhcmFtZXRlcnMuaWQpIHtcbiAgICAgICAgICAgIHJldHVybiBhd2FpdCBjcmVhdGVDb21tZW50KGV2ZW50KTtcbiAgICAgICAgfSBlbHNlIGlmIChodHRwTWV0aG9kID09PSAnUFVUJyAmJiBwYXRoLnN0YXJ0c1dpdGgoJy9jb21tZW50cy8nKSAmJiBwYXRoUGFyYW1ldGVycyAmJiBwYXRoUGFyYW1ldGVycy5pZCkge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IHVwZGF0ZUNvbW1lbnQoZXZlbnQpO1xuICAgICAgICB9IGVsc2UgaWYgKGh0dHBNZXRob2QgPT09ICdERUxFVEUnICYmIHBhdGguc3RhcnRzV2l0aCgnL2NvbW1lbnRzLycpICYmIHBhdGhQYXJhbWV0ZXJzICYmIHBhdGhQYXJhbWV0ZXJzLmlkKSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgZGVsZXRlQ29tbWVudChldmVudCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNDA0LCB7IGVycm9yOiAnTm90IGZvdW5kJyB9KTtcbiAgICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0hhbmRsZXIgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gY3JlYXRlUmVzcG9uc2UoNTAwLCB7IGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJywgZGV0YWlsczogKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlIH0pO1xuICAgIH1cbn07XG4iXX0=