import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, DeleteCommand, UpdateCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';

// Configure AWS SDK for DynamoDB
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

const REFLEXES_TABLE = process.env.REFLEXES_TABLE || 'test-reflexes-table';
const POSTS_TABLE = process.env.POSTS_TABLE || 'test-posts-table';
const USERS_TABLE = process.env.USERS_TABLE || 'test-users-table';
const MEDIA_TABLE = process.env.MEDIA_TABLE || 'test-media-table';

// TypeScript interfaces
interface Reflex {
    id: string;
    postId: string;
    userId: string;
    mediaId?: string;
    flareData?: any;
    textOverlay?: string;
    reflexType: 'flare' | 'custom_image';
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    username?: string;
    displayName?: string;
    avatar_url?: string;
    avatarUrl?: string;
}

interface CreateReflexRequest {
    mediaId?: string;
    flareData?: any;
    textOverlay?: string;
    reflexType?: 'flare' | 'custom_image';
}

interface Post {
    id: string;
    reflexes?: number;
}

interface User {
    id: string;
    username?: string;
    displayName?: string;
    avatar_url?: string;
    avatarUrl?: string;
}

interface Media {
    id: string;
}

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Helper function to get user ID from event
const getUserIdFromEvent = (event: APIGatewayProxyEvent): string => {
    try {
        if (event.requestContext && event.requestContext.authorizer) {
            const claims = (event.requestContext.authorizer as any).claims;
            if (claims && claims.sub) {
                return claims.sub;
            }
        }
        throw new Error('User ID not found in request context');
    } catch (error) {
        console.error('Error extracting user ID:', error);
        throw new Error('Authentication required');
    }
};

// Get reflexes for a post
const getReflexes = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Verify post exists
        const postCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });

        const postResult = await dynamodb.send(postCommand);

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        // Query reflexes for the post
        const queryCommand = new QueryCommand({
            TableName: REFLEXES_TABLE,
            IndexName: 'postId-index',
            KeyConditionExpression: 'postId = :postId',
            ExpressionAttributeValues: {
                ':postId': id
            },
            ScanIndexForward: false // Get most recent first
        });

        const result = await dynamodb.send(queryCommand);

        // Enrich reflexes with user data
        const reflexesWithUserData = await Promise.all(
            (result.Items || []).map(async (reflex: any) => {
                try {
                    const userCommand = new GetCommand({
                        TableName: USERS_TABLE,
                        Key: { id: reflex.userId }
                    });

                    const userResult = await dynamodb.send(userCommand);

                    if (userResult.Item) {
                        const user = userResult.Item as User;
                        reflex.username = user.username;
                        reflex.displayName = user.displayName;
                        reflex.avatar_url = user.avatar_url;
                    }
                } catch (error) {
                    console.error('Error fetching user data for reflex:', error);
                    // Continue without user data
                }
                return reflex as Reflex;
            })
        );

        return createResponse(200, {
            reflexes: reflexesWithUserData,
            count: reflexesWithUserData.length
        });

    } catch (error) {
        console.error('GetReflexes error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to get reflexes', details: errorMessage });
    }
};

// Create a new reflex
const createReflex = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {}; // post_id
        const userId = getUserIdFromEvent(event);

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const {
            mediaId,
            flareData,
            textOverlay,
            reflexType = 'flare'
        }: CreateReflexRequest = JSON.parse(event.body);

        // Validate required fields based on reflex type
        if (reflexType === 'custom_image' && !mediaId) {
            return createResponse(400, { error: 'mediaId is required for custom_image reflexes' });
        }

        if (reflexType === 'flare' && !flareData && !textOverlay) {
            return createResponse(400, { error: 'flareData or textOverlay is required for flare reflexes' });
        }

        // Verify post exists
        const postCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });

        const postResult = await dynamodb.send(postCommand);

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        // Verify media exists if mediaId provided
        if (mediaId) {
            const mediaCommand = new GetCommand({
                TableName: MEDIA_TABLE,
                Key: { id: mediaId }
            });

            const mediaResult = await dynamodb.send(mediaCommand);

            if (!mediaResult.Item) {
                return createResponse(404, { error: 'Media not found' });
            }
        }

        const reflexId = uuidv4();
        const reflex: Reflex = {
            id: reflexId,
            postId: id,
            userId: userId,
            mediaId: mediaId || undefined,
            flareData: flareData || undefined,
            textOverlay: textOverlay || undefined,
            reflexType,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        const putCommand = new PutCommand({
            TableName: REFLEXES_TABLE,
            Item: reflex
        });

        await dynamodb.send(putCommand);

        // Update post reflex count
        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD reflexes :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });

        await dynamodb.send(updateCommand);

        // Fetch user data for the response
        try {
            const userCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userId }
            });

            const userResult = await dynamodb.send(userCommand);

            if (userResult.Item) {
                const user = userResult.Item as User;
                reflex.username = user.username;
                reflex.displayName = user.displayName;
                reflex.avatarUrl = user.avatarUrl;
            }
        } catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }

        return createResponse(201, {
            message: 'Reflex created successfully',
            reflex
        });

    } catch (error) {
        console.error('CreateReflex error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to create reflex', details: errorMessage });
    }
};

// Delete a reflex
const deleteReflex = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};
        const userId = getUserIdFromEvent(event);

        if (!id) {
            return createResponse(400, { error: 'Reflex ID is required' });
        }

        // Get the reflex to verify ownership
        const getCommand = new GetCommand({
            TableName: REFLEXES_TABLE,
            Key: { id }
        });

        const reflexResult = await dynamodb.send(getCommand);

        if (!reflexResult.Item) {
            return createResponse(404, { error: 'Reflex not found' });
        }

        const reflex = reflexResult.Item as Reflex;

        // Check if user owns the reflex
        if (reflex.userId !== userId) {
            return createResponse(403, { error: 'Not authorized to delete this reflex' });
        }

        const postId = reflex.postId;

        // Delete the reflex
        const deleteCommand = new DeleteCommand({
            TableName: REFLEXES_TABLE,
            Key: { id }
        });

        await dynamodb.send(deleteCommand);

        // Update post reflex count
        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId },
            UpdateExpression: 'ADD reflexes :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });

        await dynamodb.send(updateCommand);

        return createResponse(200, { message: 'Reflex deleted successfully' });

    } catch (error) {
        console.error('DeleteReflex error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to delete reflex', details: errorMessage });
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        // Handle CORS preflight
        if (httpMethod === 'OPTIONS') {
            return createResponse(200, { message: 'CORS preflight' });
        }

        // Route requests
        if (httpMethod === 'GET' && path.includes('/posts/') && path.includes('/reflexes')) {
            return await getReflexes(event);
        } else if (httpMethod === 'POST' && path.includes('/posts/') && path.includes('/reflexes')) {
            return await createReflex(event);
        } else if (httpMethod === 'DELETE' && path.includes('/reflexes/')) {
            return await deleteReflex(event);
        } else {
            return createResponse(404, { error: 'Route not found' });
        }

    } catch (error) {
        console.error('Handler error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Internal server error', details: errorMessage });
    }
};
