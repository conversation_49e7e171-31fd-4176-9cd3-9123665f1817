"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const uuid_1 = require("uuid");
// Configure AWS SDK for DynamoDB
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};
const dynamodbClient = new client_dynamodb_1.DynamoDBClient(awsConfig);
const dynamodb = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamodbClient);
const REFLEXES_TABLE = process.env.REFLEXES_TABLE || 'test-reflexes-table';
const POSTS_TABLE = process.env.POSTS_TABLE || 'test-posts-table';
const USERS_TABLE = process.env.USERS_TABLE || 'test-users-table';
const MEDIA_TABLE = process.env.MEDIA_TABLE || 'test-media-table';
// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});
// Helper function to get user ID from event
const getUserIdFromEvent = (event) => {
    try {
        if (event.requestContext && event.requestContext.authorizer) {
            const claims = event.requestContext.authorizer.claims;
            if (claims && claims.sub) {
                return claims.sub;
            }
        }
        throw new Error('User ID not found in request context');
    }
    catch (error) {
        console.error('Error extracting user ID:', error);
        throw new Error('Authentication required');
    }
};
// Get reflexes for a post
const getReflexes = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        // Verify post exists
        const postCommand = new lib_dynamodb_1.GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(postCommand);
        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }
        // Query reflexes for the post
        const queryCommand = new lib_dynamodb_1.QueryCommand({
            TableName: REFLEXES_TABLE,
            IndexName: 'postId-index',
            KeyConditionExpression: 'postId = :postId',
            ExpressionAttributeValues: {
                ':postId': id
            },
            ScanIndexForward: false // Get most recent first
        });
        const result = await dynamodb.send(queryCommand);
        // Enrich reflexes with user data
        const reflexesWithUserData = await Promise.all((result.Items || []).map(async (reflex) => {
            try {
                const userCommand = new lib_dynamodb_1.GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: reflex.userId }
                });
                const userResult = await dynamodb.send(userCommand);
                if (userResult.Item) {
                    const user = userResult.Item;
                    reflex.username = user.username;
                    reflex.displayName = user.displayName;
                    reflex.avatar_url = user.avatar_url;
                }
            }
            catch (error) {
                console.error('Error fetching user data for reflex:', error);
                // Continue without user data
            }
            return reflex;
        }));
        return createResponse(200, {
            reflexes: reflexesWithUserData,
            count: reflexesWithUserData.length
        });
    }
    catch (error) {
        console.error('GetReflexes error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to get reflexes', details: errorMessage });
    }
};
// Create a new reflex
const createReflex = async (event) => {
    try {
        const { id } = event.pathParameters || {}; // post_id
        const userId = getUserIdFromEvent(event);
        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }
        const { mediaId, flareData, textOverlay, reflexType = 'flare' } = JSON.parse(event.body);
        // Validate required fields based on reflex type
        if (reflexType === 'custom_image' && !mediaId) {
            return createResponse(400, { error: 'mediaId is required for custom_image reflexes' });
        }
        if (reflexType === 'flare' && !flareData && !textOverlay) {
            return createResponse(400, { error: 'flareData or textOverlay is required for flare reflexes' });
        }
        // Verify post exists
        const postCommand = new lib_dynamodb_1.GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(postCommand);
        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }
        // Verify media exists if mediaId provided
        if (mediaId) {
            const mediaCommand = new lib_dynamodb_1.GetCommand({
                TableName: MEDIA_TABLE,
                Key: { id: mediaId }
            });
            const mediaResult = await dynamodb.send(mediaCommand);
            if (!mediaResult.Item) {
                return createResponse(404, { error: 'Media not found' });
            }
        }
        const reflexId = (0, uuid_1.v4)();
        const reflex = {
            id: reflexId,
            postId: id,
            userId: userId,
            mediaId: mediaId || undefined,
            flareData: flareData || undefined,
            textOverlay: textOverlay || undefined,
            reflexType,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        const putCommand = new lib_dynamodb_1.PutCommand({
            TableName: REFLEXES_TABLE,
            Item: reflex
        });
        await dynamodb.send(putCommand);
        // Update post reflex count
        const updateCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD reflexes :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updateCommand);
        // Fetch user data for the response
        try {
            const userCommand = new lib_dynamodb_1.GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userId }
            });
            const userResult = await dynamodb.send(userCommand);
            if (userResult.Item) {
                const user = userResult.Item;
                reflex.username = user.username;
                reflex.displayName = user.displayName;
                reflex.avatarUrl = user.avatarUrl;
            }
        }
        catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }
        return createResponse(201, {
            message: 'Reflex created successfully',
            reflex
        });
    }
    catch (error) {
        console.error('CreateReflex error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to create reflex', details: errorMessage });
    }
};
// Delete a reflex
const deleteReflex = async (event) => {
    try {
        const { id } = event.pathParameters || {};
        const userId = getUserIdFromEvent(event);
        if (!id) {
            return createResponse(400, { error: 'Reflex ID is required' });
        }
        // Get the reflex to verify ownership
        const getCommand = new lib_dynamodb_1.GetCommand({
            TableName: REFLEXES_TABLE,
            Key: { id }
        });
        const reflexResult = await dynamodb.send(getCommand);
        if (!reflexResult.Item) {
            return createResponse(404, { error: 'Reflex not found' });
        }
        const reflex = reflexResult.Item;
        // Check if user owns the reflex
        if (reflex.userId !== userId) {
            return createResponse(403, { error: 'Not authorized to delete this reflex' });
        }
        const postId = reflex.postId;
        // Delete the reflex
        const deleteCommand = new lib_dynamodb_1.DeleteCommand({
            TableName: REFLEXES_TABLE,
            Key: { id }
        });
        await dynamodb.send(deleteCommand);
        // Update post reflex count
        const updateCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId },
            UpdateExpression: 'ADD reflexes :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updateCommand);
        return createResponse(200, { message: 'Reflex deleted successfully' });
    }
    catch (error) {
        console.error('DeleteReflex error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Failed to delete reflex', details: errorMessage });
    }
};
// Main handler
const handler = async (event, context) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    const { httpMethod, path, pathParameters } = event;
    try {
        // Handle CORS preflight
        if (httpMethod === 'OPTIONS') {
            return createResponse(200, { message: 'CORS preflight' });
        }
        // Route requests
        if (httpMethod === 'GET' && path.includes('/posts/') && path.includes('/reflexes')) {
            return await getReflexes(event);
        }
        else if (httpMethod === 'POST' && path.includes('/posts/') && path.includes('/reflexes')) {
            return await createReflex(event);
        }
        else if (httpMethod === 'DELETE' && path.includes('/reflexes/')) {
            return await deleteReflex(event);
        }
        else {
            return createResponse(404, { error: 'Route not found' });
        }
    }
    catch (error) {
        console.error('Handler error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { error: 'Internal server error', details: errorMessage });
    }
};
exports.handler = handler;
//# sourceMappingURL=data:application/json;base64,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