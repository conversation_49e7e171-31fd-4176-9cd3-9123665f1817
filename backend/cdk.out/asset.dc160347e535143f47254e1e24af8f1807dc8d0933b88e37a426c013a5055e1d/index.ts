import {
    CognitoIdentityProviderClient,
    GetUserCommand,
    GetUserCommandOutput,
    AttributeType
} from '@aws-sdk/client-cognito-identity-provider';
import { APIGatewayTokenAuthorizerEvent } from 'aws-lambda';

// Configure AWS SDK v3 client
const cognitoClient = new CognitoIdentityProviderClient({
    region: process.env.AWS_REGION || 'us-west-2'
});

// TypeScript interfaces
interface PolicyDocument {
    Version: string;
    Statement: PolicyStatement[];
}

interface PolicyStatement {
    Action: string;
    Effect: 'Allow' | 'Deny';
    Resource: string;
}

interface AuthResponse {
    principalId: string;
    policyDocument?: PolicyDocument;
    context?: Record<string, string>;
}

interface UserAttributes {
    [key: string]: string;
}

// Helper function to generate policy
const generatePolicy = (
    principalId: string,
    effect: 'Allow' | 'Deny',
    resource: string,
    context: Record<string, string> = {}
): AuthResponse => {
    const authResponse: AuthResponse = {
        principalId: principalId
    };

    if (effect && resource) {
        const policyDocument: PolicyDocument = {
            Version: '2012-10-17',
            Statement: [
                {
                    Action: 'execute-api:Invoke',
                    Effect: effect,
                    Resource: resource
                }
            ]
        };
        authResponse.policyDocument = policyDocument;
    }

    // Add context to be passed to the lambda function
    if (Object.keys(context).length > 0) {
        authResponse.context = context;
    }

    return authResponse;
};

// Main authorizer handler
export const handler = async (event: APIGatewayTokenAuthorizerEvent): Promise<AuthResponse> => {
    console.log('Authorizer event:', JSON.stringify(event, null, 2));

    try {
        // Extract token from the authorization header
        const token = event.authorizationToken;

        if (!token) {
            console.log('No authorization token provided');
            throw new Error('Unauthorized');
        }

        // Remove 'Bearer ' prefix if present
        const accessToken = token.replace(/^Bearer\s+/, '');

        if (!accessToken) {
            console.log('No access token found after removing Bearer prefix');
            throw new Error('Unauthorized');
        }

        try {
            // Validate token with Cognito using AWS SDK v3
            const getUserCommand = new GetUserCommand({
                AccessToken: accessToken
            });

            const cognitoUser: GetUserCommandOutput = await cognitoClient.send(getUserCommand);
            console.log('Cognito user:', cognitoUser);

            // Extract user information from Cognito attributes
            const userAttributes: UserAttributes = {};
            if (cognitoUser.UserAttributes) {
                cognitoUser.UserAttributes.forEach((attr: AttributeType) => {
                    if (attr.Name && attr.Value) {
                        userAttributes[attr.Name] = attr.Value;
                    }
                });
            }

            // Create context to pass to the lambda function using Cognito data
            const context: Record<string, string> = {
                userId: userAttributes.sub || '', // Use Cognito sub as the primary user ID
                email: userAttributes.email || '',
                username: cognitoUser.Username || '',
                cognitoUserId: cognitoUser.Username || '',
                sub: userAttributes.sub || '',
                firstName: userAttributes.given_name || '',
                lastName: userAttributes.family_name || ''
            };

            console.log('User context:', context);

            // Generate allow policy for all resources in this API
            // Use wildcard to allow access to all methods and paths in this API
            const apiArn = event.methodArn.split('/').slice(0, 2).join('/') + '/*';
            return generatePolicy(userAttributes.sub, 'Allow', apiArn, context);

        } catch (cognitoError) {
            console.error('Token validation error:', cognitoError);
            throw new Error('Unauthorized');
        }

    } catch (error) {
        console.error('Authorizer error:', error);

        // Return deny policy for any error
        return generatePolicy('user', 'Deny', event.methodArn);
    }
};
