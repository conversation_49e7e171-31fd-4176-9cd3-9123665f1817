"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_cognito_identity_provider_1 = require("@aws-sdk/client-cognito-identity-provider");
// Configure AWS SDK v3 client
const cognitoClient = new client_cognito_identity_provider_1.CognitoIdentityProviderClient({
    region: process.env.AWS_REGION || 'us-west-2'
});
// Helper function to generate policy
const generatePolicy = (principalId, effect, resource, context = {}) => {
    const authResponse = {
        principalId: principalId
    };
    if (effect && resource) {
        const policyDocument = {
            Version: '2012-10-17',
            Statement: [
                {
                    Action: 'execute-api:Invoke',
                    Effect: effect,
                    Resource: resource
                }
            ]
        };
        authResponse.policyDocument = policyDocument;
    }
    // Add context to be passed to the lambda function
    if (Object.keys(context).length > 0) {
        authResponse.context = context;
    }
    return authResponse;
};
// Main authorizer handler
const handler = async (event) => {
    console.log('Authorizer event:', JSON.stringify(event, null, 2));
    try {
        // Extract token from the authorization header
        const token = event.authorizationToken;
        if (!token) {
            console.log('No authorization token provided');
            throw new Error('Unauthorized');
        }
        // Remove 'Bearer ' prefix if present
        const accessToken = token.replace(/^Bearer\s+/, '');
        if (!accessToken) {
            console.log('No access token found after removing Bearer prefix');
            throw new Error('Unauthorized');
        }
        try {
            // Validate token with Cognito using AWS SDK v3
            const getUserCommand = new client_cognito_identity_provider_1.GetUserCommand({
                AccessToken: accessToken
            });
            const cognitoUser = await cognitoClient.send(getUserCommand);
            console.log('Cognito user:', cognitoUser);
            // Extract user information from Cognito attributes
            const userAttributes = {};
            if (cognitoUser.UserAttributes) {
                cognitoUser.UserAttributes.forEach((attr) => {
                    if (attr.Name && attr.Value) {
                        userAttributes[attr.Name] = attr.Value;
                    }
                });
            }
            // Create context to pass to the lambda function using Cognito data
            const context = {
                userId: userAttributes.sub || '', // Use Cognito sub as the primary user ID
                email: userAttributes.email || '',
                username: cognitoUser.Username || '',
                cognitoUserId: cognitoUser.Username || '',
                sub: userAttributes.sub || '',
                firstName: userAttributes.given_name || '',
                lastName: userAttributes.family_name || ''
            };
            console.log('User context:', context);
            // Generate allow policy for all resources in this API
            // Use wildcard to allow access to all methods and paths in this API
            const apiArn = event.methodArn.split('/').slice(0, 2).join('/') + '/*';
            return generatePolicy(userAttributes.sub, 'Allow', apiArn, context);
        }
        catch (cognitoError) {
            console.error('Token validation error:', cognitoError);
            throw new Error('Unauthorized');
        }
    }
    catch (error) {
        console.error('Authorizer error:', error);
        // Return deny policy for any error
        return generatePolicy('user', 'Deny', event.methodArn);
    }
};
exports.handler = handler;
//# sourceMappingURL=data:application/json;base64,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