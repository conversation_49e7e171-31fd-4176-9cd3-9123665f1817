import { APIGatewayTokenAuthorizerEvent } from 'aws-lambda';
interface PolicyDocument {
    Version: string;
    Statement: PolicyStatement[];
}
interface PolicyStatement {
    Action: string;
    Effect: 'Allow' | 'Deny';
    Resource: string;
}
interface AuthResponse {
    principalId: string;
    policyDocument?: PolicyDocument;
    context?: Record<string, string>;
}
export declare const handler: (event: APIGatewayTokenAuthorizerEvent) => Promise<AuthResponse>;
export {};
