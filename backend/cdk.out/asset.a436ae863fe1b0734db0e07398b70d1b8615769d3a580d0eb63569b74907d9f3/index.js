"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
// Configure AWS SDK v3 client
const dynamoClient = new client_dynamodb_1.DynamoDBClient({
    region: process.env.AWS_REGION || 'us-west-2'
});
const dynamodb = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamoClient);
// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});
// Health check function
const healthCheck = async () => {
    try {
        const timestamp = new Date().toISOString();
        const environment = process.env.ENVIRONMENT || 'development';
        // Test DynamoDB connection (skip for local development)
        let dbStatus = 'skipped';
        if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
            try {
                const scanCommand = new lib_dynamodb_1.ScanCommand({
                    TableName: process.env.USERS_TABLE || 'gameflex-development-Users',
                    Limit: 1
                });
                await dynamodb.send(scanCommand);
                dbStatus = 'healthy';
            }
            catch (error) {
                dbStatus = 'unhealthy';
                console.error('DynamoDB health check failed:', error);
            }
        }
        const healthData = {
            status: 'healthy',
            timestamp,
            environment,
            version: '1.0.0',
            services: {
                database: dbStatus,
                api: 'healthy'
            },
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            environment_variables: {
                USER_POOL_ID: process.env.USER_POOL_ID,
                USER_POOL_CLIENT_ID: process.env.USER_POOL_CLIENT_ID,
                USERS_TABLE: process.env.USERS_TABLE,
                POSTS_TABLE: process.env.POSTS_TABLE,
                MEDIA_BUCKET: process.env.MEDIA_BUCKET
            }
        };
        return createResponse(200, healthData);
    }
    catch (error) {
        console.error('Health check error:', error);
        const errorResponse = {
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error'
        };
        return createResponse(500, errorResponse);
    }
};
// Main handler
const handler = async (event) => {
    console.log('Health check event:', JSON.stringify(event, null, 2));
    const { httpMethod, path } = event;
    if (httpMethod === 'GET' && path === '/health') {
        return await healthCheck();
    }
    else {
        return createResponse(404, { error: 'Not found' });
    }
};
exports.handler = handler;
//# sourceMappingURL=data:application/json;base64,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