{"name": "gameflex-health-lambda", "version": "1.0.0", "description": "GameFlex Health Check Lambda Function", "main": "index.ts", "scripts": {"build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.0.0", "@aws-sdk/lib-dynamodb": "^3.0.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "keywords": ["aws", "lambda", "health", "dynamodb", "typescript"], "author": "GameFlex", "license": "MIT"}