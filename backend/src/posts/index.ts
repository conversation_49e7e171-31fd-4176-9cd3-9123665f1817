import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, Scan<PERSON>ommand, GetCommand, PutCommand, UpdateCommand, DeleteCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { S3Client } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Configure AWS SDK v3 clients
const dynamoClient = new DynamoDBClient({
    region: process.env.AWS_REGION || 'us-east-1'
});

const dynamodb = DynamoDBDocumentClient.from(dynamoClient);
const s3 = new S3Client({
    region: process.env.AWS_REGION || 'us-east-1'
});

const POSTS_TABLE = process.env.POSTS_TABLE;
const MEDIA_TABLE = process.env.MEDIA_TABLE;
const COMMENTS_TABLE = process.env.COMMENTS_TABLE;
const LIKES_TABLE = process.env.LIKES_TABLE;
const USERS_TABLE = process.env.USERS_TABLE;
const MEDIA_BUCKET = process.env.MEDIA_BUCKET;

// TypeScript interfaces
interface Post {
    id: string;
    title?: string;
    content: string;
    mediaId?: string;
    media_id?: string;
    authorId: string;
    userId: string;
    likes: number;
    comments: number;
    reflexes: number;
    status: 'draft' | 'uploading_media' | 'published';
    active: boolean;
    createdAt: string;
    updatedAt: string;
    media?: MediaItem;
    isLikedByCurrentUser?: boolean;
}

interface MediaItem {
    id: string;
    status: string;
    [key: string]: any;
}

interface Comment {
    id: string;
    post_id: string;
    user_id: string;
    content: string;
    like_count: number;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    username?: string;
    displayName?: string;
    avatarUrl?: string;
}

interface User {
    id: string;
    username: string;
    displayName: string;
    avatarUrl?: string;
}

interface Like {
    postId: string;
    userId: string;
    createdAt: string;
}

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Get all posts
const getPosts = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get current user ID from authorizer context
        const currentUserId = getUserIdFromContext(event);

        const scanCommand = new ScanCommand({
            TableName: POSTS_TABLE
        });
        const result = await dynamodb.send(scanCommand);

        // Filter out inactive posts and posts that are not published
        const activePosts = (result.Items || []).filter((post: any) =>
            post.active === true && post.status === 'published'
        );

        // Sort posts by createdAt descending
        const posts = activePosts.sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        // Fetch media data and like status for posts
        const postsWithMediaAndLikes = await Promise.all(posts.map(async (post: any) => {
            // Fetch media data if post has mediaId or media_id
            const mediaId = post.mediaId || post.media_id;
            if (mediaId) {
                try {
                    const getMediaCommand = new GetCommand({
                        TableName: MEDIA_TABLE,
                        Key: { id: mediaId }
                    });
                    const mediaResult = await dynamodb.send(getMediaCommand);

                    if (mediaResult.Item) {
                        post.media = mediaResult.Item;
                    }
                } catch (error) {
                    console.error(`Failed to fetch media for post ${post.id}:`, error);
                    // Continue without media data if fetch fails
                }
            }

            // Check if current user has liked this post
            let isLikedByCurrentUser = false;
            if (currentUserId) {
                try {
                    const getLikeCommand = new GetCommand({
                        TableName: LIKES_TABLE,
                        Key: {
                            postId: post.id,
                            userId: currentUserId
                        }
                    });
                    const likeResult = await dynamodb.send(getLikeCommand);

                    isLikedByCurrentUser = !!likeResult.Item;
                } catch (error) {
                    console.error(`Failed to check like status for post ${post.id}:`, error);
                    // Continue with false if check fails
                }
            }

            // Add the like status to the post
            post.isLikedByCurrentUser = isLikedByCurrentUser;

            return post;
        }));

        return createResponse(200, {
            posts: postsWithMediaAndLikes,
            count: postsWithMediaAndLikes.length
        });

    } catch (error) {
        console.error('GetPosts error:', error);
        return createResponse(500, { error: 'Failed to get posts', details: (error as Error).message });
    }
};

// Get single post
const getPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Get current user ID from authorizer context
        const currentUserId = getUserIdFromContext(event);

        const getPostCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const result = await dynamodb.send(getPostCommand);

        if (!result.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        const post = result.Item as any;

        // Fetch media data if post has media_id
        if (post.media_id) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: post.media_id }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (mediaResult.Item) {
                    post.media = mediaResult.Item;
                }
            } catch (error) {
                console.error(`Failed to fetch media for post ${post.id}:`, error);
                // Continue without media data if fetch fails
            }
        }

        // Check if current user has liked this post
        let isLikedByCurrentUser = false;
        if (currentUserId) {
            try {
                const getLikeCommand = new GetCommand({
                    TableName: LIKES_TABLE,
                    Key: {
                        postId: post.id,
                        userId: currentUserId
                    }
                });
                const likeResult = await dynamodb.send(getLikeCommand);

                isLikedByCurrentUser = !!likeResult.Item;
            } catch (error) {
                console.error(`Failed to check like status for post ${post.id}:`, error);
                // Continue with false if check fails
            }
        }

        // Add the like status to the post
        post.isLikedByCurrentUser = isLikedByCurrentUser;

        return createResponse(200, { post });

    } catch (error) {
        console.error('GetPost error:', error);
        return createResponse(500, { error: 'Failed to get post', details: (error as Error).message });
    }
};

// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return (event.requestContext.authorizer as any).userId;
    }
    return null;
};

// Create draft post (first step of multi-step creation)
const createDraftPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { title, content } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!content) {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        const postId = uuidv4();
        const post: any = {
            id: postId,
            title: title || null,
            content,
            mediaId: null, // Will be set later if media is uploaded
            authorId: authorId,
            userId: authorId, // Keep for backwards compatibility
            likes: 0,
            comments: 0,
            reflexes: 0,
            status: 'draft', // Status: draft, uploading_media, published
            active: false, // Not active until published
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        const putCommand = new PutCommand({
            TableName: POSTS_TABLE,
            Item: post
        });
        await dynamodb.send(putCommand);

        return createResponse(201, {
            message: 'Draft post created successfully',
            post
        });

    } catch (error) {
        console.error('CreateDraftPost error:', error);
        return createResponse(500, { error: 'Failed to create draft post', details: (error as Error).message });
    }
};

// Create post (legacy endpoint - now creates and publishes immediately)
const createPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { title, content, media_id, mediaId } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!content) {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Support both media_id and mediaId field names
        const finalMediaId = media_id || mediaId;

        // If media_id is provided, verify it exists
        if (finalMediaId) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: finalMediaId }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (!mediaResult.Item) {
                    return createResponse(400, { error: 'Invalid media_id: media not found' });
                }
            } catch (error) {
                console.error('Error verifying media:', error);
                return createResponse(400, { error: 'Failed to verify media_id' });
            }
        }

        const postId = uuidv4();
        const post: any = {
            id: postId,
            title: title || null,
            content,
            mediaId: finalMediaId || null,
            authorId: authorId,
            userId: authorId, // Keep for backwards compatibility
            likes: 0,
            comments: 0,
            reflexes: 0,
            status: 'published', // Status: draft, uploading_media, published
            active: true, // Final switch to show in feeds
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        const putCommand = new PutCommand({
            TableName: POSTS_TABLE,
            Item: post
        });
        await dynamodb.send(putCommand);

        // Fetch media data if media_id was provided
        if (finalMediaId) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: finalMediaId }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (mediaResult.Item) {
                    post.media = mediaResult.Item;
                }
            } catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }

        return createResponse(201, {
            message: 'Post created successfully',
            post
        });

    } catch (error) {
        console.error('CreatePost error:', error);
        return createResponse(500, { error: 'Failed to create post', details: (error as Error).message });
    }
};

// Update post
const updatePost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { title, content, media_id } = JSON.parse(event.body);

        // If media_id is provided, verify it exists
        if (media_id) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: media_id }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (!mediaResult.Item) {
                    return createResponse(400, { error: 'Invalid media_id: media not found' });
                }
            } catch (error) {
                console.error('Error verifying media:', error);
                return createResponse(400, { error: 'Failed to verify media_id' });
            }
        }

        const updateExpression: string[] = [];
        const expressionAttributeValues: Record<string, any> = {};
        const expressionAttributeNames: Record<string, string> = {};

        if (title !== undefined) {
            updateExpression.push('#title = :title');
            expressionAttributeNames['#title'] = 'title';
            expressionAttributeValues[':title'] = title;
        }

        if (content !== undefined) {
            updateExpression.push('#content = :content');
            expressionAttributeNames['#content'] = 'content';
            expressionAttributeValues[':content'] = content;
        }

        if (media_id !== undefined) {
            updateExpression.push('media_id = :media_id');
            expressionAttributeValues[':media_id'] = media_id;
        }

        updateExpression.push('updated_at = :updated_at');
        expressionAttributeValues[':updated_at'] = new Date().toISOString();

        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: `SET ${updateExpression.join(', ')}`,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);

        const updatedPost = result.Attributes as any;

        // Fetch media data if post has media_id
        if (updatedPost.media_id) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: updatedPost.media_id }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (mediaResult.Item) {
                    updatedPost.media = mediaResult.Item;
                }
            } catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }

        return createResponse(200, {
            message: 'Post updated successfully',
            post: updatedPost
        });

    } catch (error) {
        console.error('UpdatePost error:', error);
        return createResponse(500, { error: 'Failed to update post', details: (error as Error).message });
    }
};

// Attach media to draft post
const attachMediaToPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { media_id } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!media_id) {
            return createResponse(400, { error: 'media_id is required' });
        }

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Verify media exists and is uploaded
        const getMediaCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id: media_id }
        });
        const mediaResult = await dynamodb.send(getMediaCommand);

        if (!mediaResult.Item) {
            return createResponse(400, { error: 'Invalid media_id: media not found' });
        }

        if (mediaResult.Item.status !== 'uploaded') {
            return createResponse(400, { error: 'Media upload not completed' });
        }

        // Get the post and verify ownership
        const getPostCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(getPostCommand);

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        if ((postResult.Item as any).authorId !== authorId) {
            return createResponse(403, { error: 'Not authorized to modify this post' });
        }

        // Update post with media and change status to uploading_media
        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET media_id = :media_id, #status = :status, updated_at = :updated_at',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':media_id': media_id,
                ':status': 'uploading_media',
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);

        return createResponse(200, {
            message: 'Media attached to post successfully',
            post: result.Attributes
        });

    } catch (error) {
        console.error('AttachMediaToPost error:', error);
        return createResponse(500, { error: 'Failed to attach media to post', details: (error as Error).message });
    }
};

// Publish post (final step)
const publishPost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get the post and verify ownership
        const getPostCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(getPostCommand);

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        if ((postResult.Item as any).authorId !== authorId) {
            return createResponse(403, { error: 'Not authorized to modify this post' });
        }

        // If post has media, verify it's uploaded
        if ((postResult.Item as any).media_id) {
            const getMediaCommand = new GetCommand({
                TableName: MEDIA_TABLE,
                Key: { id: (postResult.Item as any).media_id }
            });
            const mediaResult = await dynamodb.send(getMediaCommand);

            if (!mediaResult.Item || mediaResult.Item.status !== 'uploaded') {
                return createResponse(400, { error: 'Media upload not completed' });
            }
        }

        // Update post to published and active
        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET #status = :status, active = :active, updated_at = :updated_at',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': 'published',
                ':active': true,
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);

        // Fetch media data if post has media_id
        if ((result.Attributes as any).media_id) {
            try {
                const getMediaCommand = new GetCommand({
                    TableName: MEDIA_TABLE,
                    Key: { id: (result.Attributes as any).media_id }
                });
                const mediaResult = await dynamodb.send(getMediaCommand);

                if (mediaResult.Item) {
                    (result.Attributes as any).media = mediaResult.Item;
                }
            } catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }

        return createResponse(200, {
            message: 'Post published successfully',
            post: result.Attributes
        });

    } catch (error) {
        console.error('PublishPost error:', error);
        return createResponse(500, { error: 'Failed to publish post', details: (error as Error).message });
    }
};

// Delete post
const deletePost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        const deleteCommand = new DeleteCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        await dynamodb.send(deleteCommand);

        return createResponse(200, { message: 'Post deleted successfully' });

    } catch (error) {
        console.error('DeletePost error:', error);
        return createResponse(500, { error: 'Failed to delete post', details: (error as Error).message });
    }
};

// Like post
const likePost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Check if already liked
        const getLikeCommand = new GetCommand({
            TableName: LIKES_TABLE,
            Key: { postId: id, userId: userId }
        });
        const existingLike = await dynamodb.send(getLikeCommand);

        if (existingLike.Item) {
            return createResponse(400, { error: 'Post already liked' });
        }

        // Add like
        const putLikeCommand = new PutCommand({
            TableName: LIKES_TABLE,
            Item: {
                postId: id,
                userId: userId,
                createdAt: new Date().toISOString()
            }
        });
        await dynamodb.send(putLikeCommand);

        // Update post likes count
        const updatePostCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updatePostCommand);

        return createResponse(200, { message: 'Post liked successfully' });

    } catch (error) {
        console.error('LikePost error:', error);
        return createResponse(500, { error: 'Failed to like post', details: (error as Error).message });
    }
};

// Unlike post
const unlikePost = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Remove like
        const deleteLikeCommand = new DeleteCommand({
            TableName: LIKES_TABLE,
            Key: { postId: id, userId: userId }
        });
        await dynamodb.send(deleteLikeCommand);

        // Update post likes count
        const updatePostCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updatePostCommand);

        return createResponse(200, { message: 'Post unliked successfully' });

    } catch (error) {
        console.error('UnlikePost error:', error);
        return createResponse(500, { error: 'Failed to unlike post', details: (error as Error).message });
    }
};

// Get comments for a post
const getComments = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        // Query comments for the post
        const queryCommand = new QueryCommand({
            TableName: COMMENTS_TABLE,
            IndexName: 'postId-index',
            KeyConditionExpression: 'postId = :postId',
            ExpressionAttributeValues: {
                ':postId': id
            }
        });
        const result = await dynamodb.send(queryCommand);

        // Sort comments by createdAt ascending (oldest first)
        const comments = (result.Items || []).sort((a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

        // Fetch user data for each comment
        const commentsWithUserData = await Promise.all(comments.map(async (comment: any) => {
            try {
                const getUserCommand = new GetCommand({
                    TableName: USERS_TABLE,
                    Key: { id: comment.userId }
                });
                const userResult = await dynamodb.send(getUserCommand);

                if (userResult.Item) {
                    comment.username = userResult.Item.username;
                    comment.displayName = userResult.Item.displayName;
                    comment.avatarUrl = userResult.Item.avatarUrl;
                }
            } catch (error) {
                console.error(`Failed to fetch user data for comment ${comment.id}:`, error);
                // Continue without user data if fetch fails
            }
            return comment;
        }));

        return createResponse(200, {
            comments: commentsWithUserData,
            count: commentsWithUserData.length
        });

    } catch (error) {
        console.error('GetComments error:', error);
        return createResponse(500, { error: 'Failed to get comments', details: (error as Error).message });
    }
};

// Create a comment
const createComment = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Post ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { content } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!content || content.trim() === '') {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Verify the post exists
        const getPostCommand = new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id }
        });
        const postResult = await dynamodb.send(getPostCommand);

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        const commentId = uuidv4();
        const comment: any = {
            id: commentId,
            postId: id,
            userId: userId,
            content: content.trim(),
            likeCount: 0,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // Create the comment
        const putCommentCommand = new PutCommand({
            TableName: COMMENTS_TABLE,
            Item: comment
        });
        await dynamodb.send(putCommentCommand);

        // Update post comments count
        const updatePostCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD comments :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        });
        await dynamodb.send(updatePostCommand);

        // Fetch user data for the response
        try {
            const getUserCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userId }
            });
            const userResult = await dynamodb.send(getUserCommand);

            if (userResult.Item) {
                comment.username = userResult.Item.username;
                comment.displayName = userResult.Item.displayName;
                comment.avatarUrl = userResult.Item.avatarUrl;
            }
        } catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }

        return createResponse(201, {
            message: 'Comment created successfully',
            comment
        });

    } catch (error) {
        console.error('CreateComment error:', error);
        return createResponse(500, { error: 'Failed to create comment', details: (error as Error).message });
    }
};

// Update a comment
const updateComment = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Comment ID is required' });
        }

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { content } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!content || content.trim() === '') {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get the comment to verify ownership
        const getCommentCommand = new GetCommand({
            TableName: COMMENTS_TABLE,
            Key: { id }
        });
        const commentResult = await dynamodb.send(getCommentCommand);

        if (!commentResult.Item) {
            return createResponse(404, { error: 'Comment not found' });
        }

        // Verify the user owns the comment
        if ((commentResult.Item as any).userId !== userId) {
            return createResponse(403, { error: 'You can only update your own comments' });
        }

        // Update the comment
        const updateCommand = new UpdateCommand({
            TableName: COMMENTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET content = :content, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':content': content.trim(),
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });
        const result = await dynamodb.send(updateCommand);

        const updatedComment = result.Attributes as any;

        // Fetch user data for the response
        try {
            const getUserCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: userId }
            });
            const userResult = await dynamodb.send(getUserCommand);

            if (userResult.Item) {
                updatedComment.username = userResult.Item.username;
                updatedComment.displayName = userResult.Item.displayName;
                updatedComment.avatarUrl = userResult.Item.avatarUrl;
            }
        } catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }

        return createResponse(200, {
            message: 'Comment updated successfully',
            comment: updatedComment
        });

    } catch (error) {
        console.error('UpdateComment error:', error);
        return createResponse(500, { error: 'Failed to update comment', details: (error as Error).message });
    }
};

// Delete a comment
const deleteComment = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        const { id } = event.pathParameters || {};

        if (!id) {
            return createResponse(400, { error: 'Comment ID is required' });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get the comment to verify ownership and get post_id
        const getCommentCommand = new GetCommand({
            TableName: COMMENTS_TABLE,
            Key: { id }
        });
        const commentResult = await dynamodb.send(getCommentCommand);

        if (!commentResult.Item) {
            return createResponse(404, { error: 'Comment not found' });
        }

        // Verify the user owns the comment
        if ((commentResult.Item as any).userId !== userId) {
            return createResponse(403, { error: 'You can only delete your own comments' });
        }

        const postId = (commentResult.Item as any).postId;

        // Delete the comment
        const deleteCommentCommand = new DeleteCommand({
            TableName: COMMENTS_TABLE,
            Key: { id }
        });
        await dynamodb.send(deleteCommentCommand);

        // Update post comments count
        const updatePostCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId },
            UpdateExpression: 'ADD comments :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        });
        await dynamodb.send(updatePostCommand);

        return createResponse(200, { message: 'Comment deleted successfully' });

    } catch (error) {
        console.error('DeleteComment error:', error);
        return createResponse(500, { error: 'Failed to delete comment', details: (error as Error).message });
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        // Posts routes
        if (httpMethod === 'GET' && path === '/posts') {
            return await getPosts(event);
        } else if (httpMethod === 'POST' && path === '/posts/draft') {
            return await createDraftPost(event);
        } else if (httpMethod === 'POST' && path === '/posts') {
            return await createPost(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like') && !path.includes('/media') && !path.includes('/publish')) {
            return await getPost(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && path.includes('/media')) {
            return await attachMediaToPost(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && path.includes('/publish')) {
            return await publishPost(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like') && !path.includes('/media') && !path.includes('/publish')) {
            return await updatePost(event);
        } else if (httpMethod === 'DELETE' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like')) {
            return await deletePost(event);
        }
        // Like routes
        else if (httpMethod === 'POST' && path.includes('/like')) {
            return await likePost(event);
        } else if (httpMethod === 'DELETE' && path.includes('/like')) {
            return await unlikePost(event);
        }
        // Comments routes
        else if (httpMethod === 'GET' && path.includes('/comments') && pathParameters && pathParameters.id) {
            return await getComments(event);
        } else if (httpMethod === 'POST' && path.includes('/comments') && pathParameters && pathParameters.id) {
            return await createComment(event);
        } else if (httpMethod === 'PUT' && path.startsWith('/comments/') && pathParameters && pathParameters.id) {
            return await updateComment(event);
        } else if (httpMethod === 'DELETE' && path.startsWith('/comments/') && pathParameters && pathParameters.id) {
            return await deleteComment(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: (error as Error).message });
    }
};
