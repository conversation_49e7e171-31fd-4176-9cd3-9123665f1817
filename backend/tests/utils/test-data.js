/**
 * Test data generators and mock data for GameFlex SAM Backend tests
 */

const { v4: uuidv4 } = require('uuid');

class TestDataGenerator {
  static createUser(overrides = {}) {
    const now = new Date().toISOString();
    const username = `testuser_${Math.random().toString(36).substring(7)}`;

    return {
      id: uuidv4(),
      cognitoUserId: `cognito_${uuidv4()}`,
      email: `${username}@test.gameflex.com`,
      username,
      firstName: 'Test',
      lastName: 'User',
      createdAt: now,
      updatedAt: now,
      ...overrides
    };
  }

  static createPost(userId, overrides = {}) {
    const now = new Date().toISOString();

    return {
      id: uuidv4(),
      title: `Test Post ${Math.random().toString(36).substring(7)}`,
      content: 'This is a test post content',
      userId: userId || uuidv4(),
      authorId: userId || uuidv4(),
      mediaUrl: null,
      mediaId: null,
      likes: 0,
      comments: 0,
      status: 'published',
      active: true,
      createdAt: now,
      updatedAt: now,
      ...overrides
    };
  }

  static createMedia(userId, overrides = {}) {
    const now = new Date().toISOString();
    const filename = `test_image_${Math.random().toString(36).substring(7)}.jpg`;

    return {
      id: uuidv4(),
      fileName: filename,
      fileType: 'image/jpeg',
      fileSize: 1024000,
      mediaType: 'image',
      userId: userId || uuidv4(),
      s3Key: `media/${filename}`,
      bucketName: 'gameflex-media-test',
      url: `http://localhost:4566/gameflex-media-test/media/${filename}`,
      status: 'uploaded',
      createdAt: now,
      updatedAt: now,
      ...overrides
    };
  }

  static createUserProfile(userId, overrides = {}) {
    const now = new Date().toISOString();

    return {
      userId: userId || uuidv4(),
      bio: 'Test user bio',
      location: 'Test City',
      website: 'https://test.example.com',
      avatarUrl: null,
      followersCount: 0,
      followingCount: 0,
      postsCount: 0,
      createdAt: now,
      updatedAt: now,
      ...overrides
    };
  }

  static createReflex(overrides = {}) {
    const now = new Date().toISOString();

    return {
      id: uuidv4(),
      postId: uuidv4(),
      userId: uuidv4(),
      mediaId: null,
      flareData: { type: 'fire', intensity: 3 },
      textOverlay: null,
      reflexType: 'flare',
      isActive: true,
      createdAt: now,
      updatedAt: now,
      ...overrides
    };
  }

  static createAuthRequest(overrides = {}) {
    return {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User',
      ...overrides
    };
  }

  static createAPIGatewayEvent(overrides = {}) {
    return {
      httpMethod: 'GET',
      path: '/test',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      queryStringParameters: null,
      pathParameters: null,
      body: null,
      isBase64Encoded: false,
      requestContext: {
        requestId: uuidv4(),
        stage: 'test',
        resourcePath: '/test',
        httpMethod: 'GET',
        requestTime: new Date().toISOString(),
        protocol: 'HTTP/1.1',
        resourceId: 'test-resource',
        accountId: '************',
        apiId: 'test-api-id',
        identity: {
          sourceIp: '127.0.0.1',
          userAgent: 'test-agent'
        }
      },
      ...overrides
    };
  }

  static createLambdaContext() {
    return {
      callbackWaitsForEmptyEventLoop: false,
      functionName: 'test-function',
      functionVersion: '$LATEST',
      invokedFunctionArn: 'arn:aws:lambda:us-east-1:************:function:test-function',
      memoryLimitInMB: '256',
      awsRequestId: uuidv4(),
      logGroupName: '/aws/lambda/test-function',
      logStreamName: '2024/01/01/[$LATEST]test',
      getRemainingTimeInMillis: () => 30000,
      done: () => { },
      fail: () => { },
      succeed: () => { }
    };
  }

  static createCognitoUser(overrides = {}) {
    return {
      User: {
        Username: '<EMAIL>',
        Attributes: [
          { Name: 'email', Value: '<EMAIL>' },
          { Name: 'email_verified', Value: 'true' },
          { Name: 'given_name', Value: 'Test' },
          { Name: 'family_name', Value: 'User' }
        ],
        UserCreateDate: new Date(),
        UserLastModifiedDate: new Date(),
        Enabled: true,
        UserStatus: 'CONFIRMED'
      },
      ...overrides
    };
  }

  static createCognitoAuthResult(overrides = {}) {
    return {
      AuthenticationResult: {
        AccessToken: 'mock-access-token',
        RefreshToken: 'mock-refresh-token',
        IdToken: 'mock-id-token',
        TokenType: 'Bearer',
        ExpiresIn: 3600
      },
      ...overrides
    };
  }

  static createChannel(overrides = {}) {
    const now = new Date().toISOString();

    return {
      id: uuidv4(),
      name: 'Test Channel',
      description: 'A test channel for gaming discussions',
      ownerId: uuidv4(),
      isPublic: true,
      isActive: true,
      memberCount: 1,
      iconMediaId: null,
      createdAt: now,
      updatedAt: now,
      ...overrides
    };
  }

  static createChannelMember(overrides = {}) {
    const now = new Date().toISOString();

    return {
      channelId: uuidv4(),
      userId: uuidv4(),
      role: 'member',
      joinedAt: now,
      ...overrides
    };
  }

  static createChannelRequest(overrides = {}) {
    return {
      name: 'New Test Channel',
      description: 'A new channel for testing',
      isPublic: true,
      ...overrides
    };
  }

  static createChannelWithMembership(overrides = {}) {
    const channel = this.createChannel(overrides.channel || {});
    const membership = this.createChannelMember({
      channelId: channel.id,
      userId: overrides.userId || uuidv4(),
      role: overrides.role || 'member'
    });

    return {
      ...channel,
      ownerUsername: 'testuser',
      ownerDisplayName: 'Test User',
      ownerAvatarUrl: null,
      iconUrl: null,
      isUserMember: true,
      userRole: membership.role,
      ...overrides.response
    };
  }
}

// Pre-defined test users for consistent testing
const TEST_USERS = {
  VALID_USER: TestDataGenerator.createUser({
    email: '<EMAIL>',
    username: 'devuser',
    firstName: 'Dev',
    lastName: 'User'
  }),

  ADMIN_USER: TestDataGenerator.createUser({
    email: '<EMAIL>',
    username: 'admin',
    firstName: 'Admin',
    lastName: 'User'
  }),

  INACTIVE_USER: TestDataGenerator.createUser({
    email: '<EMAIL>',
    username: 'inactive',
    firstName: 'Inactive',
    lastName: 'User'
  })
};

const TEST_CREDENTIALS = {
  VALID: {
    email: '<EMAIL>',
    password: 'DevPassword123!'
  },

  INVALID_EMAIL: {
    email: '<EMAIL>',
    password: 'SomePassword123!'
  },

  INVALID_PASSWORD: {
    email: '<EMAIL>',
    password: 'WrongPassword123!'
  }
};

// Pre-defined test channels for consistent testing
const TEST_CHANNELS = {
  PUBLIC_CHANNEL: TestDataGenerator.createChannel({
    name: 'Gaming Hub',
    description: 'A public channel for gaming discussions',
    ownerId: TEST_USERS.VALID_USER.id,
    isPublic: true,
    memberCount: 5
  }),

  PRIVATE_CHANNEL: TestDataGenerator.createChannel({
    name: 'Private Gaming',
    description: 'A private channel for exclusive gaming content',
    ownerId: TEST_USERS.ADMIN_USER.id,
    isPublic: false,
    memberCount: 2
  }),

  EMPTY_CHANNEL: TestDataGenerator.createChannel({
    name: 'New Channel',
    description: 'A newly created channel with no members',
    ownerId: TEST_USERS.VALID_USER.id,
    isPublic: true,
    memberCount: 1
  })
};

module.exports = {
  TestDataGenerator,
  TEST_USERS,
  TEST_CREDENTIALS,
  TEST_CHANNELS
};
