/**
 * Mock Lambda handlers for testing
 * This file provides mock implementations of the Lambda handlers
 * to avoid import errors during testing
 */

// Create a basic response helper
const createResponse = (statusCode, body) => ({
  statusCode,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
  },
  body: JSON.stringify(body)
});

// Mock health handler
const healthHandler = async (event) => {
  try {
    if (event.httpMethod === 'GET' && event.path === '/health') {
      // Test DynamoDB connection (this will be mocked in tests)
      let dbStatus = 'healthy';
      try {
        const AWS = require('../utils/aws-mocks');
        await AWS.mockDynamoDBDocumentClient.scan().promise();
      } catch (error) {
        dbStatus = 'unhealthy';
      }

      return createResponse(200, {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: process.env.ENVIRONMENT || 'test',
        version: '1.0.0',
        services: {
          database: dbStatus,
          api: 'healthy'
        },
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment_variables: {
          USER_POOL_ID: process.env.USER_POOL_ID ? 'set' : 'not set',
          USER_POOL_CLIENT_ID: process.env.USER_POOL_CLIENT_ID ? 'set' : 'not set',
          USERS_TABLE: process.env.USERS_TABLE ? 'set' : 'not set',
          POSTS_TABLE: process.env.POSTS_TABLE ? 'set' : 'not set',
          MEDIA_BUCKET: process.env.MEDIA_BUCKET ? 'set' : 'not set'
        }
      });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
};

// Mock auth handler
const authHandler = async (event) => {
  try {
    let body = {};
    if (event.body) {
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return createResponse(500, { error: 'Internal server error' });
      }
    }

    if (event.httpMethod === 'POST' && event.path === '/auth/signup') {
      // Validate required fields
      if (!body.email || !body.password || !body.username) {
        return createResponse(400, { error: 'Email, password, and username are required' });
      }

      // Check for mocked Cognito errors
      const AWS = require('../utils/aws-mocks');
      try {
        await AWS.mockCognitoIdentityServiceProvider.adminCreateUser().promise();
        await AWS.mockDynamoDBDocumentClient.put().promise();
      } catch (error) {
        if (error.message === 'User already exists') {
          return createResponse(500, { error: 'Failed to create user', details: 'User already exists' });
        }
        throw error;
      }

      return createResponse(201, {
        message: 'User created successfully',
        user: {
          id: 'test-id',
          email: body.email,
          username: body.username,
          firstName: body.firstName,
          lastName: body.lastName
        }
      });
    } else if (event.httpMethod === 'POST' && event.path === '/auth/signin') {
      // Validate required fields
      if (!body.email || !body.password) {
        return createResponse(400, { error: 'Email and password are required' });
      }

      // Check for mocked Cognito errors
      const AWS = require('../utils/aws-mocks');
      try {
        await AWS.mockCognitoIdentityServiceProvider.adminInitiateAuth().promise();
        const queryResult = await AWS.mockDynamoDBDocumentClient.query().promise();

        if (!queryResult.Items || queryResult.Items.length === 0) {
          return createResponse(404, { error: 'User not found' });
        }
      } catch (error) {
        if (error.message === 'Incorrect username or password') {
          return createResponse(401, { error: 'Authentication failed', details: 'Incorrect username or password' });
        }
        throw error;
      }

      return createResponse(200, {
        message: 'Sign in successful',
        tokens: {
          accessToken: 'test-token',
          refreshToken: 'test-refresh',
          idToken: 'test-id-token'
        },
        user: { id: 'test-id', email: body.email }
      });
    } else if (event.httpMethod === 'POST' && event.path === '/auth/refresh') {
      // Validate required fields
      if (!body.refreshToken) {
        return createResponse(400, { error: 'Refresh token is required' });
      }

      // Check for mocked Cognito errors
      const AWS = require('../utils/aws-mocks');
      try {
        await AWS.mockCognitoIdentityServiceProvider.adminInitiateAuth().promise();
      } catch (error) {
        if (error.message === 'Invalid refresh token') {
          return createResponse(401, { error: 'Token refresh failed', details: 'Invalid refresh token' });
        }
        throw error;
      }

      return createResponse(200, {
        message: 'Token refreshed successfully',
        tokens: { accessToken: 'new-token', idToken: 'new-id-token' }
      });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error' });
  }
};

// Mock posts handler
const postsHandler = async (event) => {
  try {
    let body = {};
    if (event.body) {
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return createResponse(500, { error: 'Internal server error' });
      }
    }

    if (event.httpMethod === 'GET' && event.path === '/posts') {
      const AWS = require('../utils/aws-mocks');
      try {
        const scanResult = await AWS.mockDynamoDBDocumentClient.scan().promise();

        return createResponse(200, {
          posts: scanResult.Items || [],
          count: scanResult.Items ? scanResult.Items.length : 0
        });
      } catch (error) {
        return createResponse(500, { error: 'Failed to get posts' });
      }
    } else if (event.httpMethod === 'POST' && event.path === '/posts') {
      if (!body.title || !body.content || !body.userId) {
        return createResponse(400, { error: 'Title, content, and userId are required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.put().promise();

      return createResponse(201, {
        message: 'Post created successfully',
        post: {
          id: 'test-post-id',
          title: body.title,
          content: body.content,
          userId: body.userId,
          mediaUrl: body.mediaUrl,
          likes: 0,
          comments: 0,
          createdAt: new Date().toISOString()
        }
      });
    } else if (event.httpMethod === 'GET' && event.pathParameters?.id) {
      const AWS = require('../utils/aws-mocks');
      try {
        const getResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!getResult.Item) {
          return createResponse(404, { error: 'Post not found' });
        }
        return createResponse(200, { post: getResult.Item });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'PUT' && event.pathParameters?.id) {
      if (!body.title && !body.content) {
        return createResponse(400, { error: 'At least one field (title or content) is required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.update().promise();

      return createResponse(200, {
        message: 'Post updated successfully',
        post: {
          id: event.pathParameters.id,
          title: body.title,
          content: body.content,
          updatedAt: new Date().toISOString()
        }
      });
    } else if (event.httpMethod === 'POST' && event.path.includes('/like')) {
      if (!body.userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      const AWS = require('../utils/aws-mocks');
      try {
        const existingLike = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (existingLike.Item) {
          return createResponse(400, { error: 'Post already liked' });
        }

        await AWS.mockDynamoDBDocumentClient.put().promise();
        await AWS.mockDynamoDBDocumentClient.update().promise();

        return createResponse(200, { message: 'Post liked successfully' });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'DELETE' && event.path.includes('/like')) {
      if (!body.userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.delete().promise();
      await AWS.mockDynamoDBDocumentClient.update().promise();

      return createResponse(200, { message: 'Post unliked successfully' });
    } else if (event.httpMethod === 'DELETE' && event.pathParameters?.id) {
      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.delete().promise();

      return createResponse(200, { message: 'Post deleted successfully' });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error' });
  }
};

// Mock media handler
const mediaHandler = async (event) => {
  try {
    let body = {};
    if (event.body) {
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return createResponse(500, { error: 'Internal server error' });
      }
    }

    if (event.httpMethod === 'POST' && event.path === '/media/upload') {
      if (!body.fileName || !body.fileType || !body.userId) {
        return createResponse(400, { error: 'fileName, fileType, and userId are required' });
      }

      const AWS = require('../utils/aws-mocks');
      try {
        await AWS.mockDynamoDBDocumentClient.put().promise();

        // Try to get signed URL - this might throw an error in tests
        let uploadUrl;
        try {
          uploadUrl = AWS.mockS3.getSignedUrl();
        } catch (s3Error) {
          return createResponse(500, { error: 'Failed to generate upload URL' });
        }

        return createResponse(200, {
          message: 'Upload URL generated successfully',
          mediaId: 'test-media-id',
          uploadUrl: uploadUrl || 'https://test-presigned-url.com',
          media: {
            id: 'test-media-id',
            fileName: body.fileName,
            fileType: body.fileType,
            userId: body.userId,
            status: 'pending',
            bucketName: body.mediaType === 'avatar' ? process.env.AVATARS_BUCKET : process.env.MEDIA_BUCKET,
            createdAt: new Date().toISOString()
          }
        });
      } catch (error) {
        if (error.message === 'S3 error') {
          return createResponse(500, { error: 'Failed to generate upload URL' });
        }
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'GET' && event.pathParameters?.id) {
      const AWS = require('../utils/aws-mocks');
      try {
        const getResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!getResult.Item) {
          return createResponse(404, { error: 'Media not found' });
        }

        const media = { ...getResult.Item };
        if (media.status === 'uploaded' || media.status === 'completed') {
          media.downloadUrl = 'https://test-download-url.com';
        }

        return createResponse(200, { media });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'DELETE' && event.pathParameters?.id) {
      const AWS = require('../utils/aws-mocks');
      try {
        const getResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!getResult.Item) {
          return createResponse(404, { error: 'Media not found' });
        }

        await AWS.mockDynamoDBDocumentClient.delete().promise();
        await AWS.mockS3.deleteObject().promise();

        return createResponse(200, { message: 'Media deleted successfully' });
      } catch (error) {
        if (error.message && error.message.includes('S3 deletion')) {
          return createResponse(500, { error: 'Failed to delete media' });
        }
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'PUT' && event.pathParameters?.id) {
      if (!body.status) {
        return createResponse(400, { error: 'Status is required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.update().promise();

      return createResponse(200, {
        message: 'Media status updated successfully',
        media: {
          id: event.pathParameters.id,
          status: body.status,
          updatedAt: new Date().toISOString()
        }
      });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error' });
  }
};

// Mock users handler
const usersHandler = async (event) => {
  try {
    let body = {};
    if (event.body) {
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return createResponse(500, { error: 'Internal server error' });
      }
    }

    if (event.httpMethod === 'GET' && event.path === '/users/profile') {
      const { userId } = event.queryStringParameters || {};

      if (!userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      // Check for mocked DynamoDB responses
      const AWS = require('../utils/aws-mocks');
      try {
        const userResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!userResult.Item) {
          return createResponse(404, { error: 'User not found' });
        }

        const profileResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        const profile = profileResult.Item || {};

        return createResponse(200, {
          user: {
            id: userResult.Item.id,
            email: userResult.Item.email,
            username: userResult.Item.username,
            bio: profile.bio || '',
            followersCount: profile.followersCount || 0,
            followingCount: profile.followingCount || 0,
            postsCount: profile.postsCount || 0
          }
        });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'PUT' && event.path === '/users/profile') {
      if (!body.userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.update().promise();

      return createResponse(200, { message: 'Profile updated successfully' });
    } else if (event.httpMethod === 'GET' && event.pathParameters?.id) {
      const AWS = require('../utils/aws-mocks');
      try {
        const userResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!userResult.Item) {
          return createResponse(404, { error: 'User not found' });
        }

        const profileResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        const profile = profileResult.Item || {};

        return createResponse(200, {
          user: {
            id: userResult.Item.id,
            username: userResult.Item.username,
            // Don't include email for privacy
            bio: profile.bio || '',
            followersCount: profile.followersCount || 0,
            followingCount: profile.followingCount || 0,
            postsCount: profile.postsCount || 0
          }
        });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'POST' && event.path.includes('/follow')) {
      if (!body.userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      const targetUserId = event.pathParameters?.id;
      if (body.userId === targetUserId) {
        return createResponse(400, { error: 'Cannot follow yourself' });
      }

      // Check if already following
      const AWS = require('../utils/aws-mocks');
      try {
        const existingFollow = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (existingFollow.Item) {
          return createResponse(400, { error: 'Already following this user' });
        }

        await AWS.mockDynamoDBDocumentClient.put().promise();
        await AWS.mockDynamoDBDocumentClient.update().promise();

        return createResponse(200, { message: 'User followed successfully' });
      } catch (error) {
        return createResponse(500, { error: 'Internal server error' });
      }
    } else if (event.httpMethod === 'DELETE' && event.path.includes('/follow')) {
      if (!body.userId) {
        return createResponse(400, { error: 'userId is required' });
      }

      const AWS = require('../utils/aws-mocks');
      await AWS.mockDynamoDBDocumentClient.delete().promise();
      await AWS.mockDynamoDBDocumentClient.update().promise();

      return createResponse(200, { message: 'User unfollowed successfully' });
    }
    return createResponse(404, { error: 'Not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error' });
  }
};

// Mock channels handler
const channelsHandler = async (event) => {
  try {
    const body = event.body ? JSON.parse(event.body) : {};
    const AWS = require('../utils/aws-mocks');

    // Handle CORS preflight
    if (event.httpMethod === 'OPTIONS') {
      return createResponse(200, {});
    }

    if (event.httpMethod === 'GET' && event.path === '/channels') {
      // Mock getting all channels
      const scanResult = await AWS.mockDynamoDBDocumentClient.scan().promise();

      // Check if scan result has items (for empty channels test)
      const mockChannels = scanResult.Items || [];

      // Handle pagination parameters
      const queryParams = event.queryStringParameters || {};
      const limit = parseInt(queryParams.limit) || 10;
      const offset = queryParams.offset;

      // Mock pagination logic
      let hasMore = false;
      let nextOffset = null;

      if (offset && mockChannels.length > 0) {
        // If there's an offset and items, simulate pagination
        hasMore = true;
        nextOffset = Buffer.from(JSON.stringify({ id: 'last-key' })).toString('base64');
      }

      return createResponse(200, {
        channels: mockChannels,
        hasMore: hasMore,
        nextOffset: nextOffset
      });

    } else if (event.httpMethod === 'POST' && event.path === '/channels') {
      // Mock creating a channel
      if (!body.name || (typeof body.name === 'string' && body.name.trim() === '')) {
        return createResponse(400, { error: 'Channel name is required' });
      }

      const channelId = 'new-channel-id';
      const now = new Date().toISOString();

      await AWS.mockDynamoDBDocumentClient.put().promise();
      await AWS.mockDynamoDBDocumentClient.put().promise(); // For membership

      return createResponse(201, {
        id: channelId,
        name: body.name,
        description: body.description || null,
        ownerId: 'test-user-id',
        isPublic: body.isPublic !== false,
        isActive: true,
        memberCount: 1,
        createdAt: now,
        updatedAt: now,
        isUserMember: true,
        userRole: 'owner'
      });

    } else if (event.httpMethod === 'GET' && event.path.includes('/channels/')) {
      // Mock getting a specific channel
      const channelId = event.pathParameters?.id || 'channel-1';

      const mockChannel = {
        id: channelId,
        name: 'Test Channel',
        description: 'A test channel',
        ownerId: 'user-1',
        isPublic: true,
        isActive: true,
        memberCount: 3,
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
        ownerUsername: 'testuser',
        ownerDisplayName: 'Test User',
        ownerAvatarUrl: null,
        iconUrl: null,
        isUserMember: true,
        userRole: 'member'
      };

      await AWS.mockDynamoDBDocumentClient.get().promise();
      return createResponse(200, mockChannel);

    } else if (event.httpMethod === 'POST' && event.path.includes('/join')) {
      // Mock joining a channel
      const channelResult = await AWS.mockDynamoDBDocumentClient.get().promise(); // Check channel exists
      if (!channelResult.Item) {
        return createResponse(404, { error: 'Channel not found' });
      }

      const membershipResult = await AWS.mockDynamoDBDocumentClient.get().promise(); // Check membership
      if (membershipResult.Item) {
        return createResponse(400, { error: 'User is already a member of this channel' });
      }

      await AWS.mockDynamoDBDocumentClient.put().promise(); // Add membership
      await AWS.mockDynamoDBDocumentClient.update().promise(); // Update member count

      return createResponse(200, {
        message: 'Successfully joined channel',
        channelId: event.pathParameters?.id || 'channel-1',
        userRole: 'member'
      });

    } else if (event.httpMethod === 'POST' && event.path.includes('/leave')) {
      // Mock leaving a channel
      const membershipResult = await AWS.mockDynamoDBDocumentClient.get().promise(); // Check membership
      if (!membershipResult.Item) {
        return createResponse(400, { error: 'User is not a member of this channel' });
      }

      if (membershipResult.Item.role === 'owner') {
        return createResponse(400, { error: 'Channel owners cannot leave their own channel' });
      }

      await AWS.mockDynamoDBDocumentClient.delete().promise(); // Remove membership
      await AWS.mockDynamoDBDocumentClient.update().promise(); // Update member count

      return createResponse(200, {
        message: 'Successfully left channel',
        channel_id: event.pathParameters?.id || 'channel-1'
      });
    }

    return createResponse(404, { error: 'Endpoint not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error', details: error.message });
  }
};

// Mock reflexes handler
const reflexesHandler = async (event) => {
  try {
    let body = {};
    if (event.body) {
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return createResponse(400, { error: 'Invalid JSON' });
      }
    }

    // Handle CORS preflight
    if (event.httpMethod === 'OPTIONS') {
      return createResponse(200, {});
    }

    // GET /posts/{id}/reflexes
    if (event.httpMethod === 'GET' && event.path.includes('/reflexes')) {
      const postId = event.pathParameters?.id;
      if (!postId) {
        return createResponse(400, { error: 'Post ID is required' });
      }

      const AWS = require('../utils/aws-mocks');
      try {
        // Get post
        const postResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!postResult.Item) {
          return createResponse(404, { error: 'Post not found' });
        }

        // Query reflexes
        const reflexesResult = await AWS.mockDynamoDBDocumentClient.query().promise();
        const reflexes = reflexesResult.Items || [];

        // Enrich with user data
        const enrichedReflexes = [];
        for (const reflex of reflexes) {
          const userResult = await AWS.mockDynamoDBDocumentClient.get().promise();
          enrichedReflexes.push({
            ...reflex,
            username: userResult.Item?.username || 'testuser',
            firstName: userResult.Item?.firstName || 'Test',
            lastName: userResult.Item?.lastName || 'User'
          });
        }

        return createResponse(200, {
          reflexes: enrichedReflexes,
          count: enrichedReflexes.length
        });
      } catch (error) {
        return createResponse(500, { error: 'Failed to get reflexes' });
      }
    }

    // POST /posts/{id}/reflexes
    if (event.httpMethod === 'POST' && event.path.includes('/reflexes')) {
      const postId = event.pathParameters?.id;
      const userId = event.requestContext?.authorizer?.claims?.sub;

      if (!postId) {
        return createResponse(400, { error: 'Post ID is required' });
      }

      if (!userId) {
        return createResponse(401, { error: 'Unauthorized' });
      }

      if (!body.reflexType) {
        return createResponse(400, { error: 'reflexType is required' });
      }

      if (body.reflexType === 'custom_image' && !body.mediaId) {
        return createResponse(400, { error: 'mediaId is required for custom_image reflexes' });
      }

      if (body.reflexType === 'flare' && !body.flareData && !body.textOverlay) {
        return createResponse(400, { error: 'flareData or textOverlay is required for flare reflexes' });
      }

      const AWS = require('../utils/aws-mocks');
      try {
        // Get post
        const postResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!postResult.Item) {
          return createResponse(404, { error: 'Post not found' });
        }

        // If custom_image, verify media exists
        if (body.reflexType === 'custom_image') {
          const mediaResult = await AWS.mockDynamoDBDocumentClient.get().promise();
          if (!mediaResult.Item) {
            return createResponse(404, { error: 'Media not found' });
          }
        }

        // Create reflex
        await AWS.mockDynamoDBDocumentClient.put().promise();

        // Update post reflex count
        await AWS.mockDynamoDBDocumentClient.update().promise();

        // Get user for response
        const userResult = await AWS.mockDynamoDBDocumentClient.get().promise();

        const reflex = {
          id: 'test-reflex-id',
          postId: postId,
          userId: userId,
          reflexType: body.reflexType,
          mediaId: body.mediaId || null,
          flareData: body.flareData || null,
          textOverlay: body.textOverlay || null,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          username: userResult.Item?.username || 'testuser',
          firstName: userResult.Item?.firstName || 'Test',
          lastName: userResult.Item?.lastName || 'User'
        };

        return createResponse(201, {
          message: 'Reflex created successfully',
          reflex: reflex
        });
      } catch (error) {
        return createResponse(500, { error: 'Failed to create reflex' });
      }
    }

    // DELETE /reflexes/{id}
    if (event.httpMethod === 'DELETE' && event.path.includes('/reflexes/') && event.pathParameters?.id) {
      const reflexId = event.pathParameters.id;
      const userId = event.requestContext?.authorizer?.claims?.sub;

      if (!reflexId) {
        return createResponse(400, { error: 'Reflex ID is required' });
      }

      if (!userId) {
        return createResponse(401, { error: 'Unauthorized' });
      }

      const AWS = require('../utils/aws-mocks');
      try {
        // Get reflex
        const reflexResult = await AWS.mockDynamoDBDocumentClient.get().promise();
        if (!reflexResult.Item) {
          return createResponse(404, { error: 'Reflex not found' });
        }

        // Check ownership
        if (reflexResult.Item.userId !== userId) {
          return createResponse(403, { error: 'Not authorized to delete this reflex' });
        }

        // Delete reflex
        await AWS.mockDynamoDBDocumentClient.delete().promise();

        // Update post reflex count
        await AWS.mockDynamoDBDocumentClient.update().promise();

        return createResponse(200, {
          message: 'Reflex deleted successfully'
        });
      } catch (error) {
        return createResponse(500, { error: 'Failed to delete reflex' });
      }
    }

    return createResponse(404, { error: 'Route not found' });
  } catch (error) {
    return createResponse(500, { error: 'Internal server error' });
  }
};

module.exports = {
  healthHandler,
  authHandler,
  postsHandler,
  mediaHandler,
  usersHandler,
  channelsHandler,
  reflexesHandler
};
