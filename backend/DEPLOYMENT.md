# GameFlex Backend Deployment Guide

This document explains how to deploy the GameFlex backend to different environments using environment-specific SAM configuration files.

## Environment-Specific Configuration

We use separate SAM configuration files for each environment to avoid parameter override conflicts:

- `samconfig-development.toml` - Development environment
- `samconfig-staging.toml` - Staging environment  
- `samconfig-production.toml` - Production environment

## Deployment Commands

### Development Environment

For local development with SAM:
```bash
# Start local development (builds and deploys automatically)
./start.sh

# Manual build and deploy
sam build --config-file samconfig-development.toml
sam deploy --config-file samconfig-development.toml
```

### Staging Environment

```bash
# Using the deployment script (recommended)
./deploy-staging.sh

# Manual deployment
sam build --config-file samconfig-staging.toml
sam deploy --config-file samconfig-staging.toml
```

### Production Environment

```bash
# Using the deployment script (recommended)
./deploy-production.sh

# Manual deployment
sam build --config-file samconfig-production.toml
sam deploy --config-file samconfig-production.toml
```

## Resource Naming

Each environment creates resources with environment-specific names:

### Development
- Stack: `gameflex-development`
- Tables: `gameflex-development-*` (e.g., `gameflex-development-Users`)
- Secrets: `gameflex-r2-config-development`, `gameflex-app-config-development`
- Functions: `gameflex-*-development` (e.g., `gameflex-auth-development`)

### Staging
- Stack: `gameflex-staging`
- Tables: `gameflex-staging-*` (e.g., `gameflex-staging-Users`)
- Secrets: `gameflex-r2-config-staging`, `gameflex-app-config-staging`
- Functions: `gameflex-*-staging` (e.g., `gameflex-auth-staging`)
- Domain: `staging.api.gameflex.io`

### Production
- Stack: `gameflex-production`
- Tables: `gameflex-production-*` (e.g., `gameflex-production-Users`)
- Secrets: `gameflex-r2-config-production`, `gameflex-app-config-production`
- Functions: `gameflex-*-production` (e.g., `gameflex-auth-production`)
- Domain: `api.gameflex.io`

## Benefits of This Approach

1. **No Parameter Override Conflicts**: Each environment has its own configuration file
2. **Clear Separation**: Easy to see what's configured for each environment
3. **Consistent Naming**: Resources are properly namespaced by environment
4. **Simplified Deployment**: No complex parameter override logic needed
5. **Environment Isolation**: Changes to one environment don't affect others

## Configuration Files

Each samconfig file contains:
- Stack name and S3 configuration
- Environment-specific parameter overrides (including CertificateArn)
- Build settings
- Environment variables for testing

The parameter overrides in each file ensure that the `Environment` parameter is correctly set, which drives the resource naming in the SAM template.

### Certificate Configuration

The SSL certificate ARN is configured directly in each samconfig file:
- **Staging**: Uses certificate for `staging.api.gameflex.io`
- **Production**: Uses certificate for `api.gameflex.io`

Make sure your ACM certificate covers the appropriate domains before deploying.
