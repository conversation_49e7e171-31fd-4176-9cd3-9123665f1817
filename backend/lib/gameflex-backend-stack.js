"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameFlexBackendStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const apigateway = __importStar(require("aws-cdk-lib/aws-apigateway"));
const cognito = __importStar(require("aws-cdk-lib/aws-cognito"));
const dynamodb = __importStar(require("aws-cdk-lib/aws-dynamodb"));
const lambda = __importStar(require("aws-cdk-lib/aws-lambda"));
const secretsmanager = __importStar(require("aws-cdk-lib/aws-secretsmanager"));
class GameFlexBackendStack extends cdk.Stack {
    userPool;
    userPoolClient;
    api;
    tables;
    constructor(scope, id, props) {
        super(scope, id, props);
        const { environment, projectName, domainName, certificateArn, r2BucketName, r2PublicUrl } = props;
        // Conditions
        const isProduction = environment === 'production';
        const isStaging = environment === 'staging';
        const isProductionOrStaging = isProduction || isStaging;
        const hasCustomDomain = !!domainName;
        // AWS Secrets Manager for R2 Configuration
        // Use fromSecretNameV2 to reference existing secrets or create new ones
        const r2SecretName = `${projectName}-r2-config-${environment}`;
        const r2Secret = secretsmanager.Secret.fromSecretNameV2(this, 'R2Secret', r2SecretName);
        // AWS Secrets Manager for General Configuration
        const appConfigSecretName = `${projectName}-app-config-${environment}`;
        const appConfigSecret = secretsmanager.Secret.fromSecretNameV2(this, 'AppConfigSecret', appConfigSecretName);
        // Note: Cannot apply removal policies to imported secrets
        // Secrets are managed externally and will persist regardless
        // Cognito User Pool
        this.userPool = new cognito.UserPool(this, 'UserPool', {
            userPoolName: `${projectName}-users-${environment}`,
            autoVerify: { email: true },
            signInAliases: { email: true },
            passwordPolicy: {
                minLength: 8,
                requireUppercase: true,
                requireLowercase: true,
                requireDigits: true,
                requireSymbols: false,
            },
            deletionProtection: isProductionOrStaging,
            removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
        });
        // Cognito User Pool Client
        this.userPoolClient = new cognito.UserPoolClient(this, 'UserPoolClient', {
            userPool: this.userPool,
            userPoolClientName: `${projectName}-client-${environment}`,
            generateSecret: false,
            authFlows: {
                adminUserPassword: true,
                userPassword: true,
                userSrp: true,
            },
            refreshTokenValidity: cdk.Duration.days(30),
            accessTokenValidity: cdk.Duration.minutes(60),
            idTokenValidity: cdk.Duration.minutes(60),
        });
        // DynamoDB Tables
        this.tables = this.createDynamoDBTables(projectName, environment, isProductionOrStaging);
        // Lambda Functions
        const lambdaFunctions = this.createLambdaFunctions(projectName, environment, this.userPool, this.userPoolClient, this.tables, r2Secret, appConfigSecret);
        // API Gateway
        this.api = this.createApiGateway(projectName, environment, lambdaFunctions, this.userPool);
        // Custom Domain (if specified)
        if (hasCustomDomain && certificateArn) {
            this.createCustomDomain(domainName, certificateArn);
        }
        // Outputs
        this.createOutputs(environment, domainName, r2Secret, appConfigSecret);
    }
    createDynamoDBTables(projectName, environment, isProductionOrStaging) {
        const tableConfig = {
            billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
            deletionProtection: isProductionOrStaging,
            pointInTimeRecovery: isProductionOrStaging,
            removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
        };
        const tables = {};
        // Posts Table
        tables.posts = new dynamodb.Table(this, 'PostsTable', {
            ...tableConfig,
            tableName: `${projectName}-${environment}-Posts`,
            partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
        });
        // Media Table
        tables.media = new dynamodb.Table(this, 'MediaTable', {
            ...tableConfig,
            tableName: `${projectName}-${environment}-Media`,
            partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
        });
        // User Profiles Table
        tables.userProfiles = new dynamodb.Table(this, 'UserProfilesTable', {
            ...tableConfig,
            tableName: `${projectName}-${environment}-UserProfiles`,
            partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
        });
        // Comments Table
        tables.comments = new dynamodb.Table(this, 'CommentsTable', {
            ...tableConfig,
            tableName: `${projectName}-${environment}-Comments`,
            partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
        });
        tables.comments.addGlobalSecondaryIndex({
            indexName: 'postId-index',
            partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
        });
        // Likes Table
        tables.likes = new dynamodb.Table(this, 'LikesTable', {
            ...tableConfig,
            tableName: `${projectName}-${environment}-Likes`,
            partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
        });
        tables.likes.addGlobalSecondaryIndex({
            indexName: 'userId-index',
            partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
        });
        // Follows Table
        tables.follows = new dynamodb.Table(this, 'FollowsTable', {
            ...tableConfig,
            tableName: `${projectName}-${environment}-Follows`,
            partitionKey: { name: 'followerId', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'followingId', type: dynamodb.AttributeType.STRING },
        });
        // Channels Table
        tables.channels = new dynamodb.Table(this, 'ChannelsTable', {
            ...tableConfig,
            tableName: `${projectName}-${environment}-Channels`,
            partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
        });
        tables.channels.addGlobalSecondaryIndex({
            indexName: 'ownerId-index',
            partitionKey: { name: 'ownerId', type: dynamodb.AttributeType.STRING },
        });
        // Channel Members Table
        tables.channelMembers = new dynamodb.Table(this, 'ChannelMembersTable', {
            ...tableConfig,
            tableName: `${projectName}-${environment}-ChannelMembers`,
            partitionKey: { name: 'channelId', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
        });
        tables.channelMembers.addGlobalSecondaryIndex({
            indexName: 'userId-index',
            partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
        });
        // Reflexes Table
        tables.reflexes = new dynamodb.Table(this, 'ReflexesTable', {
            ...tableConfig,
            tableName: `${projectName}-${environment}-Reflexes`,
            partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
        });
        tables.reflexes.addGlobalSecondaryIndex({
            indexName: 'postId-index',
            partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
        });
        tables.reflexes.addGlobalSecondaryIndex({
            indexName: 'userId-index',
            partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
        });
        // Users Table
        tables.users = new dynamodb.Table(this, 'UsersTable', {
            ...tableConfig,
            tableName: `${projectName}-${environment}-Users`,
            partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
        });
        tables.users.addGlobalSecondaryIndex({
            indexName: 'EmailIndex',
            partitionKey: { name: 'email', type: dynamodb.AttributeType.STRING },
        });
        tables.users.addGlobalSecondaryIndex({
            indexName: 'UsernameIndex',
            partitionKey: { name: 'username', type: dynamodb.AttributeType.STRING },
        });
        tables.users.addGlobalSecondaryIndex({
            indexName: 'CognitoUserIdIndex',
            partitionKey: { name: 'cognitoUserId', type: dynamodb.AttributeType.STRING },
        });
        return tables;
    }
    createLambdaFunctions(projectName, environment, userPool, userPoolClient, tables, r2Secret, appConfigSecret) {
        const functions = {};
        // Common environment variables for all Lambda functions
        const commonEnvironment = {
            ENVIRONMENT: environment,
            PROJECT_NAME: projectName,
            USER_POOL_ID: userPool.userPoolId,
            USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
            POSTS_TABLE: tables.posts.tableName,
            MEDIA_TABLE: tables.media.tableName,
            USER_PROFILES_TABLE: tables.userProfiles.tableName,
            COMMENTS_TABLE: tables.comments.tableName,
            LIKES_TABLE: tables.likes.tableName,
            FOLLOWS_TABLE: tables.follows.tableName,
            CHANNELS_TABLE: tables.channels.tableName,
            CHANNEL_MEMBERS_TABLE: tables.channelMembers.tableName,
            REFLEXES_TABLE: tables.reflexes.tableName,
            USERS_TABLE: tables.users.tableName,
            R2_SECRET_NAME: r2Secret.secretName,
            APP_CONFIG_SECRET_NAME: appConfigSecret.secretName,
        };
        // Authorizer Function
        functions.authorizer = new lambda.Function(this, 'AuthorizerFunction', {
            functionName: `${projectName}-authorizer-${environment}`,
            runtime: lambda.Runtime.NODEJS_20_X,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('src/authorizer'),
            timeout: cdk.Duration.seconds(30),
            memorySize: 256,
            environment: {
                ...commonEnvironment,
                USER_POOL_ID: userPool.userPoolId,
            },
        });
        // Grant permissions to authorizer
        userPool.grant(functions.authorizer, 'cognito-idp:GetUser');
        // Auth Function
        functions.auth = new lambda.Function(this, 'AuthFunction', {
            functionName: `${projectName}-auth-${environment}`,
            runtime: lambda.Runtime.NODEJS_20_X,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('src/auth'),
            timeout: cdk.Duration.seconds(30),
            memorySize: 256,
            environment: commonEnvironment,
        });
        // Grant permissions to auth function
        tables.users.grantReadWriteData(functions.auth);
        userPool.grant(functions.auth, 'cognito-idp:*');
        // Posts Function
        functions.posts = new lambda.Function(this, 'PostsFunction', {
            functionName: `${projectName}-posts-${environment}`,
            runtime: lambda.Runtime.NODEJS_20_X,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('src/posts'),
            timeout: cdk.Duration.seconds(30),
            memorySize: 256,
            environment: commonEnvironment,
        });
        // Grant permissions to posts function
        tables.posts.grantReadWriteData(functions.posts);
        tables.media.grantReadWriteData(functions.posts);
        tables.comments.grantReadWriteData(functions.posts);
        tables.likes.grantReadWriteData(functions.posts);
        tables.reflexes.grantReadWriteData(functions.posts);
        tables.users.grantReadWriteData(functions.posts);
        // Media Function
        functions.media = new lambda.Function(this, 'MediaFunction', {
            functionName: `${projectName}-media-${environment}`,
            runtime: lambda.Runtime.NODEJS_20_X,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('src/media'),
            timeout: cdk.Duration.seconds(30),
            memorySize: 256,
            environment: commonEnvironment,
        });
        // Grant permissions to media function
        tables.media.grantReadWriteData(functions.media);
        r2Secret.grantRead(functions.media);
        appConfigSecret.grantRead(functions.media);
        // Users Function
        functions.users = new lambda.Function(this, 'UsersFunction', {
            functionName: `${projectName}-users-${environment}`,
            runtime: lambda.Runtime.NODEJS_20_X,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('src/users'),
            timeout: cdk.Duration.seconds(30),
            memorySize: 256,
            environment: commonEnvironment,
        });
        // Grant permissions to users function
        tables.users.grantReadWriteData(functions.users);
        tables.userProfiles.grantReadWriteData(functions.users);
        tables.follows.grantReadWriteData(functions.users);
        tables.posts.grantReadWriteData(functions.users);
        tables.likes.grantReadWriteData(functions.users);
        // Health Function
        functions.health = new lambda.Function(this, 'HealthFunction', {
            functionName: `${projectName}-health-${environment}`,
            runtime: lambda.Runtime.NODEJS_20_X,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('src/health'),
            timeout: cdk.Duration.seconds(30),
            memorySize: 256,
            environment: commonEnvironment,
        });
        // Grant permissions to health function
        tables.users.grantReadData(functions.health);
        // Reflexes Function
        functions.reflexes = new lambda.Function(this, 'ReflexesFunction', {
            functionName: `${projectName}-reflexes-${environment}`,
            runtime: lambda.Runtime.NODEJS_20_X,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('src/reflexes'),
            timeout: cdk.Duration.seconds(30),
            memorySize: 256,
            environment: commonEnvironment,
        });
        // Grant permissions to reflexes function
        tables.reflexes.grantReadWriteData(functions.reflexes);
        tables.posts.grantReadWriteData(functions.reflexes);
        tables.users.grantReadWriteData(functions.reflexes);
        tables.media.grantReadWriteData(functions.reflexes);
        // Channels Function
        functions.channels = new lambda.Function(this, 'ChannelsFunction', {
            functionName: `${projectName}-channels-${environment}`,
            runtime: lambda.Runtime.NODEJS_20_X,
            handler: 'index.handler',
            code: lambda.Code.fromAsset('src/channels'),
            timeout: cdk.Duration.seconds(30),
            memorySize: 256,
            environment: commonEnvironment,
        });
        // Grant permissions to channels function
        tables.channels.grantReadWriteData(functions.channels);
        tables.channelMembers.grantReadWriteData(functions.channels);
        tables.users.grantReadWriteData(functions.channels);
        tables.posts.grantReadWriteData(functions.channels);
        tables.media.grantReadWriteData(functions.channels);
        r2Secret.grantRead(functions.channels);
        appConfigSecret.grantRead(functions.channels);
        return functions;
    }
    createApiGateway(projectName, environment, functions, userPool) {
        // Create API Gateway
        const api = new apigateway.RestApi(this, 'GameFlexApi', {
            restApiName: `${projectName}-api-${environment}`,
            description: `GameFlex API for ${environment} environment`,
            defaultCorsPreflightOptions: {
                allowOrigins: apigateway.Cors.ALL_ORIGINS,
                allowMethods: apigateway.Cors.ALL_METHODS,
                allowHeaders: [
                    'Content-Type',
                    'X-Amz-Date',
                    'Authorization',
                    'X-Api-Key',
                    'X-Amz-Security-Token',
                    'X-Amz-User-Agent',
                ],
            },
            deployOptions: {
                stageName: 'v1',
                throttlingRateLimit: 1000,
                throttlingBurstLimit: 2000,
            },
        });
        // Create authorizer
        const authorizer = new apigateway.TokenAuthorizer(this, 'DefaultAuthorizer', {
            handler: functions.authorizer,
            authorizerName: 'DefaultAuthorizer',
            resultsCacheTtl: cdk.Duration.minutes(5),
        });
        // Create API resources and methods directly under root
        // Since the stage is already 'v1', we don't need a nested v1 resource
        // Health endpoint (no auth required)
        const health = api.root.addResource('health');
        health.addMethod('GET', new apigateway.LambdaIntegration(functions.health));
        // Auth endpoints (no auth required)
        const auth = api.root.addResource('auth');
        auth.addMethod('POST', new apigateway.LambdaIntegration(functions.auth));
        auth.addMethod('GET', new apigateway.LambdaIntegration(functions.auth));
        // Posts endpoints (auth required)
        const posts = api.root.addResource('posts');
        posts.addMethod('GET', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        posts.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        // Draft posts endpoint
        const postsDraft = posts.addResource('draft');
        postsDraft.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        const postId = posts.addResource('{id}');
        postId.addMethod('GET', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        postId.addMethod('PUT', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        postId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        // Media attachment endpoint
        const postMedia = postId.addResource('media');
        postMedia.addMethod('PUT', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        // Publish endpoint
        const postPublish = postId.addResource('publish');
        postPublish.addMethod('PUT', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        // Like endpoints
        const postLike = postId.addResource('like');
        postLike.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        postLike.addMethod('DELETE', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        // Comments endpoints
        const postComments = postId.addResource('comments');
        postComments.addMethod('GET', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        postComments.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
            authorizer,
        });
        // Users endpoints (auth required)
        const users = api.root.addResource('users');
        users.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
            authorizer,
        });
        users.addMethod('POST', new apigateway.LambdaIntegration(functions.users), {
            authorizer,
        });
        const userId = users.addResource('{id}');
        userId.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
            authorizer,
        });
        userId.addMethod('PUT', new apigateway.LambdaIntegration(functions.users), {
            authorizer,
        });
        // User profile endpoints
        const profile = users.addResource('profile');
        profile.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
            authorizer,
        });
        profile.addMethod('PUT', new apigateway.LambdaIntegration(functions.users), {
            authorizer,
        });
        // User liked posts endpoint
        const likedPosts = profile.addResource('liked-posts');
        likedPosts.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
            authorizer,
        });
        // Media endpoints (auth required)
        const media = api.root.addResource('media');
        media.addMethod('GET', new apigateway.LambdaIntegration(functions.media), {
            authorizer,
        });
        media.addMethod('POST', new apigateway.LambdaIntegration(functions.media), {
            authorizer,
        });
        // Media upload endpoint
        const mediaUpload = media.addResource('upload');
        mediaUpload.addMethod('POST', new apigateway.LambdaIntegration(functions.media), {
            authorizer,
        });
        const mediaId = media.addResource('{id}');
        mediaId.addMethod('GET', new apigateway.LambdaIntegration(functions.media), {
            authorizer,
        });
        mediaId.addMethod('PUT', new apigateway.LambdaIntegration(functions.media), {
            authorizer,
        });
        mediaId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.media), {
            authorizer,
        });
        // Reflexes endpoints (auth required)
        const reflexes = api.root.addResource('reflexes');
        reflexes.addMethod('GET', new apigateway.LambdaIntegration(functions.reflexes), {
            authorizer,
        });
        reflexes.addMethod('POST', new apigateway.LambdaIntegration(functions.reflexes), {
            authorizer,
        });
        const reflexId = reflexes.addResource('{id}');
        reflexId.addMethod('GET', new apigateway.LambdaIntegration(functions.reflexes), {
            authorizer,
        });
        reflexId.addMethod('PUT', new apigateway.LambdaIntegration(functions.reflexes), {
            authorizer,
        });
        reflexId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.reflexes), {
            authorizer,
        });
        // Channels endpoints (auth required)
        const channels = api.root.addResource('channels');
        channels.addMethod('GET', new apigateway.LambdaIntegration(functions.channels), {
            authorizer,
        });
        channels.addMethod('POST', new apigateway.LambdaIntegration(functions.channels), {
            authorizer,
        });
        const channelId = channels.addResource('{id}');
        channelId.addMethod('GET', new apigateway.LambdaIntegration(functions.channels), {
            authorizer,
        });
        channelId.addMethod('PUT', new apigateway.LambdaIntegration(functions.channels), {
            authorizer,
        });
        channelId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.channels), {
            authorizer,
        });
        return api;
    }
    createCustomDomain(domainName, certificateArn) {
        // Custom domain implementation would go here
        // This is a placeholder for future implementation
        console.log(`Custom domain ${domainName} with certificate ${certificateArn} would be configured here`);
    }
    createOutputs(environment, domainName, r2Secret, appConfigSecret) {
        // API Gateway URL
        new cdk.CfnOutput(this, 'ApiGatewayUrl', {
            value: this.api.url,
            description: 'API Gateway URL',
            exportName: `${this.stackName}-ApiGatewayUrl`,
        });
        // User Pool ID
        new cdk.CfnOutput(this, 'UserPoolId', {
            value: this.userPool.userPoolId,
            description: 'Cognito User Pool ID',
            exportName: `${this.stackName}-UserPoolId`,
        });
        // User Pool Client ID
        new cdk.CfnOutput(this, 'UserPoolClientId', {
            value: this.userPoolClient.userPoolClientId,
            description: 'Cognito User Pool Client ID',
            exportName: `${this.stackName}-UserPoolClientId`,
        });
        // Custom Domain URL (if configured)
        if (domainName) {
            new cdk.CfnOutput(this, 'CustomDomainUrl', {
                value: `https://${domainName}`,
                description: 'Custom Domain URL',
                exportName: `${this.stackName}-CustomDomainUrl`,
            });
        }
        // R2 Secret ARN
        if (r2Secret) {
            new cdk.CfnOutput(this, 'R2SecretArn', {
                value: r2Secret.secretArn,
                description: 'R2 Configuration Secret ARN',
                exportName: `${this.stackName}-R2SecretArn`,
            });
        }
        // App Config Secret ARN
        if (appConfigSecret) {
            new cdk.CfnOutput(this, 'AppConfigSecretArn', {
                value: appConfigSecret.secretArn,
                description: 'App Configuration Secret ARN',
                exportName: `${this.stackName}-AppConfigSecretArn`,
            });
        }
        // DynamoDB Table Names
        Object.entries(this.tables).forEach(([name, table]) => {
            new cdk.CfnOutput(this, `${name.charAt(0).toUpperCase() + name.slice(1)}TableName`, {
                value: table.tableName,
                description: `${name} DynamoDB Table Name`,
                exportName: `${this.stackName}-${name.charAt(0).toUpperCase() + name.slice(1)}TableName`,
            });
        });
    }
}
exports.GameFlexBackendStack = GameFlexBackendStack;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZ2FtZWZsZXgtYmFja2VuZC1zdGFjay5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbImdhbWVmbGV4LWJhY2tlbmQtc3RhY2sudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxpREFBbUM7QUFDbkMsdUVBQXlEO0FBQ3pELGlFQUFtRDtBQUNuRCxtRUFBcUQ7QUFDckQsK0RBQWlEO0FBRWpELCtFQUFpRTtBQWFqRSxNQUFhLG9CQUFxQixTQUFRLEdBQUcsQ0FBQyxLQUFLO0lBQ2pDLFFBQVEsQ0FBbUI7SUFDM0IsY0FBYyxDQUF5QjtJQUN2QyxHQUFHLENBQXFCO0lBQ3hCLE1BQU0sQ0FBb0M7SUFFMUQsWUFBWSxLQUFnQixFQUFFLEVBQVUsRUFBRSxLQUFnQztRQUN4RSxLQUFLLENBQUMsS0FBSyxFQUFFLEVBQUUsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUV4QixNQUFNLEVBQUUsV0FBVyxFQUFFLFdBQVcsRUFBRSxVQUFVLEVBQUUsY0FBYyxFQUFFLFlBQVksRUFBRSxXQUFXLEVBQUUsR0FBRyxLQUFLLENBQUM7UUFFbEcsYUFBYTtRQUNiLE1BQU0sWUFBWSxHQUFHLFdBQVcsS0FBSyxZQUFZLENBQUM7UUFDbEQsTUFBTSxTQUFTLEdBQUcsV0FBVyxLQUFLLFNBQVMsQ0FBQztRQUM1QyxNQUFNLHFCQUFxQixHQUFHLFlBQVksSUFBSSxTQUFTLENBQUM7UUFDeEQsTUFBTSxlQUFlLEdBQUcsQ0FBQyxDQUFDLFVBQVUsQ0FBQztRQUVyQywyQ0FBMkM7UUFDM0Msd0VBQXdFO1FBQ3hFLE1BQU0sWUFBWSxHQUFHLEdBQUcsV0FBVyxjQUFjLFdBQVcsRUFBRSxDQUFDO1FBQy9ELE1BQU0sUUFBUSxHQUFHLGNBQWMsQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxFQUFFLFVBQVUsRUFBRSxZQUFZLENBQUMsQ0FBQztRQUV4RixnREFBZ0Q7UUFDaEQsTUFBTSxtQkFBbUIsR0FBRyxHQUFHLFdBQVcsZUFBZSxXQUFXLEVBQUUsQ0FBQztRQUN2RSxNQUFNLGVBQWUsR0FBRyxjQUFjLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLElBQUksRUFBRSxpQkFBaUIsRUFBRSxtQkFBbUIsQ0FBQyxDQUFDO1FBRTdHLDBEQUEwRDtRQUMxRCw2REFBNkQ7UUFFN0Qsb0JBQW9CO1FBQ3BCLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxPQUFPLENBQUMsUUFBUSxDQUFDLElBQUksRUFBRSxVQUFVLEVBQUU7WUFDckQsWUFBWSxFQUFFLEdBQUcsV0FBVyxVQUFVLFdBQVcsRUFBRTtZQUNuRCxVQUFVLEVBQUUsRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFO1lBQzNCLGFBQWEsRUFBRSxFQUFFLEtBQUssRUFBRSxJQUFJLEVBQUU7WUFDOUIsY0FBYyxFQUFFO2dCQUNkLFNBQVMsRUFBRSxDQUFDO2dCQUNaLGdCQUFnQixFQUFFLElBQUk7Z0JBQ3RCLGdCQUFnQixFQUFFLElBQUk7Z0JBQ3RCLGFBQWEsRUFBRSxJQUFJO2dCQUNuQixjQUFjLEVBQUUsS0FBSzthQUN0QjtZQUNELGtCQUFrQixFQUFFLHFCQUFxQjtZQUN6QyxhQUFhLEVBQUUscUJBQXFCLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxhQUFhLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsYUFBYSxDQUFDLE9BQU87U0FDNUYsQ0FBQyxDQUFDO1FBRUgsMkJBQTJCO1FBQzNCLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxPQUFPLENBQUMsY0FBYyxDQUFDLElBQUksRUFBRSxnQkFBZ0IsRUFBRTtZQUN2RSxRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVE7WUFDdkIsa0JBQWtCLEVBQUUsR0FBRyxXQUFXLFdBQVcsV0FBVyxFQUFFO1lBQzFELGNBQWMsRUFBRSxLQUFLO1lBQ3JCLFNBQVMsRUFBRTtnQkFDVCxpQkFBaUIsRUFBRSxJQUFJO2dCQUN2QixZQUFZLEVBQUUsSUFBSTtnQkFDbEIsT0FBTyxFQUFFLElBQUk7YUFDZDtZQUNELG9CQUFvQixFQUFFLEdBQUcsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQztZQUMzQyxtQkFBbUIsRUFBRSxHQUFHLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7WUFDN0MsZUFBZSxFQUFFLEdBQUcsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztTQUMxQyxDQUFDLENBQUM7UUFFSCxrQkFBa0I7UUFDbEIsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsb0JBQW9CLENBQUMsV0FBVyxFQUFFLFdBQVcsRUFBRSxxQkFBcUIsQ0FBQyxDQUFDO1FBRXpGLG1CQUFtQjtRQUNuQixNQUFNLGVBQWUsR0FBRyxJQUFJLENBQUMscUJBQXFCLENBQ2hELFdBQVcsRUFDWCxXQUFXLEVBQ1gsSUFBSSxDQUFDLFFBQVEsRUFDYixJQUFJLENBQUMsY0FBYyxFQUNuQixJQUFJLENBQUMsTUFBTSxFQUNYLFFBQVEsRUFDUixlQUFlLENBQ2hCLENBQUM7UUFFRixjQUFjO1FBQ2QsSUFBSSxDQUFDLEdBQUcsR0FBRyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsV0FBVyxFQUFFLFdBQVcsRUFBRSxlQUFlLEVBQUUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBRTNGLCtCQUErQjtRQUMvQixJQUFJLGVBQWUsSUFBSSxjQUFjLEVBQUUsQ0FBQztZQUN0QyxJQUFJLENBQUMsa0JBQWtCLENBQUMsVUFBVyxFQUFFLGNBQWMsQ0FBQyxDQUFDO1FBQ3ZELENBQUM7UUFFRCxVQUFVO1FBQ1YsSUFBSSxDQUFDLGFBQWEsQ0FBQyxXQUFXLEVBQUUsVUFBVSxFQUFFLFFBQVEsRUFBRSxlQUFlLENBQUMsQ0FBQztJQUN6RSxDQUFDO0lBRU8sb0JBQW9CLENBQzFCLFdBQW1CLEVBQ25CLFdBQW1CLEVBQ25CLHFCQUE4QjtRQUU5QixNQUFNLFdBQVcsR0FBRztZQUNsQixXQUFXLEVBQUUsUUFBUSxDQUFDLFdBQVcsQ0FBQyxlQUFlO1lBQ2pELGtCQUFrQixFQUFFLHFCQUFxQjtZQUN6QyxtQkFBbUIsRUFBRSxxQkFBcUI7WUFDMUMsYUFBYSxFQUFFLHFCQUFxQixDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsYUFBYSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLGFBQWEsQ0FBQyxPQUFPO1NBQzVGLENBQUM7UUFFRixNQUFNLE1BQU0sR0FBc0MsRUFBRSxDQUFDO1FBRXJELGNBQWM7UUFDZCxNQUFNLENBQUMsS0FBSyxHQUFHLElBQUksUUFBUSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsWUFBWSxFQUFFO1lBQ3BELEdBQUcsV0FBVztZQUNkLFNBQVMsRUFBRSxHQUFHLFdBQVcsSUFBSSxXQUFXLFFBQVE7WUFDaEQsWUFBWSxFQUFFLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsUUFBUSxDQUFDLGFBQWEsQ0FBQyxNQUFNLEVBQUU7U0FDbEUsQ0FBQyxDQUFDO1FBRUgsY0FBYztRQUNkLE1BQU0sQ0FBQyxLQUFLLEdBQUcsSUFBSSxRQUFRLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxZQUFZLEVBQUU7WUFDcEQsR0FBRyxXQUFXO1lBQ2QsU0FBUyxFQUFFLEdBQUcsV0FBVyxJQUFJLFdBQVcsUUFBUTtZQUNoRCxZQUFZLEVBQUUsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxRQUFRLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRTtTQUNsRSxDQUFDLENBQUM7UUFFSCxzQkFBc0I7UUFDdEIsTUFBTSxDQUFDLFlBQVksR0FBRyxJQUFJLFFBQVEsQ0FBQyxLQUFLLENBQUMsSUFBSSxFQUFFLG1CQUFtQixFQUFFO1lBQ2xFLEdBQUcsV0FBVztZQUNkLFNBQVMsRUFBRSxHQUFHLFdBQVcsSUFBSSxXQUFXLGVBQWU7WUFDdkQsWUFBWSxFQUFFLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsUUFBUSxDQUFDLGFBQWEsQ0FBQyxNQUFNLEVBQUU7U0FDdEUsQ0FBQyxDQUFDO1FBRUgsaUJBQWlCO1FBQ2pCLE1BQU0sQ0FBQyxRQUFRLEdBQUcsSUFBSSxRQUFRLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxlQUFlLEVBQUU7WUFDMUQsR0FBRyxXQUFXO1lBQ2QsU0FBUyxFQUFFLEdBQUcsV0FBVyxJQUFJLFdBQVcsV0FBVztZQUNuRCxZQUFZLEVBQUUsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxRQUFRLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRTtTQUNsRSxDQUFDLENBQUM7UUFDSCxNQUFNLENBQUMsUUFBUSxDQUFDLHVCQUF1QixDQUFDO1lBQ3RDLFNBQVMsRUFBRSxjQUFjO1lBQ3pCLFlBQVksRUFBRSxFQUFFLElBQUksRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLFFBQVEsQ0FBQyxhQUFhLENBQUMsTUFBTSxFQUFFO1NBQ3RFLENBQUMsQ0FBQztRQUVILGNBQWM7UUFDZCxNQUFNLENBQUMsS0FBSyxHQUFHLElBQUksUUFBUSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsWUFBWSxFQUFFO1lBQ3BELEdBQUcsV0FBVztZQUNkLFNBQVMsRUFBRSxHQUFHLFdBQVcsSUFBSSxXQUFXLFFBQVE7WUFDaEQsWUFBWSxFQUFFLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsUUFBUSxDQUFDLGFBQWEsQ0FBQyxNQUFNLEVBQUU7WUFDckUsT0FBTyxFQUFFLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsUUFBUSxDQUFDLGFBQWEsQ0FBQyxNQUFNLEVBQUU7U0FDakUsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxDQUFDLEtBQUssQ0FBQyx1QkFBdUIsQ0FBQztZQUNuQyxTQUFTLEVBQUUsY0FBYztZQUN6QixZQUFZLEVBQUUsRUFBRSxJQUFJLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxRQUFRLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRTtTQUN0RSxDQUFDLENBQUM7UUFFSCxnQkFBZ0I7UUFDaEIsTUFBTSxDQUFDLE9BQU8sR0FBRyxJQUFJLFFBQVEsQ0FBQyxLQUFLLENBQUMsSUFBSSxFQUFFLGNBQWMsRUFBRTtZQUN4RCxHQUFHLFdBQVc7WUFDZCxTQUFTLEVBQUUsR0FBRyxXQUFXLElBQUksV0FBVyxVQUFVO1lBQ2xELFlBQVksRUFBRSxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsSUFBSSxFQUFFLFFBQVEsQ0FBQyxhQUFhLENBQUMsTUFBTSxFQUFFO1lBQ3pFLE9BQU8sRUFBRSxFQUFFLElBQUksRUFBRSxhQUFhLEVBQUUsSUFBSSxFQUFFLFFBQVEsQ0FBQyxhQUFhLENBQUMsTUFBTSxFQUFFO1NBQ3RFLENBQUMsQ0FBQztRQUVILGlCQUFpQjtRQUNqQixNQUFNLENBQUMsUUFBUSxHQUFHLElBQUksUUFBUSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsZUFBZSxFQUFFO1lBQzFELEdBQUcsV0FBVztZQUNkLFNBQVMsRUFBRSxHQUFHLFdBQVcsSUFBSSxXQUFXLFdBQVc7WUFDbkQsWUFBWSxFQUFFLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsUUFBUSxDQUFDLGFBQWEsQ0FBQyxNQUFNLEVBQUU7U0FDbEUsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxDQUFDLFFBQVEsQ0FBQyx1QkFBdUIsQ0FBQztZQUN0QyxTQUFTLEVBQUUsZUFBZTtZQUMxQixZQUFZLEVBQUUsRUFBRSxJQUFJLEVBQUUsU0FBUyxFQUFFLElBQUksRUFBRSxRQUFRLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRTtTQUN2RSxDQUFDLENBQUM7UUFFSCx3QkFBd0I7UUFDeEIsTUFBTSxDQUFDLGNBQWMsR0FBRyxJQUFJLFFBQVEsQ0FBQyxLQUFLLENBQUMsSUFBSSxFQUFFLHFCQUFxQixFQUFFO1lBQ3RFLEdBQUcsV0FBVztZQUNkLFNBQVMsRUFBRSxHQUFHLFdBQVcsSUFBSSxXQUFXLGlCQUFpQjtZQUN6RCxZQUFZLEVBQUUsRUFBRSxJQUFJLEVBQUUsV0FBVyxFQUFFLElBQUksRUFBRSxRQUFRLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRTtZQUN4RSxPQUFPLEVBQUUsRUFBRSxJQUFJLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxRQUFRLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRTtTQUNqRSxDQUFDLENBQUM7UUFDSCxNQUFNLENBQUMsY0FBYyxDQUFDLHVCQUF1QixDQUFDO1lBQzVDLFNBQVMsRUFBRSxjQUFjO1lBQ3pCLFlBQVksRUFBRSxFQUFFLElBQUksRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLFFBQVEsQ0FBQyxhQUFhLENBQUMsTUFBTSxFQUFFO1NBQ3RFLENBQUMsQ0FBQztRQUVILGlCQUFpQjtRQUNqQixNQUFNLENBQUMsUUFBUSxHQUFHLElBQUksUUFBUSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsZUFBZSxFQUFFO1lBQzFELEdBQUcsV0FBVztZQUNkLFNBQVMsRUFBRSxHQUFHLFdBQVcsSUFBSSxXQUFXLFdBQVc7WUFDbkQsWUFBWSxFQUFFLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsUUFBUSxDQUFDLGFBQWEsQ0FBQyxNQUFNLEVBQUU7U0FDbEUsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxDQUFDLFFBQVEsQ0FBQyx1QkFBdUIsQ0FBQztZQUN0QyxTQUFTLEVBQUUsY0FBYztZQUN6QixZQUFZLEVBQUUsRUFBRSxJQUFJLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxRQUFRLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRTtTQUN0RSxDQUFDLENBQUM7UUFDSCxNQUFNLENBQUMsUUFBUSxDQUFDLHVCQUF1QixDQUFDO1lBQ3RDLFNBQVMsRUFBRSxjQUFjO1lBQ3pCLFlBQVksRUFBRSxFQUFFLElBQUksRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLFFBQVEsQ0FBQyxhQUFhLENBQUMsTUFBTSxFQUFFO1NBQ3RFLENBQUMsQ0FBQztRQUVILGNBQWM7UUFDZCxNQUFNLENBQUMsS0FBSyxHQUFHLElBQUksUUFBUSxDQUFDLEtBQUssQ0FBQyxJQUFJLEVBQUUsWUFBWSxFQUFFO1lBQ3BELEdBQUcsV0FBVztZQUNkLFNBQVMsRUFBRSxHQUFHLFdBQVcsSUFBSSxXQUFXLFFBQVE7WUFDaEQsWUFBWSxFQUFFLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsUUFBUSxDQUFDLGFBQWEsQ0FBQyxNQUFNLEVBQUU7U0FDbEUsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxDQUFDLEtBQUssQ0FBQyx1QkFBdUIsQ0FBQztZQUNuQyxTQUFTLEVBQUUsWUFBWTtZQUN2QixZQUFZLEVBQUUsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLElBQUksRUFBRSxRQUFRLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRTtTQUNyRSxDQUFDLENBQUM7UUFDSCxNQUFNLENBQUMsS0FBSyxDQUFDLHVCQUF1QixDQUFDO1lBQ25DLFNBQVMsRUFBRSxlQUFlO1lBQzFCLFlBQVksRUFBRSxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsSUFBSSxFQUFFLFFBQVEsQ0FBQyxhQUFhLENBQUMsTUFBTSxFQUFFO1NBQ3hFLENBQUMsQ0FBQztRQUNILE1BQU0sQ0FBQyxLQUFLLENBQUMsdUJBQXVCLENBQUM7WUFDbkMsU0FBUyxFQUFFLG9CQUFvQjtZQUMvQixZQUFZLEVBQUUsRUFBRSxJQUFJLEVBQUUsZUFBZSxFQUFFLElBQUksRUFBRSxRQUFRLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRTtTQUM3RSxDQUFDLENBQUM7UUFFSCxPQUFPLE1BQU0sQ0FBQztJQUNoQixDQUFDO0lBRU8scUJBQXFCLENBQzNCLFdBQW1CLEVBQ25CLFdBQW1CLEVBQ25CLFFBQTBCLEVBQzFCLGNBQXNDLEVBQ3RDLE1BQXlDLEVBQ3pDLFFBQWdDLEVBQ2hDLGVBQXVDO1FBRXZDLE1BQU0sU0FBUyxHQUF1QyxFQUFFLENBQUM7UUFFekQsd0RBQXdEO1FBQ3hELE1BQU0saUJBQWlCLEdBQUc7WUFDeEIsV0FBVyxFQUFFLFdBQVc7WUFDeEIsWUFBWSxFQUFFLFdBQVc7WUFDekIsWUFBWSxFQUFFLFFBQVEsQ0FBQyxVQUFVO1lBQ2pDLG1CQUFtQixFQUFFLGNBQWMsQ0FBQyxnQkFBZ0I7WUFDcEQsV0FBVyxFQUFFLE1BQU0sQ0FBQyxLQUFLLENBQUMsU0FBUztZQUNuQyxXQUFXLEVBQUUsTUFBTSxDQUFDLEtBQUssQ0FBQyxTQUFTO1lBQ25DLG1CQUFtQixFQUFFLE1BQU0sQ0FBQyxZQUFZLENBQUMsU0FBUztZQUNsRCxjQUFjLEVBQUUsTUFBTSxDQUFDLFFBQVEsQ0FBQyxTQUFTO1lBQ3pDLFdBQVcsRUFBRSxNQUFNLENBQUMsS0FBSyxDQUFDLFNBQVM7WUFDbkMsYUFBYSxFQUFFLE1BQU0sQ0FBQyxPQUFPLENBQUMsU0FBUztZQUN2QyxjQUFjLEVBQUUsTUFBTSxDQUFDLFFBQVEsQ0FBQyxTQUFTO1lBQ3pDLHFCQUFxQixFQUFFLE1BQU0sQ0FBQyxjQUFjLENBQUMsU0FBUztZQUN0RCxjQUFjLEVBQUUsTUFBTSxDQUFDLFFBQVEsQ0FBQyxTQUFTO1lBQ3pDLFdBQVcsRUFBRSxNQUFNLENBQUMsS0FBSyxDQUFDLFNBQVM7WUFDbkMsY0FBYyxFQUFFLFFBQVEsQ0FBQyxVQUFVO1lBQ25DLHNCQUFzQixFQUFFLGVBQWUsQ0FBQyxVQUFVO1NBQ25ELENBQUM7UUFFRixzQkFBc0I7UUFDdEIsU0FBUyxDQUFDLFVBQVUsR0FBRyxJQUFJLE1BQU0sQ0FBQyxRQUFRLENBQUMsSUFBSSxFQUFFLG9CQUFvQixFQUFFO1lBQ3JFLFlBQVksRUFBRSxHQUFHLFdBQVcsZUFBZSxXQUFXLEVBQUU7WUFDeEQsT0FBTyxFQUFFLE1BQU0sQ0FBQyxPQUFPLENBQUMsV0FBVztZQUNuQyxPQUFPLEVBQUUsZUFBZTtZQUN4QixJQUFJLEVBQUUsTUFBTSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsZ0JBQWdCLENBQUM7WUFDN0MsT0FBTyxFQUFFLEdBQUcsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNqQyxVQUFVLEVBQUUsR0FBRztZQUNmLFdBQVcsRUFBRTtnQkFDWCxHQUFHLGlCQUFpQjtnQkFDcEIsWUFBWSxFQUFFLFFBQVEsQ0FBQyxVQUFVO2FBQ2xDO1NBQ0YsQ0FBQyxDQUFDO1FBRUgsa0NBQWtDO1FBQ2xDLFFBQVEsQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLFVBQVUsRUFBRSxxQkFBcUIsQ0FBQyxDQUFDO1FBRTVELGdCQUFnQjtRQUNoQixTQUFTLENBQUMsSUFBSSxHQUFHLElBQUksTUFBTSxDQUFDLFFBQVEsQ0FBQyxJQUFJLEVBQUUsY0FBYyxFQUFFO1lBQ3pELFlBQVksRUFBRSxHQUFHLFdBQVcsU0FBUyxXQUFXLEVBQUU7WUFDbEQsT0FBTyxFQUFFLE1BQU0sQ0FBQyxPQUFPLENBQUMsV0FBVztZQUNuQyxPQUFPLEVBQUUsZUFBZTtZQUN4QixJQUFJLEVBQUUsTUFBTSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDO1lBQ3ZDLE9BQU8sRUFBRSxHQUFHLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7WUFDakMsVUFBVSxFQUFFLEdBQUc7WUFDZixXQUFXLEVBQUUsaUJBQWlCO1NBQy9CLENBQUMsQ0FBQztRQUVILHFDQUFxQztRQUNyQyxNQUFNLENBQUMsS0FBSyxDQUFDLGtCQUFrQixDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNoRCxRQUFRLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUsZUFBZSxDQUFDLENBQUM7UUFFaEQsaUJBQWlCO1FBQ2pCLFNBQVMsQ0FBQyxLQUFLLEdBQUcsSUFBSSxNQUFNLENBQUMsUUFBUSxDQUFDLElBQUksRUFBRSxlQUFlLEVBQUU7WUFDM0QsWUFBWSxFQUFFLEdBQUcsV0FBVyxVQUFVLFdBQVcsRUFBRTtZQUNuRCxPQUFPLEVBQUUsTUFBTSxDQUFDLE9BQU8sQ0FBQyxXQUFXO1lBQ25DLE9BQU8sRUFBRSxlQUFlO1lBQ3hCLElBQUksRUFBRSxNQUFNLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxXQUFXLENBQUM7WUFDeEMsT0FBTyxFQUFFLEdBQUcsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNqQyxVQUFVLEVBQUUsR0FBRztZQUNmLFdBQVcsRUFBRSxpQkFBaUI7U0FDL0IsQ0FBQyxDQUFDO1FBRUgsc0NBQXNDO1FBQ3RDLE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ2pELE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ2pELE1BQU0sQ0FBQyxRQUFRLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ3BELE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ2pELE1BQU0sQ0FBQyxRQUFRLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ3BELE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBRWpELGlCQUFpQjtRQUNqQixTQUFTLENBQUMsS0FBSyxHQUFHLElBQUksTUFBTSxDQUFDLFFBQVEsQ0FBQyxJQUFJLEVBQUUsZUFBZSxFQUFFO1lBQzNELFlBQVksRUFBRSxHQUFHLFdBQVcsVUFBVSxXQUFXLEVBQUU7WUFDbkQsT0FBTyxFQUFFLE1BQU0sQ0FBQyxPQUFPLENBQUMsV0FBVztZQUNuQyxPQUFPLEVBQUUsZUFBZTtZQUN4QixJQUFJLEVBQUUsTUFBTSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVyxDQUFDO1lBQ3hDLE9BQU8sRUFBRSxHQUFHLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7WUFDakMsVUFBVSxFQUFFLEdBQUc7WUFDZixXQUFXLEVBQUUsaUJBQWlCO1NBQy9CLENBQUMsQ0FBQztRQUVILHNDQUFzQztRQUN0QyxNQUFNLENBQUMsS0FBSyxDQUFDLGtCQUFrQixDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNqRCxRQUFRLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNwQyxlQUFlLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUUzQyxpQkFBaUI7UUFDakIsU0FBUyxDQUFDLEtBQUssR0FBRyxJQUFJLE1BQU0sQ0FBQyxRQUFRLENBQUMsSUFBSSxFQUFFLGVBQWUsRUFBRTtZQUMzRCxZQUFZLEVBQUUsR0FBRyxXQUFXLFVBQVUsV0FBVyxFQUFFO1lBQ25ELE9BQU8sRUFBRSxNQUFNLENBQUMsT0FBTyxDQUFDLFdBQVc7WUFDbkMsT0FBTyxFQUFFLGVBQWU7WUFDeEIsSUFBSSxFQUFFLE1BQU0sQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQztZQUN4QyxPQUFPLEVBQUUsR0FBRyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDO1lBQ2pDLFVBQVUsRUFBRSxHQUFHO1lBQ2YsV0FBVyxFQUFFLGlCQUFpQjtTQUMvQixDQUFDLENBQUM7UUFFSCxzQ0FBc0M7UUFDdEMsTUFBTSxDQUFDLEtBQUssQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDakQsTUFBTSxDQUFDLFlBQVksQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDeEQsTUFBTSxDQUFDLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDbkQsTUFBTSxDQUFDLEtBQUssQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7UUFDakQsTUFBTSxDQUFDLEtBQUssQ0FBQyxrQkFBa0IsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7UUFFakQsa0JBQWtCO1FBQ2xCLFNBQVMsQ0FBQyxNQUFNLEdBQUcsSUFBSSxNQUFNLENBQUMsUUFBUSxDQUFDLElBQUksRUFBRSxnQkFBZ0IsRUFBRTtZQUM3RCxZQUFZLEVBQUUsR0FBRyxXQUFXLFdBQVcsV0FBVyxFQUFFO1lBQ3BELE9BQU8sRUFBRSxNQUFNLENBQUMsT0FBTyxDQUFDLFdBQVc7WUFDbkMsT0FBTyxFQUFFLGVBQWU7WUFDeEIsSUFBSSxFQUFFLE1BQU0sQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksQ0FBQztZQUN6QyxPQUFPLEVBQUUsR0FBRyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDO1lBQ2pDLFVBQVUsRUFBRSxHQUFHO1lBQ2YsV0FBVyxFQUFFLGlCQUFpQjtTQUMvQixDQUFDLENBQUM7UUFFSCx1Q0FBdUM7UUFDdkMsTUFBTSxDQUFDLEtBQUssQ0FBQyxhQUFhLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBRTdDLG9CQUFvQjtRQUNwQixTQUFTLENBQUMsUUFBUSxHQUFHLElBQUksTUFBTSxDQUFDLFFBQVEsQ0FBQyxJQUFJLEVBQUUsa0JBQWtCLEVBQUU7WUFDakUsWUFBWSxFQUFFLEdBQUcsV0FBVyxhQUFhLFdBQVcsRUFBRTtZQUN0RCxPQUFPLEVBQUUsTUFBTSxDQUFDLE9BQU8sQ0FBQyxXQUFXO1lBQ25DLE9BQU8sRUFBRSxlQUFlO1lBQ3hCLElBQUksRUFBRSxNQUFNLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxjQUFjLENBQUM7WUFDM0MsT0FBTyxFQUFFLEdBQUcsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNqQyxVQUFVLEVBQUUsR0FBRztZQUNmLFdBQVcsRUFBRSxpQkFBaUI7U0FDL0IsQ0FBQyxDQUFDO1FBRUgseUNBQXlDO1FBQ3pDLE1BQU0sQ0FBQyxRQUFRLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3ZELE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3BELE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3BELE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBRXBELG9CQUFvQjtRQUNwQixTQUFTLENBQUMsUUFBUSxHQUFHLElBQUksTUFBTSxDQUFDLFFBQVEsQ0FBQyxJQUFJLEVBQUUsa0JBQWtCLEVBQUU7WUFDakUsWUFBWSxFQUFFLEdBQUcsV0FBVyxhQUFhLFdBQVcsRUFBRTtZQUN0RCxPQUFPLEVBQUUsTUFBTSxDQUFDLE9BQU8sQ0FBQyxXQUFXO1lBQ25DLE9BQU8sRUFBRSxlQUFlO1lBQ3hCLElBQUksRUFBRSxNQUFNLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxjQUFjLENBQUM7WUFDM0MsT0FBTyxFQUFFLEdBQUcsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNqQyxVQUFVLEVBQUUsR0FBRztZQUNmLFdBQVcsRUFBRSxpQkFBaUI7U0FDL0IsQ0FBQyxDQUFDO1FBRUgseUNBQXlDO1FBQ3pDLE1BQU0sQ0FBQyxRQUFRLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3ZELE1BQU0sQ0FBQyxjQUFjLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQzdELE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3BELE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3BELE1BQU0sQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3BELFFBQVEsQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ3ZDLGVBQWUsQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBRTlDLE9BQU8sU0FBUyxDQUFDO0lBQ25CLENBQUM7SUFFTyxnQkFBZ0IsQ0FDdEIsV0FBbUIsRUFDbkIsV0FBbUIsRUFDbkIsU0FBNkMsRUFDN0MsUUFBMEI7UUFFMUIscUJBQXFCO1FBQ3JCLE1BQU0sR0FBRyxHQUFHLElBQUksVUFBVSxDQUFDLE9BQU8sQ0FBQyxJQUFJLEVBQUUsYUFBYSxFQUFFO1lBQ3RELFdBQVcsRUFBRSxHQUFHLFdBQVcsUUFBUSxXQUFXLEVBQUU7WUFDaEQsV0FBVyxFQUFFLG9CQUFvQixXQUFXLGNBQWM7WUFDMUQsMkJBQTJCLEVBQUU7Z0JBQzNCLFlBQVksRUFBRSxVQUFVLENBQUMsSUFBSSxDQUFDLFdBQVc7Z0JBQ3pDLFlBQVksRUFBRSxVQUFVLENBQUMsSUFBSSxDQUFDLFdBQVc7Z0JBQ3pDLFlBQVksRUFBRTtvQkFDWixjQUFjO29CQUNkLFlBQVk7b0JBQ1osZUFBZTtvQkFDZixXQUFXO29CQUNYLHNCQUFzQjtvQkFDdEIsa0JBQWtCO2lCQUNuQjthQUNGO1lBQ0QsYUFBYSxFQUFFO2dCQUNiLFNBQVMsRUFBRSxJQUFJO2dCQUNmLG1CQUFtQixFQUFFLElBQUk7Z0JBQ3pCLG9CQUFvQixFQUFFLElBQUk7YUFDM0I7U0FDRixDQUFDLENBQUM7UUFFSCxvQkFBb0I7UUFDcEIsTUFBTSxVQUFVLEdBQUcsSUFBSSxVQUFVLENBQUMsZUFBZSxDQUFDLElBQUksRUFBRSxtQkFBbUIsRUFBRTtZQUMzRSxPQUFPLEVBQUUsU0FBUyxDQUFDLFVBQVU7WUFDN0IsY0FBYyxFQUFFLG1CQUFtQjtZQUNuQyxlQUFlLEVBQUUsR0FBRyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDO1NBQ3pDLENBQUMsQ0FBQztRQUVILHVEQUF1RDtRQUN2RCxzRUFBc0U7UUFFdEUscUNBQXFDO1FBQ3JDLE1BQU0sTUFBTSxHQUFHLEdBQUcsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQzlDLE1BQU0sQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDO1FBRTVFLG9DQUFvQztRQUNwQyxNQUFNLElBQUksR0FBRyxHQUFHLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUMxQyxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztRQUN6RSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztRQUV4RSxrQ0FBa0M7UUFDbEMsTUFBTSxLQUFLLEdBQUcsR0FBRyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDNUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQ3hFLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFDSCxLQUFLLENBQUMsU0FBUyxDQUFDLE1BQU0sRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDekUsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUVILHVCQUF1QjtRQUN2QixNQUFNLFVBQVUsR0FBRyxLQUFLLENBQUMsV0FBVyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQzlDLFVBQVUsQ0FBQyxTQUFTLENBQUMsTUFBTSxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUM5RSxVQUFVO1NBQ1gsQ0FBQyxDQUFDO1FBRUgsTUFBTSxNQUFNLEdBQUcsS0FBSyxDQUFDLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUN6QyxNQUFNLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDekUsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUNILE1BQU0sQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUN6RSxVQUFVO1NBQ1gsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxDQUFDLFNBQVMsQ0FBQyxRQUFRLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQzVFLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFFSCw0QkFBNEI7UUFDNUIsTUFBTSxTQUFTLEdBQUcsTUFBTSxDQUFDLFdBQVcsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUM5QyxTQUFTLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDNUUsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUVILG1CQUFtQjtRQUNuQixNQUFNLFdBQVcsR0FBRyxNQUFNLENBQUMsV0FBVyxDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBQ2xELFdBQVcsQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUM5RSxVQUFVO1NBQ1gsQ0FBQyxDQUFDO1FBRUgsaUJBQWlCO1FBQ2pCLE1BQU0sUUFBUSxHQUFHLE1BQU0sQ0FBQyxXQUFXLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDNUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQzVFLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFDSCxRQUFRLENBQUMsU0FBUyxDQUFDLFFBQVEsRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDOUUsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUVILHFCQUFxQjtRQUNyQixNQUFNLFlBQVksR0FBRyxNQUFNLENBQUMsV0FBVyxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ3BELFlBQVksQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUMvRSxVQUFVO1NBQ1gsQ0FBQyxDQUFDO1FBQ0gsWUFBWSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQ2hGLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFFSCxrQ0FBa0M7UUFDbEMsTUFBTSxLQUFLLEdBQUcsR0FBRyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDNUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQ3hFLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFDSCxLQUFLLENBQUMsU0FBUyxDQUFDLE1BQU0sRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDekUsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUNILE1BQU0sTUFBTSxHQUFHLEtBQUssQ0FBQyxXQUFXLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDekMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQ3pFLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFDSCxNQUFNLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDekUsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUVILHlCQUF5QjtRQUN6QixNQUFNLE9BQU8sR0FBRyxLQUFLLENBQUMsV0FBVyxDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBQzdDLE9BQU8sQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUMxRSxVQUFVO1NBQ1gsQ0FBQyxDQUFDO1FBQ0gsT0FBTyxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQzFFLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFFSCw0QkFBNEI7UUFDNUIsTUFBTSxVQUFVLEdBQUcsT0FBTyxDQUFDLFdBQVcsQ0FBQyxhQUFhLENBQUMsQ0FBQztRQUN0RCxVQUFVLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDN0UsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUVILGtDQUFrQztRQUNsQyxNQUFNLEtBQUssR0FBRyxHQUFHLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUM1QyxLQUFLLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDeEUsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUNILEtBQUssQ0FBQyxTQUFTLENBQUMsTUFBTSxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUN6RSxVQUFVO1NBQ1gsQ0FBQyxDQUFDO1FBRUgsd0JBQXdCO1FBQ3hCLE1BQU0sV0FBVyxHQUFHLEtBQUssQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDaEQsV0FBVyxDQUFDLFNBQVMsQ0FBQyxNQUFNLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQy9FLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFFSCxNQUFNLE9BQU8sR0FBRyxLQUFLLENBQUMsV0FBVyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzFDLE9BQU8sQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUMxRSxVQUFVO1NBQ1gsQ0FBQyxDQUFDO1FBQ0gsT0FBTyxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQzFFLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFDSCxPQUFPLENBQUMsU0FBUyxDQUFDLFFBQVEsRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDN0UsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUVILHFDQUFxQztRQUNyQyxNQUFNLFFBQVEsR0FBRyxHQUFHLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUNsRCxRQUFRLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLEVBQUU7WUFDOUUsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUNILFFBQVEsQ0FBQyxTQUFTLENBQUMsTUFBTSxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsRUFBRTtZQUMvRSxVQUFVO1NBQ1gsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxRQUFRLEdBQUcsUUFBUSxDQUFDLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUM5QyxRQUFRLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLEVBQUU7WUFDOUUsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUNILFFBQVEsQ0FBQyxTQUFTLENBQUMsS0FBSyxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsRUFBRTtZQUM5RSxVQUFVO1NBQ1gsQ0FBQyxDQUFDO1FBQ0gsUUFBUSxDQUFDLFNBQVMsQ0FBQyxRQUFRLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxFQUFFO1lBQ2pGLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFFSCxxQ0FBcUM7UUFDckMsTUFBTSxRQUFRLEdBQUcsR0FBRyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDbEQsUUFBUSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxFQUFFO1lBQzlFLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFDSCxRQUFRLENBQUMsU0FBUyxDQUFDLE1BQU0sRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLEVBQUU7WUFDL0UsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUNILE1BQU0sU0FBUyxHQUFHLFFBQVEsQ0FBQyxXQUFXLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDL0MsU0FBUyxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsSUFBSSxVQUFVLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxFQUFFO1lBQy9FLFVBQVU7U0FDWCxDQUFDLENBQUM7UUFDSCxTQUFTLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxJQUFJLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLEVBQUU7WUFDL0UsVUFBVTtTQUNYLENBQUMsQ0FBQztRQUNILFNBQVMsQ0FBQyxTQUFTLENBQUMsUUFBUSxFQUFFLElBQUksVUFBVSxDQUFDLGlCQUFpQixDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsRUFBRTtZQUNsRixVQUFVO1NBQ1gsQ0FBQyxDQUFDO1FBRUgsT0FBTyxHQUFHLENBQUM7SUFDYixDQUFDO0lBRU8sa0JBQWtCLENBQUMsVUFBa0IsRUFBRSxjQUFzQjtRQUNuRSw2Q0FBNkM7UUFDN0Msa0RBQWtEO1FBQ2xELE9BQU8sQ0FBQyxHQUFHLENBQUMsaUJBQWlCLFVBQVUscUJBQXFCLGNBQWMsMkJBQTJCLENBQUMsQ0FBQztJQUN6RyxDQUFDO0lBRU8sYUFBYSxDQUNuQixXQUFtQixFQUNuQixVQUFtQixFQUNuQixRQUFpQyxFQUNqQyxlQUF3QztRQUV4QyxrQkFBa0I7UUFDbEIsSUFBSSxHQUFHLENBQUMsU0FBUyxDQUFDLElBQUksRUFBRSxlQUFlLEVBQUU7WUFDdkMsS0FBSyxFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsR0FBRztZQUNuQixXQUFXLEVBQUUsaUJBQWlCO1lBQzlCLFVBQVUsRUFBRSxHQUFHLElBQUksQ0FBQyxTQUFTLGdCQUFnQjtTQUM5QyxDQUFDLENBQUM7UUFFSCxlQUFlO1FBQ2YsSUFBSSxHQUFHLENBQUMsU0FBUyxDQUFDLElBQUksRUFBRSxZQUFZLEVBQUU7WUFDcEMsS0FBSyxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsVUFBVTtZQUMvQixXQUFXLEVBQUUsc0JBQXNCO1lBQ25DLFVBQVUsRUFBRSxHQUFHLElBQUksQ0FBQyxTQUFTLGFBQWE7U0FDM0MsQ0FBQyxDQUFDO1FBRUgsc0JBQXNCO1FBQ3RCLElBQUksR0FBRyxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUsa0JBQWtCLEVBQUU7WUFDMUMsS0FBSyxFQUFFLElBQUksQ0FBQyxjQUFjLENBQUMsZ0JBQWdCO1lBQzNDLFdBQVcsRUFBRSw2QkFBNkI7WUFDMUMsVUFBVSxFQUFFLEdBQUcsSUFBSSxDQUFDLFNBQVMsbUJBQW1CO1NBQ2pELENBQUMsQ0FBQztRQUVILG9DQUFvQztRQUNwQyxJQUFJLFVBQVUsRUFBRSxDQUFDO1lBQ2YsSUFBSSxHQUFHLENBQUMsU0FBUyxDQUFDLElBQUksRUFBRSxpQkFBaUIsRUFBRTtnQkFDekMsS0FBSyxFQUFFLFdBQVcsVUFBVSxFQUFFO2dCQUM5QixXQUFXLEVBQUUsbUJBQW1CO2dCQUNoQyxVQUFVLEVBQUUsR0FBRyxJQUFJLENBQUMsU0FBUyxrQkFBa0I7YUFDaEQsQ0FBQyxDQUFDO1FBQ0wsQ0FBQztRQUVELGdCQUFnQjtRQUNoQixJQUFJLFFBQVEsRUFBRSxDQUFDO1lBQ2IsSUFBSSxHQUFHLENBQUMsU0FBUyxDQUFDLElBQUksRUFBRSxhQUFhLEVBQUU7Z0JBQ3JDLEtBQUssRUFBRSxRQUFRLENBQUMsU0FBUztnQkFDekIsV0FBVyxFQUFFLDZCQUE2QjtnQkFDMUMsVUFBVSxFQUFFLEdBQUcsSUFBSSxDQUFDLFNBQVMsY0FBYzthQUM1QyxDQUFDLENBQUM7UUFDTCxDQUFDO1FBRUQsd0JBQXdCO1FBQ3hCLElBQUksZUFBZSxFQUFFLENBQUM7WUFDcEIsSUFBSSxHQUFHLENBQUMsU0FBUyxDQUFDLElBQUksRUFBRSxvQkFBb0IsRUFBRTtnQkFDNUMsS0FBSyxFQUFFLGVBQWUsQ0FBQyxTQUFTO2dCQUNoQyxXQUFXLEVBQUUsOEJBQThCO2dCQUMzQyxVQUFVLEVBQUUsR0FBRyxJQUFJLENBQUMsU0FBUyxxQkFBcUI7YUFDbkQsQ0FBQyxDQUFDO1FBQ0wsQ0FBQztRQUVELHVCQUF1QjtRQUN2QixNQUFNLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLElBQUksRUFBRSxLQUFLLENBQUMsRUFBRSxFQUFFO1lBQ3BELElBQUksR0FBRyxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLFdBQVcsRUFBRTtnQkFDbEYsS0FBSyxFQUFFLEtBQUssQ0FBQyxTQUFTO2dCQUN0QixXQUFXLEVBQUUsR0FBRyxJQUFJLHNCQUFzQjtnQkFDMUMsVUFBVSxFQUFFLEdBQUcsSUFBSSxDQUFDLFNBQVMsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLFdBQVc7YUFDekYsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDO0NBQ0Y7QUE3b0JELG9EQTZvQkMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBjZGsgZnJvbSAnYXdzLWNkay1saWInO1xuaW1wb3J0ICogYXMgYXBpZ2F0ZXdheSBmcm9tICdhd3MtY2RrLWxpYi9hd3MtYXBpZ2F0ZXdheSc7XG5pbXBvcnQgKiBhcyBjb2duaXRvIGZyb20gJ2F3cy1jZGstbGliL2F3cy1jb2duaXRvJztcbmltcG9ydCAqIGFzIGR5bmFtb2RiIGZyb20gJ2F3cy1jZGstbGliL2F3cy1keW5hbW9kYic7XG5pbXBvcnQgKiBhcyBsYW1iZGEgZnJvbSAnYXdzLWNkay1saWIvYXdzLWxhbWJkYSc7XG5pbXBvcnQgKiBhcyBub2RlanMgZnJvbSAnYXdzLWNkay1saWIvYXdzLWxhbWJkYS1ub2RlanMnO1xuaW1wb3J0ICogYXMgc2VjcmV0c21hbmFnZXIgZnJvbSAnYXdzLWNkay1saWIvYXdzLXNlY3JldHNtYW5hZ2VyJztcbmltcG9ydCAqIGFzIGlhbSBmcm9tICdhd3MtY2RrLWxpYi9hd3MtaWFtJztcbmltcG9ydCB7IENvbnN0cnVjdCB9IGZyb20gJ2NvbnN0cnVjdHMnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEdhbWVGbGV4QmFja2VuZFN0YWNrUHJvcHMgZXh0ZW5kcyBjZGsuU3RhY2tQcm9wcyB7XG4gIGVudmlyb25tZW50OiBzdHJpbmc7XG4gIHByb2plY3ROYW1lOiBzdHJpbmc7XG4gIGRvbWFpbk5hbWU/OiBzdHJpbmc7XG4gIGNlcnRpZmljYXRlQXJuPzogc3RyaW5nO1xuICByMkJ1Y2tldE5hbWU6IHN0cmluZztcbiAgcjJQdWJsaWNVcmw6IHN0cmluZztcbn1cblxuZXhwb3J0IGNsYXNzIEdhbWVGbGV4QmFja2VuZFN0YWNrIGV4dGVuZHMgY2RrLlN0YWNrIHtcbiAgcHVibGljIHJlYWRvbmx5IHVzZXJQb29sOiBjb2duaXRvLlVzZXJQb29sO1xuICBwdWJsaWMgcmVhZG9ubHkgdXNlclBvb2xDbGllbnQ6IGNvZ25pdG8uVXNlclBvb2xDbGllbnQ7XG4gIHB1YmxpYyByZWFkb25seSBhcGk6IGFwaWdhdGV3YXkuUmVzdEFwaTtcbiAgcHVibGljIHJlYWRvbmx5IHRhYmxlczogeyBba2V5OiBzdHJpbmddOiBkeW5hbW9kYi5UYWJsZSB9O1xuXG4gIGNvbnN0cnVjdG9yKHNjb3BlOiBDb25zdHJ1Y3QsIGlkOiBzdHJpbmcsIHByb3BzOiBHYW1lRmxleEJhY2tlbmRTdGFja1Byb3BzKSB7XG4gICAgc3VwZXIoc2NvcGUsIGlkLCBwcm9wcyk7XG5cbiAgICBjb25zdCB7IGVudmlyb25tZW50LCBwcm9qZWN0TmFtZSwgZG9tYWluTmFtZSwgY2VydGlmaWNhdGVBcm4sIHIyQnVja2V0TmFtZSwgcjJQdWJsaWNVcmwgfSA9IHByb3BzO1xuXG4gICAgLy8gQ29uZGl0aW9uc1xuICAgIGNvbnN0IGlzUHJvZHVjdGlvbiA9IGVudmlyb25tZW50ID09PSAncHJvZHVjdGlvbic7XG4gICAgY29uc3QgaXNTdGFnaW5nID0gZW52aXJvbm1lbnQgPT09ICdzdGFnaW5nJztcbiAgICBjb25zdCBpc1Byb2R1Y3Rpb25PclN0YWdpbmcgPSBpc1Byb2R1Y3Rpb24gfHwgaXNTdGFnaW5nO1xuICAgIGNvbnN0IGhhc0N1c3RvbURvbWFpbiA9ICEhZG9tYWluTmFtZTtcblxuICAgIC8vIEFXUyBTZWNyZXRzIE1hbmFnZXIgZm9yIFIyIENvbmZpZ3VyYXRpb25cbiAgICAvLyBVc2UgZnJvbVNlY3JldE5hbWVWMiB0byByZWZlcmVuY2UgZXhpc3Rpbmcgc2VjcmV0cyBvciBjcmVhdGUgbmV3IG9uZXNcbiAgICBjb25zdCByMlNlY3JldE5hbWUgPSBgJHtwcm9qZWN0TmFtZX0tcjItY29uZmlnLSR7ZW52aXJvbm1lbnR9YDtcbiAgICBjb25zdCByMlNlY3JldCA9IHNlY3JldHNtYW5hZ2VyLlNlY3JldC5mcm9tU2VjcmV0TmFtZVYyKHRoaXMsICdSMlNlY3JldCcsIHIyU2VjcmV0TmFtZSk7XG5cbiAgICAvLyBBV1MgU2VjcmV0cyBNYW5hZ2VyIGZvciBHZW5lcmFsIENvbmZpZ3VyYXRpb25cbiAgICBjb25zdCBhcHBDb25maWdTZWNyZXROYW1lID0gYCR7cHJvamVjdE5hbWV9LWFwcC1jb25maWctJHtlbnZpcm9ubWVudH1gO1xuICAgIGNvbnN0IGFwcENvbmZpZ1NlY3JldCA9IHNlY3JldHNtYW5hZ2VyLlNlY3JldC5mcm9tU2VjcmV0TmFtZVYyKHRoaXMsICdBcHBDb25maWdTZWNyZXQnLCBhcHBDb25maWdTZWNyZXROYW1lKTtcblxuICAgIC8vIE5vdGU6IENhbm5vdCBhcHBseSByZW1vdmFsIHBvbGljaWVzIHRvIGltcG9ydGVkIHNlY3JldHNcbiAgICAvLyBTZWNyZXRzIGFyZSBtYW5hZ2VkIGV4dGVybmFsbHkgYW5kIHdpbGwgcGVyc2lzdCByZWdhcmRsZXNzXG5cbiAgICAvLyBDb2duaXRvIFVzZXIgUG9vbFxuICAgIHRoaXMudXNlclBvb2wgPSBuZXcgY29nbml0by5Vc2VyUG9vbCh0aGlzLCAnVXNlclBvb2wnLCB7XG4gICAgICB1c2VyUG9vbE5hbWU6IGAke3Byb2plY3ROYW1lfS11c2Vycy0ke2Vudmlyb25tZW50fWAsXG4gICAgICBhdXRvVmVyaWZ5OiB7IGVtYWlsOiB0cnVlIH0sXG4gICAgICBzaWduSW5BbGlhc2VzOiB7IGVtYWlsOiB0cnVlIH0sXG4gICAgICBwYXNzd29yZFBvbGljeToge1xuICAgICAgICBtaW5MZW5ndGg6IDgsXG4gICAgICAgIHJlcXVpcmVVcHBlcmNhc2U6IHRydWUsXG4gICAgICAgIHJlcXVpcmVMb3dlcmNhc2U6IHRydWUsXG4gICAgICAgIHJlcXVpcmVEaWdpdHM6IHRydWUsXG4gICAgICAgIHJlcXVpcmVTeW1ib2xzOiBmYWxzZSxcbiAgICAgIH0sXG4gICAgICBkZWxldGlvblByb3RlY3Rpb246IGlzUHJvZHVjdGlvbk9yU3RhZ2luZyxcbiAgICAgIHJlbW92YWxQb2xpY3k6IGlzUHJvZHVjdGlvbk9yU3RhZ2luZyA/IGNkay5SZW1vdmFsUG9saWN5LlJFVEFJTiA6IGNkay5SZW1vdmFsUG9saWN5LkRFU1RST1ksXG4gICAgfSk7XG5cbiAgICAvLyBDb2duaXRvIFVzZXIgUG9vbCBDbGllbnRcbiAgICB0aGlzLnVzZXJQb29sQ2xpZW50ID0gbmV3IGNvZ25pdG8uVXNlclBvb2xDbGllbnQodGhpcywgJ1VzZXJQb29sQ2xpZW50Jywge1xuICAgICAgdXNlclBvb2w6IHRoaXMudXNlclBvb2wsXG4gICAgICB1c2VyUG9vbENsaWVudE5hbWU6IGAke3Byb2plY3ROYW1lfS1jbGllbnQtJHtlbnZpcm9ubWVudH1gLFxuICAgICAgZ2VuZXJhdGVTZWNyZXQ6IGZhbHNlLFxuICAgICAgYXV0aEZsb3dzOiB7XG4gICAgICAgIGFkbWluVXNlclBhc3N3b3JkOiB0cnVlLFxuICAgICAgICB1c2VyUGFzc3dvcmQ6IHRydWUsXG4gICAgICAgIHVzZXJTcnA6IHRydWUsXG4gICAgICB9LFxuICAgICAgcmVmcmVzaFRva2VuVmFsaWRpdHk6IGNkay5EdXJhdGlvbi5kYXlzKDMwKSxcbiAgICAgIGFjY2Vzc1Rva2VuVmFsaWRpdHk6IGNkay5EdXJhdGlvbi5taW51dGVzKDYwKSxcbiAgICAgIGlkVG9rZW5WYWxpZGl0eTogY2RrLkR1cmF0aW9uLm1pbnV0ZXMoNjApLFxuICAgIH0pO1xuXG4gICAgLy8gRHluYW1vREIgVGFibGVzXG4gICAgdGhpcy50YWJsZXMgPSB0aGlzLmNyZWF0ZUR5bmFtb0RCVGFibGVzKHByb2plY3ROYW1lLCBlbnZpcm9ubWVudCwgaXNQcm9kdWN0aW9uT3JTdGFnaW5nKTtcblxuICAgIC8vIExhbWJkYSBGdW5jdGlvbnNcbiAgICBjb25zdCBsYW1iZGFGdW5jdGlvbnMgPSB0aGlzLmNyZWF0ZUxhbWJkYUZ1bmN0aW9ucyhcbiAgICAgIHByb2plY3ROYW1lLFxuICAgICAgZW52aXJvbm1lbnQsXG4gICAgICB0aGlzLnVzZXJQb29sLFxuICAgICAgdGhpcy51c2VyUG9vbENsaWVudCxcbiAgICAgIHRoaXMudGFibGVzLFxuICAgICAgcjJTZWNyZXQsXG4gICAgICBhcHBDb25maWdTZWNyZXRcbiAgICApO1xuXG4gICAgLy8gQVBJIEdhdGV3YXlcbiAgICB0aGlzLmFwaSA9IHRoaXMuY3JlYXRlQXBpR2F0ZXdheShwcm9qZWN0TmFtZSwgZW52aXJvbm1lbnQsIGxhbWJkYUZ1bmN0aW9ucywgdGhpcy51c2VyUG9vbCk7XG5cbiAgICAvLyBDdXN0b20gRG9tYWluIChpZiBzcGVjaWZpZWQpXG4gICAgaWYgKGhhc0N1c3RvbURvbWFpbiAmJiBjZXJ0aWZpY2F0ZUFybikge1xuICAgICAgdGhpcy5jcmVhdGVDdXN0b21Eb21haW4oZG9tYWluTmFtZSEsIGNlcnRpZmljYXRlQXJuKTtcbiAgICB9XG5cbiAgICAvLyBPdXRwdXRzXG4gICAgdGhpcy5jcmVhdGVPdXRwdXRzKGVudmlyb25tZW50LCBkb21haW5OYW1lLCByMlNlY3JldCwgYXBwQ29uZmlnU2VjcmV0KTtcbiAgfVxuXG4gIHByaXZhdGUgY3JlYXRlRHluYW1vREJUYWJsZXMoXG4gICAgcHJvamVjdE5hbWU6IHN0cmluZyxcbiAgICBlbnZpcm9ubWVudDogc3RyaW5nLFxuICAgIGlzUHJvZHVjdGlvbk9yU3RhZ2luZzogYm9vbGVhblxuICApOiB7IFtrZXk6IHN0cmluZ106IGR5bmFtb2RiLlRhYmxlIH0ge1xuICAgIGNvbnN0IHRhYmxlQ29uZmlnID0ge1xuICAgICAgYmlsbGluZ01vZGU6IGR5bmFtb2RiLkJpbGxpbmdNb2RlLlBBWV9QRVJfUkVRVUVTVCxcbiAgICAgIGRlbGV0aW9uUHJvdGVjdGlvbjogaXNQcm9kdWN0aW9uT3JTdGFnaW5nLFxuICAgICAgcG9pbnRJblRpbWVSZWNvdmVyeTogaXNQcm9kdWN0aW9uT3JTdGFnaW5nLFxuICAgICAgcmVtb3ZhbFBvbGljeTogaXNQcm9kdWN0aW9uT3JTdGFnaW5nID8gY2RrLlJlbW92YWxQb2xpY3kuUkVUQUlOIDogY2RrLlJlbW92YWxQb2xpY3kuREVTVFJPWSxcbiAgICB9O1xuXG4gICAgY29uc3QgdGFibGVzOiB7IFtrZXk6IHN0cmluZ106IGR5bmFtb2RiLlRhYmxlIH0gPSB7fTtcblxuICAgIC8vIFBvc3RzIFRhYmxlXG4gICAgdGFibGVzLnBvc3RzID0gbmV3IGR5bmFtb2RiLlRhYmxlKHRoaXMsICdQb3N0c1RhYmxlJywge1xuICAgICAgLi4udGFibGVDb25maWcsXG4gICAgICB0YWJsZU5hbWU6IGAke3Byb2plY3ROYW1lfS0ke2Vudmlyb25tZW50fS1Qb3N0c2AsXG4gICAgICBwYXJ0aXRpb25LZXk6IHsgbmFtZTogJ2lkJywgdHlwZTogZHluYW1vZGIuQXR0cmlidXRlVHlwZS5TVFJJTkcgfSxcbiAgICB9KTtcblxuICAgIC8vIE1lZGlhIFRhYmxlXG4gICAgdGFibGVzLm1lZGlhID0gbmV3IGR5bmFtb2RiLlRhYmxlKHRoaXMsICdNZWRpYVRhYmxlJywge1xuICAgICAgLi4udGFibGVDb25maWcsXG4gICAgICB0YWJsZU5hbWU6IGAke3Byb2plY3ROYW1lfS0ke2Vudmlyb25tZW50fS1NZWRpYWAsXG4gICAgICBwYXJ0aXRpb25LZXk6IHsgbmFtZTogJ2lkJywgdHlwZTogZHluYW1vZGIuQXR0cmlidXRlVHlwZS5TVFJJTkcgfSxcbiAgICB9KTtcblxuICAgIC8vIFVzZXIgUHJvZmlsZXMgVGFibGVcbiAgICB0YWJsZXMudXNlclByb2ZpbGVzID0gbmV3IGR5bmFtb2RiLlRhYmxlKHRoaXMsICdVc2VyUHJvZmlsZXNUYWJsZScsIHtcbiAgICAgIC4uLnRhYmxlQ29uZmlnLFxuICAgICAgdGFibGVOYW1lOiBgJHtwcm9qZWN0TmFtZX0tJHtlbnZpcm9ubWVudH0tVXNlclByb2ZpbGVzYCxcbiAgICAgIHBhcnRpdGlvbktleTogeyBuYW1lOiAndXNlcklkJywgdHlwZTogZHluYW1vZGIuQXR0cmlidXRlVHlwZS5TVFJJTkcgfSxcbiAgICB9KTtcblxuICAgIC8vIENvbW1lbnRzIFRhYmxlXG4gICAgdGFibGVzLmNvbW1lbnRzID0gbmV3IGR5bmFtb2RiLlRhYmxlKHRoaXMsICdDb21tZW50c1RhYmxlJywge1xuICAgICAgLi4udGFibGVDb25maWcsXG4gICAgICB0YWJsZU5hbWU6IGAke3Byb2plY3ROYW1lfS0ke2Vudmlyb25tZW50fS1Db21tZW50c2AsXG4gICAgICBwYXJ0aXRpb25LZXk6IHsgbmFtZTogJ2lkJywgdHlwZTogZHluYW1vZGIuQXR0cmlidXRlVHlwZS5TVFJJTkcgfSxcbiAgICB9KTtcbiAgICB0YWJsZXMuY29tbWVudHMuYWRkR2xvYmFsU2Vjb25kYXJ5SW5kZXgoe1xuICAgICAgaW5kZXhOYW1lOiAncG9zdElkLWluZGV4JyxcbiAgICAgIHBhcnRpdGlvbktleTogeyBuYW1lOiAncG9zdElkJywgdHlwZTogZHluYW1vZGIuQXR0cmlidXRlVHlwZS5TVFJJTkcgfSxcbiAgICB9KTtcblxuICAgIC8vIExpa2VzIFRhYmxlXG4gICAgdGFibGVzLmxpa2VzID0gbmV3IGR5bmFtb2RiLlRhYmxlKHRoaXMsICdMaWtlc1RhYmxlJywge1xuICAgICAgLi4udGFibGVDb25maWcsXG4gICAgICB0YWJsZU5hbWU6IGAke3Byb2plY3ROYW1lfS0ke2Vudmlyb25tZW50fS1MaWtlc2AsXG4gICAgICBwYXJ0aXRpb25LZXk6IHsgbmFtZTogJ3Bvc3RJZCcsIHR5cGU6IGR5bmFtb2RiLkF0dHJpYnV0ZVR5cGUuU1RSSU5HIH0sXG4gICAgICBzb3J0S2V5OiB7IG5hbWU6ICd1c2VySWQnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuICAgIHRhYmxlcy5saWtlcy5hZGRHbG9iYWxTZWNvbmRhcnlJbmRleCh7XG4gICAgICBpbmRleE5hbWU6ICd1c2VySWQtaW5kZXgnLFxuICAgICAgcGFydGl0aW9uS2V5OiB7IG5hbWU6ICd1c2VySWQnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuXG4gICAgLy8gRm9sbG93cyBUYWJsZVxuICAgIHRhYmxlcy5mb2xsb3dzID0gbmV3IGR5bmFtb2RiLlRhYmxlKHRoaXMsICdGb2xsb3dzVGFibGUnLCB7XG4gICAgICAuLi50YWJsZUNvbmZpZyxcbiAgICAgIHRhYmxlTmFtZTogYCR7cHJvamVjdE5hbWV9LSR7ZW52aXJvbm1lbnR9LUZvbGxvd3NgLFxuICAgICAgcGFydGl0aW9uS2V5OiB7IG5hbWU6ICdmb2xsb3dlcklkJywgdHlwZTogZHluYW1vZGIuQXR0cmlidXRlVHlwZS5TVFJJTkcgfSxcbiAgICAgIHNvcnRLZXk6IHsgbmFtZTogJ2ZvbGxvd2luZ0lkJywgdHlwZTogZHluYW1vZGIuQXR0cmlidXRlVHlwZS5TVFJJTkcgfSxcbiAgICB9KTtcblxuICAgIC8vIENoYW5uZWxzIFRhYmxlXG4gICAgdGFibGVzLmNoYW5uZWxzID0gbmV3IGR5bmFtb2RiLlRhYmxlKHRoaXMsICdDaGFubmVsc1RhYmxlJywge1xuICAgICAgLi4udGFibGVDb25maWcsXG4gICAgICB0YWJsZU5hbWU6IGAke3Byb2plY3ROYW1lfS0ke2Vudmlyb25tZW50fS1DaGFubmVsc2AsXG4gICAgICBwYXJ0aXRpb25LZXk6IHsgbmFtZTogJ2lkJywgdHlwZTogZHluYW1vZGIuQXR0cmlidXRlVHlwZS5TVFJJTkcgfSxcbiAgICB9KTtcbiAgICB0YWJsZXMuY2hhbm5lbHMuYWRkR2xvYmFsU2Vjb25kYXJ5SW5kZXgoe1xuICAgICAgaW5kZXhOYW1lOiAnb3duZXJJZC1pbmRleCcsXG4gICAgICBwYXJ0aXRpb25LZXk6IHsgbmFtZTogJ293bmVySWQnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuXG4gICAgLy8gQ2hhbm5lbCBNZW1iZXJzIFRhYmxlXG4gICAgdGFibGVzLmNoYW5uZWxNZW1iZXJzID0gbmV3IGR5bmFtb2RiLlRhYmxlKHRoaXMsICdDaGFubmVsTWVtYmVyc1RhYmxlJywge1xuICAgICAgLi4udGFibGVDb25maWcsXG4gICAgICB0YWJsZU5hbWU6IGAke3Byb2plY3ROYW1lfS0ke2Vudmlyb25tZW50fS1DaGFubmVsTWVtYmVyc2AsXG4gICAgICBwYXJ0aXRpb25LZXk6IHsgbmFtZTogJ2NoYW5uZWxJZCcsIHR5cGU6IGR5bmFtb2RiLkF0dHJpYnV0ZVR5cGUuU1RSSU5HIH0sXG4gICAgICBzb3J0S2V5OiB7IG5hbWU6ICd1c2VySWQnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuICAgIHRhYmxlcy5jaGFubmVsTWVtYmVycy5hZGRHbG9iYWxTZWNvbmRhcnlJbmRleCh7XG4gICAgICBpbmRleE5hbWU6ICd1c2VySWQtaW5kZXgnLFxuICAgICAgcGFydGl0aW9uS2V5OiB7IG5hbWU6ICd1c2VySWQnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuXG4gICAgLy8gUmVmbGV4ZXMgVGFibGVcbiAgICB0YWJsZXMucmVmbGV4ZXMgPSBuZXcgZHluYW1vZGIuVGFibGUodGhpcywgJ1JlZmxleGVzVGFibGUnLCB7XG4gICAgICAuLi50YWJsZUNvbmZpZyxcbiAgICAgIHRhYmxlTmFtZTogYCR7cHJvamVjdE5hbWV9LSR7ZW52aXJvbm1lbnR9LVJlZmxleGVzYCxcbiAgICAgIHBhcnRpdGlvbktleTogeyBuYW1lOiAnaWQnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuICAgIHRhYmxlcy5yZWZsZXhlcy5hZGRHbG9iYWxTZWNvbmRhcnlJbmRleCh7XG4gICAgICBpbmRleE5hbWU6ICdwb3N0SWQtaW5kZXgnLFxuICAgICAgcGFydGl0aW9uS2V5OiB7IG5hbWU6ICdwb3N0SWQnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuICAgIHRhYmxlcy5yZWZsZXhlcy5hZGRHbG9iYWxTZWNvbmRhcnlJbmRleCh7XG4gICAgICBpbmRleE5hbWU6ICd1c2VySWQtaW5kZXgnLFxuICAgICAgcGFydGl0aW9uS2V5OiB7IG5hbWU6ICd1c2VySWQnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuXG4gICAgLy8gVXNlcnMgVGFibGVcbiAgICB0YWJsZXMudXNlcnMgPSBuZXcgZHluYW1vZGIuVGFibGUodGhpcywgJ1VzZXJzVGFibGUnLCB7XG4gICAgICAuLi50YWJsZUNvbmZpZyxcbiAgICAgIHRhYmxlTmFtZTogYCR7cHJvamVjdE5hbWV9LSR7ZW52aXJvbm1lbnR9LVVzZXJzYCxcbiAgICAgIHBhcnRpdGlvbktleTogeyBuYW1lOiAnaWQnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuICAgIHRhYmxlcy51c2Vycy5hZGRHbG9iYWxTZWNvbmRhcnlJbmRleCh7XG4gICAgICBpbmRleE5hbWU6ICdFbWFpbEluZGV4JyxcbiAgICAgIHBhcnRpdGlvbktleTogeyBuYW1lOiAnZW1haWwnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuICAgIHRhYmxlcy51c2Vycy5hZGRHbG9iYWxTZWNvbmRhcnlJbmRleCh7XG4gICAgICBpbmRleE5hbWU6ICdVc2VybmFtZUluZGV4JyxcbiAgICAgIHBhcnRpdGlvbktleTogeyBuYW1lOiAndXNlcm5hbWUnLCB0eXBlOiBkeW5hbW9kYi5BdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgIH0pO1xuICAgIHRhYmxlcy51c2Vycy5hZGRHbG9iYWxTZWNvbmRhcnlJbmRleCh7XG4gICAgICBpbmRleE5hbWU6ICdDb2duaXRvVXNlcklkSW5kZXgnLFxuICAgICAgcGFydGl0aW9uS2V5OiB7IG5hbWU6ICdjb2duaXRvVXNlcklkJywgdHlwZTogZHluYW1vZGIuQXR0cmlidXRlVHlwZS5TVFJJTkcgfSxcbiAgICB9KTtcblxuICAgIHJldHVybiB0YWJsZXM7XG4gIH1cblxuICBwcml2YXRlIGNyZWF0ZUxhbWJkYUZ1bmN0aW9ucyhcbiAgICBwcm9qZWN0TmFtZTogc3RyaW5nLFxuICAgIGVudmlyb25tZW50OiBzdHJpbmcsXG4gICAgdXNlclBvb2w6IGNvZ25pdG8uVXNlclBvb2wsXG4gICAgdXNlclBvb2xDbGllbnQ6IGNvZ25pdG8uVXNlclBvb2xDbGllbnQsXG4gICAgdGFibGVzOiB7IFtrZXk6IHN0cmluZ106IGR5bmFtb2RiLlRhYmxlIH0sXG4gICAgcjJTZWNyZXQ6IHNlY3JldHNtYW5hZ2VyLklTZWNyZXQsXG4gICAgYXBwQ29uZmlnU2VjcmV0OiBzZWNyZXRzbWFuYWdlci5JU2VjcmV0XG4gICk6IHsgW2tleTogc3RyaW5nXTogbGFtYmRhLkZ1bmN0aW9uIH0ge1xuICAgIGNvbnN0IGZ1bmN0aW9uczogeyBba2V5OiBzdHJpbmddOiBsYW1iZGEuRnVuY3Rpb24gfSA9IHt9O1xuXG4gICAgLy8gQ29tbW9uIGVudmlyb25tZW50IHZhcmlhYmxlcyBmb3IgYWxsIExhbWJkYSBmdW5jdGlvbnNcbiAgICBjb25zdCBjb21tb25FbnZpcm9ubWVudCA9IHtcbiAgICAgIEVOVklST05NRU5UOiBlbnZpcm9ubWVudCxcbiAgICAgIFBST0pFQ1RfTkFNRTogcHJvamVjdE5hbWUsXG4gICAgICBVU0VSX1BPT0xfSUQ6IHVzZXJQb29sLnVzZXJQb29sSWQsXG4gICAgICBVU0VSX1BPT0xfQ0xJRU5UX0lEOiB1c2VyUG9vbENsaWVudC51c2VyUG9vbENsaWVudElkLFxuICAgICAgUE9TVFNfVEFCTEU6IHRhYmxlcy5wb3N0cy50YWJsZU5hbWUsXG4gICAgICBNRURJQV9UQUJMRTogdGFibGVzLm1lZGlhLnRhYmxlTmFtZSxcbiAgICAgIFVTRVJfUFJPRklMRVNfVEFCTEU6IHRhYmxlcy51c2VyUHJvZmlsZXMudGFibGVOYW1lLFxuICAgICAgQ09NTUVOVFNfVEFCTEU6IHRhYmxlcy5jb21tZW50cy50YWJsZU5hbWUsXG4gICAgICBMSUtFU19UQUJMRTogdGFibGVzLmxpa2VzLnRhYmxlTmFtZSxcbiAgICAgIEZPTExPV1NfVEFCTEU6IHRhYmxlcy5mb2xsb3dzLnRhYmxlTmFtZSxcbiAgICAgIENIQU5ORUxTX1RBQkxFOiB0YWJsZXMuY2hhbm5lbHMudGFibGVOYW1lLFxuICAgICAgQ0hBTk5FTF9NRU1CRVJTX1RBQkxFOiB0YWJsZXMuY2hhbm5lbE1lbWJlcnMudGFibGVOYW1lLFxuICAgICAgUkVGTEVYRVNfVEFCTEU6IHRhYmxlcy5yZWZsZXhlcy50YWJsZU5hbWUsXG4gICAgICBVU0VSU19UQUJMRTogdGFibGVzLnVzZXJzLnRhYmxlTmFtZSxcbiAgICAgIFIyX1NFQ1JFVF9OQU1FOiByMlNlY3JldC5zZWNyZXROYW1lLFxuICAgICAgQVBQX0NPTkZJR19TRUNSRVRfTkFNRTogYXBwQ29uZmlnU2VjcmV0LnNlY3JldE5hbWUsXG4gICAgfTtcblxuICAgIC8vIEF1dGhvcml6ZXIgRnVuY3Rpb25cbiAgICBmdW5jdGlvbnMuYXV0aG9yaXplciA9IG5ldyBsYW1iZGEuRnVuY3Rpb24odGhpcywgJ0F1dGhvcml6ZXJGdW5jdGlvbicsIHtcbiAgICAgIGZ1bmN0aW9uTmFtZTogYCR7cHJvamVjdE5hbWV9LWF1dGhvcml6ZXItJHtlbnZpcm9ubWVudH1gLFxuICAgICAgcnVudGltZTogbGFtYmRhLlJ1bnRpbWUuTk9ERUpTXzIwX1gsXG4gICAgICBoYW5kbGVyOiAnaW5kZXguaGFuZGxlcicsXG4gICAgICBjb2RlOiBsYW1iZGEuQ29kZS5mcm9tQXNzZXQoJ3NyYy9hdXRob3JpemVyJyksXG4gICAgICB0aW1lb3V0OiBjZGsuRHVyYXRpb24uc2Vjb25kcygzMCksXG4gICAgICBtZW1vcnlTaXplOiAyNTYsXG4gICAgICBlbnZpcm9ubWVudDoge1xuICAgICAgICAuLi5jb21tb25FbnZpcm9ubWVudCxcbiAgICAgICAgVVNFUl9QT09MX0lEOiB1c2VyUG9vbC51c2VyUG9vbElkLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIC8vIEdyYW50IHBlcm1pc3Npb25zIHRvIGF1dGhvcml6ZXJcbiAgICB1c2VyUG9vbC5ncmFudChmdW5jdGlvbnMuYXV0aG9yaXplciwgJ2NvZ25pdG8taWRwOkdldFVzZXInKTtcblxuICAgIC8vIEF1dGggRnVuY3Rpb25cbiAgICBmdW5jdGlvbnMuYXV0aCA9IG5ldyBsYW1iZGEuRnVuY3Rpb24odGhpcywgJ0F1dGhGdW5jdGlvbicsIHtcbiAgICAgIGZ1bmN0aW9uTmFtZTogYCR7cHJvamVjdE5hbWV9LWF1dGgtJHtlbnZpcm9ubWVudH1gLFxuICAgICAgcnVudGltZTogbGFtYmRhLlJ1bnRpbWUuTk9ERUpTXzIwX1gsXG4gICAgICBoYW5kbGVyOiAnaW5kZXguaGFuZGxlcicsXG4gICAgICBjb2RlOiBsYW1iZGEuQ29kZS5mcm9tQXNzZXQoJ3NyYy9hdXRoJyksXG4gICAgICB0aW1lb3V0OiBjZGsuRHVyYXRpb24uc2Vjb25kcygzMCksXG4gICAgICBtZW1vcnlTaXplOiAyNTYsXG4gICAgICBlbnZpcm9ubWVudDogY29tbW9uRW52aXJvbm1lbnQsXG4gICAgfSk7XG5cbiAgICAvLyBHcmFudCBwZXJtaXNzaW9ucyB0byBhdXRoIGZ1bmN0aW9uXG4gICAgdGFibGVzLnVzZXJzLmdyYW50UmVhZFdyaXRlRGF0YShmdW5jdGlvbnMuYXV0aCk7XG4gICAgdXNlclBvb2wuZ3JhbnQoZnVuY3Rpb25zLmF1dGgsICdjb2duaXRvLWlkcDoqJyk7XG5cbiAgICAvLyBQb3N0cyBGdW5jdGlvblxuICAgIGZ1bmN0aW9ucy5wb3N0cyA9IG5ldyBsYW1iZGEuRnVuY3Rpb24odGhpcywgJ1Bvc3RzRnVuY3Rpb24nLCB7XG4gICAgICBmdW5jdGlvbk5hbWU6IGAke3Byb2plY3ROYW1lfS1wb3N0cy0ke2Vudmlyb25tZW50fWAsXG4gICAgICBydW50aW1lOiBsYW1iZGEuUnVudGltZS5OT0RFSlNfMjBfWCxcbiAgICAgIGhhbmRsZXI6ICdpbmRleC5oYW5kbGVyJyxcbiAgICAgIGNvZGU6IGxhbWJkYS5Db2RlLmZyb21Bc3NldCgnc3JjL3Bvc3RzJyksXG4gICAgICB0aW1lb3V0OiBjZGsuRHVyYXRpb24uc2Vjb25kcygzMCksXG4gICAgICBtZW1vcnlTaXplOiAyNTYsXG4gICAgICBlbnZpcm9ubWVudDogY29tbW9uRW52aXJvbm1lbnQsXG4gICAgfSk7XG5cbiAgICAvLyBHcmFudCBwZXJtaXNzaW9ucyB0byBwb3N0cyBmdW5jdGlvblxuICAgIHRhYmxlcy5wb3N0cy5ncmFudFJlYWRXcml0ZURhdGEoZnVuY3Rpb25zLnBvc3RzKTtcbiAgICB0YWJsZXMubWVkaWEuZ3JhbnRSZWFkV3JpdGVEYXRhKGZ1bmN0aW9ucy5wb3N0cyk7XG4gICAgdGFibGVzLmNvbW1lbnRzLmdyYW50UmVhZFdyaXRlRGF0YShmdW5jdGlvbnMucG9zdHMpO1xuICAgIHRhYmxlcy5saWtlcy5ncmFudFJlYWRXcml0ZURhdGEoZnVuY3Rpb25zLnBvc3RzKTtcbiAgICB0YWJsZXMucmVmbGV4ZXMuZ3JhbnRSZWFkV3JpdGVEYXRhKGZ1bmN0aW9ucy5wb3N0cyk7XG4gICAgdGFibGVzLnVzZXJzLmdyYW50UmVhZFdyaXRlRGF0YShmdW5jdGlvbnMucG9zdHMpO1xuXG4gICAgLy8gTWVkaWEgRnVuY3Rpb25cbiAgICBmdW5jdGlvbnMubWVkaWEgPSBuZXcgbGFtYmRhLkZ1bmN0aW9uKHRoaXMsICdNZWRpYUZ1bmN0aW9uJywge1xuICAgICAgZnVuY3Rpb25OYW1lOiBgJHtwcm9qZWN0TmFtZX0tbWVkaWEtJHtlbnZpcm9ubWVudH1gLFxuICAgICAgcnVudGltZTogbGFtYmRhLlJ1bnRpbWUuTk9ERUpTXzIwX1gsXG4gICAgICBoYW5kbGVyOiAnaW5kZXguaGFuZGxlcicsXG4gICAgICBjb2RlOiBsYW1iZGEuQ29kZS5mcm9tQXNzZXQoJ3NyYy9tZWRpYScpLFxuICAgICAgdGltZW91dDogY2RrLkR1cmF0aW9uLnNlY29uZHMoMzApLFxuICAgICAgbWVtb3J5U2l6ZTogMjU2LFxuICAgICAgZW52aXJvbm1lbnQ6IGNvbW1vbkVudmlyb25tZW50LFxuICAgIH0pO1xuXG4gICAgLy8gR3JhbnQgcGVybWlzc2lvbnMgdG8gbWVkaWEgZnVuY3Rpb25cbiAgICB0YWJsZXMubWVkaWEuZ3JhbnRSZWFkV3JpdGVEYXRhKGZ1bmN0aW9ucy5tZWRpYSk7XG4gICAgcjJTZWNyZXQuZ3JhbnRSZWFkKGZ1bmN0aW9ucy5tZWRpYSk7XG4gICAgYXBwQ29uZmlnU2VjcmV0LmdyYW50UmVhZChmdW5jdGlvbnMubWVkaWEpO1xuXG4gICAgLy8gVXNlcnMgRnVuY3Rpb25cbiAgICBmdW5jdGlvbnMudXNlcnMgPSBuZXcgbGFtYmRhLkZ1bmN0aW9uKHRoaXMsICdVc2Vyc0Z1bmN0aW9uJywge1xuICAgICAgZnVuY3Rpb25OYW1lOiBgJHtwcm9qZWN0TmFtZX0tdXNlcnMtJHtlbnZpcm9ubWVudH1gLFxuICAgICAgcnVudGltZTogbGFtYmRhLlJ1bnRpbWUuTk9ERUpTXzIwX1gsXG4gICAgICBoYW5kbGVyOiAnaW5kZXguaGFuZGxlcicsXG4gICAgICBjb2RlOiBsYW1iZGEuQ29kZS5mcm9tQXNzZXQoJ3NyYy91c2VycycpLFxuICAgICAgdGltZW91dDogY2RrLkR1cmF0aW9uLnNlY29uZHMoMzApLFxuICAgICAgbWVtb3J5U2l6ZTogMjU2LFxuICAgICAgZW52aXJvbm1lbnQ6IGNvbW1vbkVudmlyb25tZW50LFxuICAgIH0pO1xuXG4gICAgLy8gR3JhbnQgcGVybWlzc2lvbnMgdG8gdXNlcnMgZnVuY3Rpb25cbiAgICB0YWJsZXMudXNlcnMuZ3JhbnRSZWFkV3JpdGVEYXRhKGZ1bmN0aW9ucy51c2Vycyk7XG4gICAgdGFibGVzLnVzZXJQcm9maWxlcy5ncmFudFJlYWRXcml0ZURhdGEoZnVuY3Rpb25zLnVzZXJzKTtcbiAgICB0YWJsZXMuZm9sbG93cy5ncmFudFJlYWRXcml0ZURhdGEoZnVuY3Rpb25zLnVzZXJzKTtcbiAgICB0YWJsZXMucG9zdHMuZ3JhbnRSZWFkV3JpdGVEYXRhKGZ1bmN0aW9ucy51c2Vycyk7XG4gICAgdGFibGVzLmxpa2VzLmdyYW50UmVhZFdyaXRlRGF0YShmdW5jdGlvbnMudXNlcnMpO1xuXG4gICAgLy8gSGVhbHRoIEZ1bmN0aW9uXG4gICAgZnVuY3Rpb25zLmhlYWx0aCA9IG5ldyBsYW1iZGEuRnVuY3Rpb24odGhpcywgJ0hlYWx0aEZ1bmN0aW9uJywge1xuICAgICAgZnVuY3Rpb25OYW1lOiBgJHtwcm9qZWN0TmFtZX0taGVhbHRoLSR7ZW52aXJvbm1lbnR9YCxcbiAgICAgIHJ1bnRpbWU6IGxhbWJkYS5SdW50aW1lLk5PREVKU18yMF9YLFxuICAgICAgaGFuZGxlcjogJ2luZGV4LmhhbmRsZXInLFxuICAgICAgY29kZTogbGFtYmRhLkNvZGUuZnJvbUFzc2V0KCdzcmMvaGVhbHRoJyksXG4gICAgICB0aW1lb3V0OiBjZGsuRHVyYXRpb24uc2Vjb25kcygzMCksXG4gICAgICBtZW1vcnlTaXplOiAyNTYsXG4gICAgICBlbnZpcm9ubWVudDogY29tbW9uRW52aXJvbm1lbnQsXG4gICAgfSk7XG5cbiAgICAvLyBHcmFudCBwZXJtaXNzaW9ucyB0byBoZWFsdGggZnVuY3Rpb25cbiAgICB0YWJsZXMudXNlcnMuZ3JhbnRSZWFkRGF0YShmdW5jdGlvbnMuaGVhbHRoKTtcblxuICAgIC8vIFJlZmxleGVzIEZ1bmN0aW9uXG4gICAgZnVuY3Rpb25zLnJlZmxleGVzID0gbmV3IGxhbWJkYS5GdW5jdGlvbih0aGlzLCAnUmVmbGV4ZXNGdW5jdGlvbicsIHtcbiAgICAgIGZ1bmN0aW9uTmFtZTogYCR7cHJvamVjdE5hbWV9LXJlZmxleGVzLSR7ZW52aXJvbm1lbnR9YCxcbiAgICAgIHJ1bnRpbWU6IGxhbWJkYS5SdW50aW1lLk5PREVKU18yMF9YLFxuICAgICAgaGFuZGxlcjogJ2luZGV4LmhhbmRsZXInLFxuICAgICAgY29kZTogbGFtYmRhLkNvZGUuZnJvbUFzc2V0KCdzcmMvcmVmbGV4ZXMnKSxcbiAgICAgIHRpbWVvdXQ6IGNkay5EdXJhdGlvbi5zZWNvbmRzKDMwKSxcbiAgICAgIG1lbW9yeVNpemU6IDI1NixcbiAgICAgIGVudmlyb25tZW50OiBjb21tb25FbnZpcm9ubWVudCxcbiAgICB9KTtcblxuICAgIC8vIEdyYW50IHBlcm1pc3Npb25zIHRvIHJlZmxleGVzIGZ1bmN0aW9uXG4gICAgdGFibGVzLnJlZmxleGVzLmdyYW50UmVhZFdyaXRlRGF0YShmdW5jdGlvbnMucmVmbGV4ZXMpO1xuICAgIHRhYmxlcy5wb3N0cy5ncmFudFJlYWRXcml0ZURhdGEoZnVuY3Rpb25zLnJlZmxleGVzKTtcbiAgICB0YWJsZXMudXNlcnMuZ3JhbnRSZWFkV3JpdGVEYXRhKGZ1bmN0aW9ucy5yZWZsZXhlcyk7XG4gICAgdGFibGVzLm1lZGlhLmdyYW50UmVhZFdyaXRlRGF0YShmdW5jdGlvbnMucmVmbGV4ZXMpO1xuXG4gICAgLy8gQ2hhbm5lbHMgRnVuY3Rpb25cbiAgICBmdW5jdGlvbnMuY2hhbm5lbHMgPSBuZXcgbGFtYmRhLkZ1bmN0aW9uKHRoaXMsICdDaGFubmVsc0Z1bmN0aW9uJywge1xuICAgICAgZnVuY3Rpb25OYW1lOiBgJHtwcm9qZWN0TmFtZX0tY2hhbm5lbHMtJHtlbnZpcm9ubWVudH1gLFxuICAgICAgcnVudGltZTogbGFtYmRhLlJ1bnRpbWUuTk9ERUpTXzIwX1gsXG4gICAgICBoYW5kbGVyOiAnaW5kZXguaGFuZGxlcicsXG4gICAgICBjb2RlOiBsYW1iZGEuQ29kZS5mcm9tQXNzZXQoJ3NyYy9jaGFubmVscycpLFxuICAgICAgdGltZW91dDogY2RrLkR1cmF0aW9uLnNlY29uZHMoMzApLFxuICAgICAgbWVtb3J5U2l6ZTogMjU2LFxuICAgICAgZW52aXJvbm1lbnQ6IGNvbW1vbkVudmlyb25tZW50LFxuICAgIH0pO1xuXG4gICAgLy8gR3JhbnQgcGVybWlzc2lvbnMgdG8gY2hhbm5lbHMgZnVuY3Rpb25cbiAgICB0YWJsZXMuY2hhbm5lbHMuZ3JhbnRSZWFkV3JpdGVEYXRhKGZ1bmN0aW9ucy5jaGFubmVscyk7XG4gICAgdGFibGVzLmNoYW5uZWxNZW1iZXJzLmdyYW50UmVhZFdyaXRlRGF0YShmdW5jdGlvbnMuY2hhbm5lbHMpO1xuICAgIHRhYmxlcy51c2Vycy5ncmFudFJlYWRXcml0ZURhdGEoZnVuY3Rpb25zLmNoYW5uZWxzKTtcbiAgICB0YWJsZXMucG9zdHMuZ3JhbnRSZWFkV3JpdGVEYXRhKGZ1bmN0aW9ucy5jaGFubmVscyk7XG4gICAgdGFibGVzLm1lZGlhLmdyYW50UmVhZFdyaXRlRGF0YShmdW5jdGlvbnMuY2hhbm5lbHMpO1xuICAgIHIyU2VjcmV0LmdyYW50UmVhZChmdW5jdGlvbnMuY2hhbm5lbHMpO1xuICAgIGFwcENvbmZpZ1NlY3JldC5ncmFudFJlYWQoZnVuY3Rpb25zLmNoYW5uZWxzKTtcblxuICAgIHJldHVybiBmdW5jdGlvbnM7XG4gIH1cblxuICBwcml2YXRlIGNyZWF0ZUFwaUdhdGV3YXkoXG4gICAgcHJvamVjdE5hbWU6IHN0cmluZyxcbiAgICBlbnZpcm9ubWVudDogc3RyaW5nLFxuICAgIGZ1bmN0aW9uczogeyBba2V5OiBzdHJpbmddOiBsYW1iZGEuRnVuY3Rpb24gfSxcbiAgICB1c2VyUG9vbDogY29nbml0by5Vc2VyUG9vbFxuICApOiBhcGlnYXRld2F5LlJlc3RBcGkge1xuICAgIC8vIENyZWF0ZSBBUEkgR2F0ZXdheVxuICAgIGNvbnN0IGFwaSA9IG5ldyBhcGlnYXRld2F5LlJlc3RBcGkodGhpcywgJ0dhbWVGbGV4QXBpJywge1xuICAgICAgcmVzdEFwaU5hbWU6IGAke3Byb2plY3ROYW1lfS1hcGktJHtlbnZpcm9ubWVudH1gLFxuICAgICAgZGVzY3JpcHRpb246IGBHYW1lRmxleCBBUEkgZm9yICR7ZW52aXJvbm1lbnR9IGVudmlyb25tZW50YCxcbiAgICAgIGRlZmF1bHRDb3JzUHJlZmxpZ2h0T3B0aW9uczoge1xuICAgICAgICBhbGxvd09yaWdpbnM6IGFwaWdhdGV3YXkuQ29ycy5BTExfT1JJR0lOUyxcbiAgICAgICAgYWxsb3dNZXRob2RzOiBhcGlnYXRld2F5LkNvcnMuQUxMX01FVEhPRFMsXG4gICAgICAgIGFsbG93SGVhZGVyczogW1xuICAgICAgICAgICdDb250ZW50LVR5cGUnLFxuICAgICAgICAgICdYLUFtei1EYXRlJyxcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbicsXG4gICAgICAgICAgJ1gtQXBpLUtleScsXG4gICAgICAgICAgJ1gtQW16LVNlY3VyaXR5LVRva2VuJyxcbiAgICAgICAgICAnWC1BbXotVXNlci1BZ2VudCcsXG4gICAgICAgIF0sXG4gICAgICB9LFxuICAgICAgZGVwbG95T3B0aW9uczoge1xuICAgICAgICBzdGFnZU5hbWU6ICd2MScsXG4gICAgICAgIHRocm90dGxpbmdSYXRlTGltaXQ6IDEwMDAsXG4gICAgICAgIHRocm90dGxpbmdCdXJzdExpbWl0OiAyMDAwLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIC8vIENyZWF0ZSBhdXRob3JpemVyXG4gICAgY29uc3QgYXV0aG9yaXplciA9IG5ldyBhcGlnYXRld2F5LlRva2VuQXV0aG9yaXplcih0aGlzLCAnRGVmYXVsdEF1dGhvcml6ZXInLCB7XG4gICAgICBoYW5kbGVyOiBmdW5jdGlvbnMuYXV0aG9yaXplcixcbiAgICAgIGF1dGhvcml6ZXJOYW1lOiAnRGVmYXVsdEF1dGhvcml6ZXInLFxuICAgICAgcmVzdWx0c0NhY2hlVHRsOiBjZGsuRHVyYXRpb24ubWludXRlcyg1KSxcbiAgICB9KTtcblxuICAgIC8vIENyZWF0ZSBBUEkgcmVzb3VyY2VzIGFuZCBtZXRob2RzIGRpcmVjdGx5IHVuZGVyIHJvb3RcbiAgICAvLyBTaW5jZSB0aGUgc3RhZ2UgaXMgYWxyZWFkeSAndjEnLCB3ZSBkb24ndCBuZWVkIGEgbmVzdGVkIHYxIHJlc291cmNlXG5cbiAgICAvLyBIZWFsdGggZW5kcG9pbnQgKG5vIGF1dGggcmVxdWlyZWQpXG4gICAgY29uc3QgaGVhbHRoID0gYXBpLnJvb3QuYWRkUmVzb3VyY2UoJ2hlYWx0aCcpO1xuICAgIGhlYWx0aC5hZGRNZXRob2QoJ0dFVCcsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy5oZWFsdGgpKTtcblxuICAgIC8vIEF1dGggZW5kcG9pbnRzIChubyBhdXRoIHJlcXVpcmVkKVxuICAgIGNvbnN0IGF1dGggPSBhcGkucm9vdC5hZGRSZXNvdXJjZSgnYXV0aCcpO1xuICAgIGF1dGguYWRkTWV0aG9kKCdQT1NUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLmF1dGgpKTtcbiAgICBhdXRoLmFkZE1ldGhvZCgnR0VUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLmF1dGgpKTtcblxuICAgIC8vIFBvc3RzIGVuZHBvaW50cyAoYXV0aCByZXF1aXJlZClcbiAgICBjb25zdCBwb3N0cyA9IGFwaS5yb290LmFkZFJlc291cmNlKCdwb3N0cycpO1xuICAgIHBvc3RzLmFkZE1ldGhvZCgnR0VUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLnBvc3RzKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcbiAgICBwb3N0cy5hZGRNZXRob2QoJ1BPU1QnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMucG9zdHMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuXG4gICAgLy8gRHJhZnQgcG9zdHMgZW5kcG9pbnRcbiAgICBjb25zdCBwb3N0c0RyYWZ0ID0gcG9zdHMuYWRkUmVzb3VyY2UoJ2RyYWZ0Jyk7XG4gICAgcG9zdHNEcmFmdC5hZGRNZXRob2QoJ1BPU1QnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMucG9zdHMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuXG4gICAgY29uc3QgcG9zdElkID0gcG9zdHMuYWRkUmVzb3VyY2UoJ3tpZH0nKTtcbiAgICBwb3N0SWQuYWRkTWV0aG9kKCdHRVQnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMucG9zdHMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuICAgIHBvc3RJZC5hZGRNZXRob2QoJ1BVVCcsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy5wb3N0cyksIHtcbiAgICAgIGF1dGhvcml6ZXIsXG4gICAgfSk7XG4gICAgcG9zdElkLmFkZE1ldGhvZCgnREVMRVRFJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLnBvc3RzKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcblxuICAgIC8vIE1lZGlhIGF0dGFjaG1lbnQgZW5kcG9pbnRcbiAgICBjb25zdCBwb3N0TWVkaWEgPSBwb3N0SWQuYWRkUmVzb3VyY2UoJ21lZGlhJyk7XG4gICAgcG9zdE1lZGlhLmFkZE1ldGhvZCgnUFVUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLnBvc3RzKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcblxuICAgIC8vIFB1Ymxpc2ggZW5kcG9pbnRcbiAgICBjb25zdCBwb3N0UHVibGlzaCA9IHBvc3RJZC5hZGRSZXNvdXJjZSgncHVibGlzaCcpO1xuICAgIHBvc3RQdWJsaXNoLmFkZE1ldGhvZCgnUFVUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLnBvc3RzKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcblxuICAgIC8vIExpa2UgZW5kcG9pbnRzXG4gICAgY29uc3QgcG9zdExpa2UgPSBwb3N0SWQuYWRkUmVzb3VyY2UoJ2xpa2UnKTtcbiAgICBwb3N0TGlrZS5hZGRNZXRob2QoJ1BPU1QnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMucG9zdHMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuICAgIHBvc3RMaWtlLmFkZE1ldGhvZCgnREVMRVRFJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLnBvc3RzKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcblxuICAgIC8vIENvbW1lbnRzIGVuZHBvaW50c1xuICAgIGNvbnN0IHBvc3RDb21tZW50cyA9IHBvc3RJZC5hZGRSZXNvdXJjZSgnY29tbWVudHMnKTtcbiAgICBwb3N0Q29tbWVudHMuYWRkTWV0aG9kKCdHRVQnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMucG9zdHMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuICAgIHBvc3RDb21tZW50cy5hZGRNZXRob2QoJ1BPU1QnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMucG9zdHMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuXG4gICAgLy8gVXNlcnMgZW5kcG9pbnRzIChhdXRoIHJlcXVpcmVkKVxuICAgIGNvbnN0IHVzZXJzID0gYXBpLnJvb3QuYWRkUmVzb3VyY2UoJ3VzZXJzJyk7XG4gICAgdXNlcnMuYWRkTWV0aG9kKCdHRVQnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMudXNlcnMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuICAgIHVzZXJzLmFkZE1ldGhvZCgnUE9TVCcsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy51c2VycyksIHtcbiAgICAgIGF1dGhvcml6ZXIsXG4gICAgfSk7XG4gICAgY29uc3QgdXNlcklkID0gdXNlcnMuYWRkUmVzb3VyY2UoJ3tpZH0nKTtcbiAgICB1c2VySWQuYWRkTWV0aG9kKCdHRVQnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMudXNlcnMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuICAgIHVzZXJJZC5hZGRNZXRob2QoJ1BVVCcsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy51c2VycyksIHtcbiAgICAgIGF1dGhvcml6ZXIsXG4gICAgfSk7XG5cbiAgICAvLyBVc2VyIHByb2ZpbGUgZW5kcG9pbnRzXG4gICAgY29uc3QgcHJvZmlsZSA9IHVzZXJzLmFkZFJlc291cmNlKCdwcm9maWxlJyk7XG4gICAgcHJvZmlsZS5hZGRNZXRob2QoJ0dFVCcsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy51c2VycyksIHtcbiAgICAgIGF1dGhvcml6ZXIsXG4gICAgfSk7XG4gICAgcHJvZmlsZS5hZGRNZXRob2QoJ1BVVCcsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy51c2VycyksIHtcbiAgICAgIGF1dGhvcml6ZXIsXG4gICAgfSk7XG5cbiAgICAvLyBVc2VyIGxpa2VkIHBvc3RzIGVuZHBvaW50XG4gICAgY29uc3QgbGlrZWRQb3N0cyA9IHByb2ZpbGUuYWRkUmVzb3VyY2UoJ2xpa2VkLXBvc3RzJyk7XG4gICAgbGlrZWRQb3N0cy5hZGRNZXRob2QoJ0dFVCcsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy51c2VycyksIHtcbiAgICAgIGF1dGhvcml6ZXIsXG4gICAgfSk7XG5cbiAgICAvLyBNZWRpYSBlbmRwb2ludHMgKGF1dGggcmVxdWlyZWQpXG4gICAgY29uc3QgbWVkaWEgPSBhcGkucm9vdC5hZGRSZXNvdXJjZSgnbWVkaWEnKTtcbiAgICBtZWRpYS5hZGRNZXRob2QoJ0dFVCcsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy5tZWRpYSksIHtcbiAgICAgIGF1dGhvcml6ZXIsXG4gICAgfSk7XG4gICAgbWVkaWEuYWRkTWV0aG9kKCdQT1NUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLm1lZGlhKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcblxuICAgIC8vIE1lZGlhIHVwbG9hZCBlbmRwb2ludFxuICAgIGNvbnN0IG1lZGlhVXBsb2FkID0gbWVkaWEuYWRkUmVzb3VyY2UoJ3VwbG9hZCcpO1xuICAgIG1lZGlhVXBsb2FkLmFkZE1ldGhvZCgnUE9TVCcsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy5tZWRpYSksIHtcbiAgICAgIGF1dGhvcml6ZXIsXG4gICAgfSk7XG5cbiAgICBjb25zdCBtZWRpYUlkID0gbWVkaWEuYWRkUmVzb3VyY2UoJ3tpZH0nKTtcbiAgICBtZWRpYUlkLmFkZE1ldGhvZCgnR0VUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLm1lZGlhKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcbiAgICBtZWRpYUlkLmFkZE1ldGhvZCgnUFVUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLm1lZGlhKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcbiAgICBtZWRpYUlkLmFkZE1ldGhvZCgnREVMRVRFJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLm1lZGlhKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcblxuICAgIC8vIFJlZmxleGVzIGVuZHBvaW50cyAoYXV0aCByZXF1aXJlZClcbiAgICBjb25zdCByZWZsZXhlcyA9IGFwaS5yb290LmFkZFJlc291cmNlKCdyZWZsZXhlcycpO1xuICAgIHJlZmxleGVzLmFkZE1ldGhvZCgnR0VUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLnJlZmxleGVzKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcbiAgICByZWZsZXhlcy5hZGRNZXRob2QoJ1BPU1QnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMucmVmbGV4ZXMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuICAgIGNvbnN0IHJlZmxleElkID0gcmVmbGV4ZXMuYWRkUmVzb3VyY2UoJ3tpZH0nKTtcbiAgICByZWZsZXhJZC5hZGRNZXRob2QoJ0dFVCcsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy5yZWZsZXhlcyksIHtcbiAgICAgIGF1dGhvcml6ZXIsXG4gICAgfSk7XG4gICAgcmVmbGV4SWQuYWRkTWV0aG9kKCdQVVQnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMucmVmbGV4ZXMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuICAgIHJlZmxleElkLmFkZE1ldGhvZCgnREVMRVRFJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLnJlZmxleGVzKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcblxuICAgIC8vIENoYW5uZWxzIGVuZHBvaW50cyAoYXV0aCByZXF1aXJlZClcbiAgICBjb25zdCBjaGFubmVscyA9IGFwaS5yb290LmFkZFJlc291cmNlKCdjaGFubmVscycpO1xuICAgIGNoYW5uZWxzLmFkZE1ldGhvZCgnR0VUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLmNoYW5uZWxzKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcbiAgICBjaGFubmVscy5hZGRNZXRob2QoJ1BPU1QnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMuY2hhbm5lbHMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuICAgIGNvbnN0IGNoYW5uZWxJZCA9IGNoYW5uZWxzLmFkZFJlc291cmNlKCd7aWR9Jyk7XG4gICAgY2hhbm5lbElkLmFkZE1ldGhvZCgnR0VUJywgbmV3IGFwaWdhdGV3YXkuTGFtYmRhSW50ZWdyYXRpb24oZnVuY3Rpb25zLmNoYW5uZWxzKSwge1xuICAgICAgYXV0aG9yaXplcixcbiAgICB9KTtcbiAgICBjaGFubmVsSWQuYWRkTWV0aG9kKCdQVVQnLCBuZXcgYXBpZ2F0ZXdheS5MYW1iZGFJbnRlZ3JhdGlvbihmdW5jdGlvbnMuY2hhbm5lbHMpLCB7XG4gICAgICBhdXRob3JpemVyLFxuICAgIH0pO1xuICAgIGNoYW5uZWxJZC5hZGRNZXRob2QoJ0RFTEVURScsIG5ldyBhcGlnYXRld2F5LkxhbWJkYUludGVncmF0aW9uKGZ1bmN0aW9ucy5jaGFubmVscyksIHtcbiAgICAgIGF1dGhvcml6ZXIsXG4gICAgfSk7XG5cbiAgICByZXR1cm4gYXBpO1xuICB9XG5cbiAgcHJpdmF0ZSBjcmVhdGVDdXN0b21Eb21haW4oZG9tYWluTmFtZTogc3RyaW5nLCBjZXJ0aWZpY2F0ZUFybjogc3RyaW5nKTogdm9pZCB7XG4gICAgLy8gQ3VzdG9tIGRvbWFpbiBpbXBsZW1lbnRhdGlvbiB3b3VsZCBnbyBoZXJlXG4gICAgLy8gVGhpcyBpcyBhIHBsYWNlaG9sZGVyIGZvciBmdXR1cmUgaW1wbGVtZW50YXRpb25cbiAgICBjb25zb2xlLmxvZyhgQ3VzdG9tIGRvbWFpbiAke2RvbWFpbk5hbWV9IHdpdGggY2VydGlmaWNhdGUgJHtjZXJ0aWZpY2F0ZUFybn0gd291bGQgYmUgY29uZmlndXJlZCBoZXJlYCk7XG4gIH1cblxuICBwcml2YXRlIGNyZWF0ZU91dHB1dHMoXG4gICAgZW52aXJvbm1lbnQ6IHN0cmluZyxcbiAgICBkb21haW5OYW1lPzogc3RyaW5nLFxuICAgIHIyU2VjcmV0Pzogc2VjcmV0c21hbmFnZXIuSVNlY3JldCxcbiAgICBhcHBDb25maWdTZWNyZXQ/OiBzZWNyZXRzbWFuYWdlci5JU2VjcmV0XG4gICk6IHZvaWQge1xuICAgIC8vIEFQSSBHYXRld2F5IFVSTFxuICAgIG5ldyBjZGsuQ2ZuT3V0cHV0KHRoaXMsICdBcGlHYXRld2F5VXJsJywge1xuICAgICAgdmFsdWU6IHRoaXMuYXBpLnVybCxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQVBJIEdhdGV3YXkgVVJMJyxcbiAgICAgIGV4cG9ydE5hbWU6IGAke3RoaXMuc3RhY2tOYW1lfS1BcGlHYXRld2F5VXJsYCxcbiAgICB9KTtcblxuICAgIC8vIFVzZXIgUG9vbCBJRFxuICAgIG5ldyBjZGsuQ2ZuT3V0cHV0KHRoaXMsICdVc2VyUG9vbElkJywge1xuICAgICAgdmFsdWU6IHRoaXMudXNlclBvb2wudXNlclBvb2xJZCxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ29nbml0byBVc2VyIFBvb2wgSUQnLFxuICAgICAgZXhwb3J0TmFtZTogYCR7dGhpcy5zdGFja05hbWV9LVVzZXJQb29sSWRgLFxuICAgIH0pO1xuXG4gICAgLy8gVXNlciBQb29sIENsaWVudCBJRFxuICAgIG5ldyBjZGsuQ2ZuT3V0cHV0KHRoaXMsICdVc2VyUG9vbENsaWVudElkJywge1xuICAgICAgdmFsdWU6IHRoaXMudXNlclBvb2xDbGllbnQudXNlclBvb2xDbGllbnRJZCxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ29nbml0byBVc2VyIFBvb2wgQ2xpZW50IElEJyxcbiAgICAgIGV4cG9ydE5hbWU6IGAke3RoaXMuc3RhY2tOYW1lfS1Vc2VyUG9vbENsaWVudElkYCxcbiAgICB9KTtcblxuICAgIC8vIEN1c3RvbSBEb21haW4gVVJMIChpZiBjb25maWd1cmVkKVxuICAgIGlmIChkb21haW5OYW1lKSB7XG4gICAgICBuZXcgY2RrLkNmbk91dHB1dCh0aGlzLCAnQ3VzdG9tRG9tYWluVXJsJywge1xuICAgICAgICB2YWx1ZTogYGh0dHBzOi8vJHtkb21haW5OYW1lfWAsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQ3VzdG9tIERvbWFpbiBVUkwnLFxuICAgICAgICBleHBvcnROYW1lOiBgJHt0aGlzLnN0YWNrTmFtZX0tQ3VzdG9tRG9tYWluVXJsYCxcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIFIyIFNlY3JldCBBUk5cbiAgICBpZiAocjJTZWNyZXQpIHtcbiAgICAgIG5ldyBjZGsuQ2ZuT3V0cHV0KHRoaXMsICdSMlNlY3JldEFybicsIHtcbiAgICAgICAgdmFsdWU6IHIyU2VjcmV0LnNlY3JldEFybixcbiAgICAgICAgZGVzY3JpcHRpb246ICdSMiBDb25maWd1cmF0aW9uIFNlY3JldCBBUk4nLFxuICAgICAgICBleHBvcnROYW1lOiBgJHt0aGlzLnN0YWNrTmFtZX0tUjJTZWNyZXRBcm5gLFxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gQXBwIENvbmZpZyBTZWNyZXQgQVJOXG4gICAgaWYgKGFwcENvbmZpZ1NlY3JldCkge1xuICAgICAgbmV3IGNkay5DZm5PdXRwdXQodGhpcywgJ0FwcENvbmZpZ1NlY3JldEFybicsIHtcbiAgICAgICAgdmFsdWU6IGFwcENvbmZpZ1NlY3JldC5zZWNyZXRBcm4sXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQXBwIENvbmZpZ3VyYXRpb24gU2VjcmV0IEFSTicsXG4gICAgICAgIGV4cG9ydE5hbWU6IGAke3RoaXMuc3RhY2tOYW1lfS1BcHBDb25maWdTZWNyZXRBcm5gLFxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gRHluYW1vREIgVGFibGUgTmFtZXNcbiAgICBPYmplY3QuZW50cmllcyh0aGlzLnRhYmxlcykuZm9yRWFjaCgoW25hbWUsIHRhYmxlXSkgPT4ge1xuICAgICAgbmV3IGNkay5DZm5PdXRwdXQodGhpcywgYCR7bmFtZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIG5hbWUuc2xpY2UoMSl9VGFibGVOYW1lYCwge1xuICAgICAgICB2YWx1ZTogdGFibGUudGFibGVOYW1lLFxuICAgICAgICBkZXNjcmlwdGlvbjogYCR7bmFtZX0gRHluYW1vREIgVGFibGUgTmFtZWAsXG4gICAgICAgIGV4cG9ydE5hbWU6IGAke3RoaXMuc3RhY2tOYW1lfS0ke25hbWUuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBuYW1lLnNsaWNlKDEpfVRhYmxlTmFtZWAsXG4gICAgICB9KTtcbiAgICB9KTtcbiAgfVxufVxuIl19