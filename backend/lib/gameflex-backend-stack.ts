import * as cdk from 'aws-cdk-lib';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as nodejs from 'aws-cdk-lib/aws-lambda-nodejs';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export interface GameFlexBackendStackProps extends cdk.StackProps {
  environment: string;
  projectName: string;
  domainName?: string;
  certificateArn?: string;
  r2BucketName: string;
  r2PublicUrl: string;
}

export class GameFlexBackendStack extends cdk.Stack {
  public readonly userPool: cognito.UserPool;
  public readonly userPoolClient: cognito.UserPoolClient;
  public readonly api: apigateway.RestApi;
  public readonly tables: { [key: string]: dynamodb.Table };

  constructor(scope: Construct, id: string, props: GameFlexBackendStackProps) {
    super(scope, id, props);

    const { environment, projectName, domainName, certificateArn, r2BucketName, r2PublicUrl } = props;

    // Conditions
    const isProduction = environment === 'production';
    const isStaging = environment === 'staging';
    const isProductionOrStaging = isProduction || isStaging;
    const hasCustomDomain = !!domainName;

    // AWS Secrets Manager for R2 Configuration
    // Use fromSecretNameV2 to reference existing secrets or create new ones
    const r2SecretName = `${projectName}-r2-config-${environment}`;
    const r2Secret = secretsmanager.Secret.fromSecretNameV2(this, 'R2Secret', r2SecretName);

    // AWS Secrets Manager for General Configuration
    const appConfigSecretName = `${projectName}-app-config-${environment}`;
    const appConfigSecret = secretsmanager.Secret.fromSecretNameV2(this, 'AppConfigSecret', appConfigSecretName);

    // Note: Cannot apply removal policies to imported secrets
    // Secrets are managed externally and will persist regardless

    // Cognito User Pool
    this.userPool = new cognito.UserPool(this, 'UserPool', {
      userPoolName: `${projectName}-users-${environment}`,
      autoVerify: { email: true },
      signInAliases: { email: true },
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireDigits: true,
        requireSymbols: false,
      },
      deletionProtection: isProductionOrStaging,
      removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
    });

    // Cognito User Pool Client
    this.userPoolClient = new cognito.UserPoolClient(this, 'UserPoolClient', {
      userPool: this.userPool,
      userPoolClientName: `${projectName}-client-${environment}`,
      generateSecret: false,
      authFlows: {
        adminUserPassword: true,
        userPassword: true,
        userSrp: true,
      },
      refreshTokenValidity: cdk.Duration.days(30),
      accessTokenValidity: cdk.Duration.minutes(60),
      idTokenValidity: cdk.Duration.minutes(60),
    });

    // DynamoDB Tables
    this.tables = this.createDynamoDBTables(projectName, environment, isProductionOrStaging);

    // Lambda Functions
    const lambdaFunctions = this.createLambdaFunctions(
      projectName,
      environment,
      this.userPool,
      this.userPoolClient,
      this.tables,
      r2Secret,
      appConfigSecret
    );

    // API Gateway
    this.api = this.createApiGateway(projectName, environment, lambdaFunctions, this.userPool);

    // Custom Domain (if specified)
    if (hasCustomDomain && certificateArn) {
      this.createCustomDomain(domainName!, certificateArn);
    }

    // Outputs
    this.createOutputs(environment, domainName, r2Secret, appConfigSecret);
  }

  private createDynamoDBTables(
    projectName: string,
    environment: string,
    isProductionOrStaging: boolean
  ): { [key: string]: dynamodb.Table } {
    const tableConfig = {
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      deletionProtection: isProductionOrStaging,
      pointInTimeRecovery: isProductionOrStaging,
      removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
    };

    const tables: { [key: string]: dynamodb.Table } = {};

    // Posts Table
    tables.posts = new dynamodb.Table(this, 'PostsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Posts`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });

    // Media Table
    tables.media = new dynamodb.Table(this, 'MediaTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Media`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });

    // User Profiles Table
    tables.userProfiles = new dynamodb.Table(this, 'UserProfilesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-UserProfiles`,
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Comments Table
    tables.comments = new dynamodb.Table(this, 'CommentsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Comments`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.comments.addGlobalSecondaryIndex({
      indexName: 'postId-index',
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
    });

    // Likes Table
    tables.likes = new dynamodb.Table(this, 'LikesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Likes`,
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });
    tables.likes.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Follows Table
    tables.follows = new dynamodb.Table(this, 'FollowsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Follows`,
      partitionKey: { name: 'followerId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'followingId', type: dynamodb.AttributeType.STRING },
    });

    // Channels Table
    tables.channels = new dynamodb.Table(this, 'ChannelsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Channels`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.channels.addGlobalSecondaryIndex({
      indexName: 'ownerId-index',
      partitionKey: { name: 'ownerId', type: dynamodb.AttributeType.STRING },
    });

    // Channel Members Table
    tables.channelMembers = new dynamodb.Table(this, 'ChannelMembersTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-ChannelMembers`,
      partitionKey: { name: 'channelId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });
    tables.channelMembers.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Reflexes Table
    tables.reflexes = new dynamodb.Table(this, 'ReflexesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Reflexes`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.reflexes.addGlobalSecondaryIndex({
      indexName: 'postId-index',
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
    });
    tables.reflexes.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Users Table
    tables.users = new dynamodb.Table(this, 'UsersTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Users`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.users.addGlobalSecondaryIndex({
      indexName: 'EmailIndex',
      partitionKey: { name: 'email', type: dynamodb.AttributeType.STRING },
    });
    tables.users.addGlobalSecondaryIndex({
      indexName: 'UsernameIndex',
      partitionKey: { name: 'username', type: dynamodb.AttributeType.STRING },
    });
    tables.users.addGlobalSecondaryIndex({
      indexName: 'CognitoUserIdIndex',
      partitionKey: { name: 'cognitoUserId', type: dynamodb.AttributeType.STRING },
    });

    return tables;
  }

  private createLambdaFunctions(
    projectName: string,
    environment: string,
    userPool: cognito.UserPool,
    userPoolClient: cognito.UserPoolClient,
    tables: { [key: string]: dynamodb.Table },
    r2Secret: secretsmanager.ISecret,
    appConfigSecret: secretsmanager.ISecret
  ): { [key: string]: lambda.Function } {
    const functions: { [key: string]: lambda.Function } = {};

    // Common environment variables for all Lambda functions
    const commonEnvironment = {
      ENVIRONMENT: environment,
      PROJECT_NAME: projectName,
      USER_POOL_ID: userPool.userPoolId,
      USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      POSTS_TABLE: tables.posts.tableName,
      MEDIA_TABLE: tables.media.tableName,
      USER_PROFILES_TABLE: tables.userProfiles.tableName,
      COMMENTS_TABLE: tables.comments.tableName,
      LIKES_TABLE: tables.likes.tableName,
      FOLLOWS_TABLE: tables.follows.tableName,
      CHANNELS_TABLE: tables.channels.tableName,
      CHANNEL_MEMBERS_TABLE: tables.channelMembers.tableName,
      REFLEXES_TABLE: tables.reflexes.tableName,
      USERS_TABLE: tables.users.tableName,
      R2_SECRET_NAME: r2Secret.secretName,
      APP_CONFIG_SECRET_NAME: appConfigSecret.secretName,
    };

    // Authorizer Function
    functions.authorizer = new lambda.Function(this, 'AuthorizerFunction', {
      functionName: `${projectName}-authorizer-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/authorizer'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: {
        ...commonEnvironment,
        USER_POOL_ID: userPool.userPoolId,
      },
    });

    // Grant permissions to authorizer
    userPool.grant(functions.authorizer, 'cognito-idp:GetUser');

    // Auth Function
    functions.auth = new lambda.Function(this, 'AuthFunction', {
      functionName: `${projectName}-auth-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to auth function
    tables.users.grantReadWriteData(functions.auth);
    userPool.grant(functions.auth, 'cognito-idp:*');

    // Posts Function
    functions.posts = new lambda.Function(this, 'PostsFunction', {
      functionName: `${projectName}-posts-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/posts'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to posts function
    tables.posts.grantReadWriteData(functions.posts);
    tables.media.grantReadWriteData(functions.posts);
    tables.comments.grantReadWriteData(functions.posts);
    tables.likes.grantReadWriteData(functions.posts);
    tables.reflexes.grantReadWriteData(functions.posts);
    tables.users.grantReadWriteData(functions.posts);

    // Media Function
    functions.media = new lambda.Function(this, 'MediaFunction', {
      functionName: `${projectName}-media-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/media'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to media function
    tables.media.grantReadWriteData(functions.media);
    r2Secret.grantRead(functions.media);
    appConfigSecret.grantRead(functions.media);

    // Users Function
    functions.users = new lambda.Function(this, 'UsersFunction', {
      functionName: `${projectName}-users-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/users'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to users function
    tables.users.grantReadWriteData(functions.users);
    tables.userProfiles.grantReadWriteData(functions.users);
    tables.follows.grantReadWriteData(functions.users);
    tables.posts.grantReadWriteData(functions.users);
    tables.likes.grantReadWriteData(functions.users);

    // Health Function
    functions.health = new lambda.Function(this, 'HealthFunction', {
      functionName: `${projectName}-health-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/health'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to health function
    tables.users.grantReadData(functions.health);

    // Reflexes Function
    functions.reflexes = new lambda.Function(this, 'ReflexesFunction', {
      functionName: `${projectName}-reflexes-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/reflexes'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to reflexes function
    tables.reflexes.grantReadWriteData(functions.reflexes);
    tables.posts.grantReadWriteData(functions.reflexes);
    tables.users.grantReadWriteData(functions.reflexes);
    tables.media.grantReadWriteData(functions.reflexes);

    // Channels Function
    functions.channels = new lambda.Function(this, 'ChannelsFunction', {
      functionName: `${projectName}-channels-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/channels'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to channels function
    tables.channels.grantReadWriteData(functions.channels);
    tables.channelMembers.grantReadWriteData(functions.channels);
    tables.users.grantReadWriteData(functions.channels);
    tables.posts.grantReadWriteData(functions.channels);
    tables.media.grantReadWriteData(functions.channels);
    r2Secret.grantRead(functions.channels);
    appConfigSecret.grantRead(functions.channels);

    return functions;
  }

  private createApiGateway(
    projectName: string,
    environment: string,
    functions: { [key: string]: lambda.Function },
    userPool: cognito.UserPool
  ): apigateway.RestApi {
    // Create API Gateway
    const api = new apigateway.RestApi(this, 'GameFlexApi', {
      restApiName: `${projectName}-api-${environment}`,
      description: `GameFlex API for ${environment} environment`,
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: [
          'Content-Type',
          'X-Amz-Date',
          'Authorization',
          'X-Api-Key',
          'X-Amz-Security-Token',
          'X-Amz-User-Agent',
        ],
      },
      deployOptions: {
        stageName: 'v1',
        throttlingRateLimit: 1000,
        throttlingBurstLimit: 2000,
      },
    });

    // Create authorizer
    const authorizer = new apigateway.TokenAuthorizer(this, 'DefaultAuthorizer', {
      handler: functions.authorizer,
      authorizerName: 'DefaultAuthorizer',
      resultsCacheTtl: cdk.Duration.minutes(5),
    });

    // Create API resources and methods directly under root
    // Since the stage is already 'v1', we don't need a nested v1 resource

    // Health endpoint (no auth required)
    const health = api.root.addResource('health');
    health.addMethod('GET', new apigateway.LambdaIntegration(functions.health));

    // Auth endpoints (no auth required)
    const auth = api.root.addResource('auth');
    auth.addMethod('POST', new apigateway.LambdaIntegration(functions.auth));
    auth.addMethod('GET', new apigateway.LambdaIntegration(functions.auth));

    // Posts endpoints (auth required)
    const posts = api.root.addResource('posts');
    posts.addMethod('GET', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });
    posts.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });

    // Draft posts endpoint
    const postsDraft = posts.addResource('draft');
    postsDraft.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });

    const postId = posts.addResource('{id}');
    postId.addMethod('GET', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });
    postId.addMethod('PUT', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });
    postId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });

    // Media attachment endpoint
    const postMedia = postId.addResource('media');
    postMedia.addMethod('PUT', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });

    // Publish endpoint
    const postPublish = postId.addResource('publish');
    postPublish.addMethod('PUT', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });

    // Like endpoints
    const postLike = postId.addResource('like');
    postLike.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });
    postLike.addMethod('DELETE', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });

    // Comments endpoints
    const postComments = postId.addResource('comments');
    postComments.addMethod('GET', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });
    postComments.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
      authorizer,
    });

    // Users endpoints (auth required)
    const users = api.root.addResource('users');
    users.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
      authorizer,
    });
    users.addMethod('POST', new apigateway.LambdaIntegration(functions.users), {
      authorizer,
    });
    const userId = users.addResource('{id}');
    userId.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
      authorizer,
    });
    userId.addMethod('PUT', new apigateway.LambdaIntegration(functions.users), {
      authorizer,
    });

    // User profile endpoints
    const profile = users.addResource('profile');
    profile.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
      authorizer,
    });
    profile.addMethod('PUT', new apigateway.LambdaIntegration(functions.users), {
      authorizer,
    });

    // User liked posts endpoint
    const likedPosts = profile.addResource('liked-posts');
    likedPosts.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
      authorizer,
    });

    // Media endpoints (auth required)
    const media = api.root.addResource('media');
    media.addMethod('GET', new apigateway.LambdaIntegration(functions.media), {
      authorizer,
    });
    media.addMethod('POST', new apigateway.LambdaIntegration(functions.media), {
      authorizer,
    });

    // Media upload endpoint
    const mediaUpload = media.addResource('upload');
    mediaUpload.addMethod('POST', new apigateway.LambdaIntegration(functions.media), {
      authorizer,
    });

    const mediaId = media.addResource('{id}');
    mediaId.addMethod('GET', new apigateway.LambdaIntegration(functions.media), {
      authorizer,
    });
    mediaId.addMethod('PUT', new apigateway.LambdaIntegration(functions.media), {
      authorizer,
    });
    mediaId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.media), {
      authorizer,
    });

    // Reflexes endpoints (auth required)
    const reflexes = api.root.addResource('reflexes');
    reflexes.addMethod('GET', new apigateway.LambdaIntegration(functions.reflexes), {
      authorizer,
    });
    reflexes.addMethod('POST', new apigateway.LambdaIntegration(functions.reflexes), {
      authorizer,
    });
    const reflexId = reflexes.addResource('{id}');
    reflexId.addMethod('GET', new apigateway.LambdaIntegration(functions.reflexes), {
      authorizer,
    });
    reflexId.addMethod('PUT', new apigateway.LambdaIntegration(functions.reflexes), {
      authorizer,
    });
    reflexId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.reflexes), {
      authorizer,
    });

    // Channels endpoints (auth required)
    const channels = api.root.addResource('channels');
    channels.addMethod('GET', new apigateway.LambdaIntegration(functions.channels), {
      authorizer,
    });
    channels.addMethod('POST', new apigateway.LambdaIntegration(functions.channels), {
      authorizer,
    });
    const channelId = channels.addResource('{id}');
    channelId.addMethod('GET', new apigateway.LambdaIntegration(functions.channels), {
      authorizer,
    });
    channelId.addMethod('PUT', new apigateway.LambdaIntegration(functions.channels), {
      authorizer,
    });
    channelId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.channels), {
      authorizer,
    });

    return api;
  }

  private createCustomDomain(domainName: string, certificateArn: string): void {
    // Custom domain implementation would go here
    // This is a placeholder for future implementation
    console.log(`Custom domain ${domainName} with certificate ${certificateArn} would be configured here`);
  }

  private createOutputs(
    environment: string,
    domainName?: string,
    r2Secret?: secretsmanager.ISecret,
    appConfigSecret?: secretsmanager.ISecret
  ): void {
    // API Gateway URL
    new cdk.CfnOutput(this, 'ApiGatewayUrl', {
      value: this.api.url,
      description: 'API Gateway URL',
      exportName: `${this.stackName}-ApiGatewayUrl`,
    });

    // User Pool ID
    new cdk.CfnOutput(this, 'UserPoolId', {
      value: this.userPool.userPoolId,
      description: 'Cognito User Pool ID',
      exportName: `${this.stackName}-UserPoolId`,
    });

    // User Pool Client ID
    new cdk.CfnOutput(this, 'UserPoolClientId', {
      value: this.userPoolClient.userPoolClientId,
      description: 'Cognito User Pool Client ID',
      exportName: `${this.stackName}-UserPoolClientId`,
    });

    // Custom Domain URL (if configured)
    if (domainName) {
      new cdk.CfnOutput(this, 'CustomDomainUrl', {
        value: `https://${domainName}`,
        description: 'Custom Domain URL',
        exportName: `${this.stackName}-CustomDomainUrl`,
      });
    }

    // R2 Secret ARN
    if (r2Secret) {
      new cdk.CfnOutput(this, 'R2SecretArn', {
        value: r2Secret.secretArn,
        description: 'R2 Configuration Secret ARN',
        exportName: `${this.stackName}-R2SecretArn`,
      });
    }

    // App Config Secret ARN
    if (appConfigSecret) {
      new cdk.CfnOutput(this, 'AppConfigSecretArn', {
        value: appConfigSecret.secretArn,
        description: 'App Configuration Secret ARN',
        exportName: `${this.stackName}-AppConfigSecretArn`,
      });
    }

    // DynamoDB Table Names
    Object.entries(this.tables).forEach(([name, table]) => {
      new cdk.CfnOutput(this, `${name.charAt(0).toUpperCase() + name.slice(1)}TableName`, {
        value: table.tableName,
        description: `${name} DynamoDB Table Name`,
        exportName: `${this.stackName}-${name.charAt(0).toUpperCase() + name.slice(1)}TableName`,
      });
    });
  }
}
