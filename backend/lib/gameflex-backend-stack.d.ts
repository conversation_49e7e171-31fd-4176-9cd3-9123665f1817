import * as cdk from 'aws-cdk-lib';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import { Construct } from 'constructs';
export interface GameFlexBackendStackProps extends cdk.StackProps {
    environment: string;
    projectName: string;
    domainName?: string;
    certificateArn?: string;
    r2BucketName: string;
    r2PublicUrl: string;
}
export declare class GameFlexBackendStack extends cdk.Stack {
    readonly userPool: cognito.UserPool;
    readonly userPoolClient: cognito.UserPoolClient;
    readonly api: apigateway.RestApi;
    readonly tables: {
        [key: string]: dynamodb.Table;
    };
    constructor(scope: Construct, id: string, props: GameFlexBackendStackProps);
    private createDynamoDBTables;
    private createLambdaFunctions;
    private createApiGateway;
    private createCustomDomain;
    private createOutputs;
}
