"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const cdk = __importStar(require("aws-cdk-lib"));
const assertions_1 = require("aws-cdk-lib/assertions");
const gameflex_backend_stack_1 = require("../lib/gameflex-backend-stack");
describe('GameFlex Backend Stack', () => {
    let app;
    let stack;
    let template;
    beforeEach(() => {
        app = new cdk.App();
        stack = new gameflex_backend_stack_1.GameFlexBackendStack(app, 'TestStack', {
            environment: 'test',
            projectName: 'gameflex',
            r2BucketName: 'test-bucket',
            r2PublicUrl: 'https://test.example.com',
        });
        template = assertions_1.Template.fromStack(stack);
    });
    test('Creates Cognito User Pool', () => {
        template.hasResourceProperties('AWS::Cognito::UserPool', {
            UserPoolName: 'gameflex-users-test',
        });
    });
    test('Creates Cognito User Pool Client', () => {
        template.hasResourceProperties('AWS::Cognito::UserPoolClient', {
            ClientName: 'gameflex-client-test',
            GenerateSecret: false,
        });
    });
    test('Creates DynamoDB Tables', () => {
        // Check for Posts table
        template.hasResourceProperties('AWS::DynamoDB::Table', {
            TableName: 'gameflex-test-Posts',
            BillingMode: 'PAY_PER_REQUEST',
        });
        // Check for Users table
        template.hasResourceProperties('AWS::DynamoDB::Table', {
            TableName: 'gameflex-test-Users',
            BillingMode: 'PAY_PER_REQUEST',
        });
    });
    test('Creates Lambda Functions', () => {
        // Check for Auth function
        template.hasResourceProperties('AWS::Lambda::Function', {
            FunctionName: 'gameflex-auth-test',
            Runtime: 'nodejs20.x',
        });
        // Check for Posts function
        template.hasResourceProperties('AWS::Lambda::Function', {
            FunctionName: 'gameflex-posts-test',
            Runtime: 'nodejs20.x',
        });
    });
    test('Creates API Gateway', () => {
        template.hasResourceProperties('AWS::ApiGateway::RestApi', {
            Name: 'gameflex-api-test',
        });
    });
    test('References Secrets Manager Secrets', () => {
        // Since we're now importing existing secrets using fromSecretNameV2,
        // they won't appear as new resources in the CloudFormation template.
        // Instead, we verify that the stack synthesizes without errors,
        // which means the secret references are valid.
        // Check that the template contains Lambda functions that reference the secrets
        template.hasResourceProperties('AWS::Lambda::Function', {
            Environment: {
                Variables: {
                    R2_SECRET_NAME: 'gameflex-r2-config-test',
                    APP_CONFIG_SECRET_NAME: 'gameflex-app-config-test',
                }
            }
        });
    });
    test('Creates API Gateway Authorizer', () => {
        template.hasResourceProperties('AWS::ApiGateway::Authorizer', {
            Name: 'DefaultAuthorizer',
            Type: 'REQUEST',
        });
    });
});
//# sourceMappingURL=data:application/json;base64,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