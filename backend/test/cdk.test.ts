import * as cdk from 'aws-cdk-lib';
import { Template } from 'aws-cdk-lib/assertions';
import { GameFlexBackendStack } from '../lib/gameflex-backend-stack';

describe('GameFlex Backend Stack', () => {
    let app: cdk.App;
    let stack: GameFlexBackendStack;
    let template: Template;

    beforeEach(() => {
        app = new cdk.App();
        stack = new GameFlexBackendStack(app, 'TestStack', {
            environment: 'test',
            projectName: 'gameflex',
            r2BucketName: 'test-bucket',
            r2PublicUrl: 'https://test.example.com',
        });
        template = Template.fromStack(stack);
    });

    test('Creates Cognito User Pool', () => {
        template.hasResourceProperties('AWS::Cognito::UserPool', {
            UserPoolName: 'gameflex-users-test',
        });
    });

    test('Creates Cognito User Pool Client', () => {
        template.hasResourceProperties('AWS::Cognito::UserPoolClient', {
            ClientName: 'gameflex-client-test',
            GenerateSecret: false,
        });
    });

    test('Creates DynamoDB Tables', () => {
        // Check for Posts table
        template.hasResourceProperties('AWS::DynamoDB::Table', {
            TableName: 'gameflex-test-Posts',
            BillingMode: 'PAY_PER_REQUEST',
        });

        // Check for Users table
        template.hasResourceProperties('AWS::DynamoDB::Table', {
            TableName: 'gameflex-test-Users',
            BillingMode: 'PAY_PER_REQUEST',
        });
    });

    test('Creates Lambda Functions', () => {
        // Check for Auth function
        template.hasResourceProperties('AWS::Lambda::Function', {
            FunctionName: 'gameflex-auth-test',
            Runtime: 'nodejs20.x',
        });

        // Check for Posts function
        template.hasResourceProperties('AWS::Lambda::Function', {
            FunctionName: 'gameflex-posts-test',
            Runtime: 'nodejs20.x',
        });
    });

    test('Creates API Gateway', () => {
        template.hasResourceProperties('AWS::ApiGateway::RestApi', {
            Name: 'gameflex-api-test',
        });
    });

    test('References Secrets Manager Secrets', () => {
        // Since we're now importing existing secrets using fromSecretNameV2,
        // they won't appear as new resources in the CloudFormation template.
        // Instead, we verify that the stack synthesizes without errors,
        // which means the secret references are valid.

        // Check that the template contains Lambda functions that reference the secrets
        template.hasResourceProperties('AWS::Lambda::Function', {
            Environment: {
                Variables: {
                    R2_SECRET_NAME: 'gameflex-r2-config-test',
                    APP_CONFIG_SECRET_NAME: 'gameflex-app-config-test',
                }
            }
        });
    });

    test('Creates API Gateway Authorizer', () => {
        template.hasResourceProperties('AWS::ApiGateway::Authorizer', {
            Name: 'DefaultAuthorizer',
            Type: 'REQUEST',
        });
    });
});
